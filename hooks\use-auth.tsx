"use client"

import { useState, useEffect, create<PERSON>ontext, useContext, type ReactNode } from "react"
import { useAuth as useClerkAuth, useUser } from "@clerk/nextjs"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useToast } from "@/components/ui/use-toast"
import type { User } from "@/lib/types"
import { Id } from "@/convex/_generated/dataModel"

interface AuthContextType {
  userId: string | null
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  error: Error | null
  updateUserStatus: (status: "online" | "offline" | "away") => Promise<void>
  updateUserProfile: (data: {
    statusMessage?: string
    mood?: "happy" | "sad" | "excited" | "bored" | "busy" | "relaxed" | "tired" | "angry" | "sick" | "love"
  }) => Promise<void>
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: ReactNode }) {
  const { isLoaded: isClerkLoaded, userId: clerkId, isSignedIn } = useClerkAuth()
  const { user: clerkUser, isLoaded: isUserLoaded } = useUser()
  const { toast } = useToast()

  const [userId, setUserId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const storeUser = useMutation(api.users.storeUser)
  const updateStatus = useMutation(api.users.updateUserStatus)
  const updateUser = useMutation(api.users.updateUser)
  const existingUser = useQuery(api.users.getCurrentUser, isSignedIn ? undefined : "skip")

  // Update the useEffect in the AuthProvider component to prevent the login/logout loop

  // Replace the first useEffect with this implementation:
  useEffect(() => {
    let isMounted = true

    async function createOrGetUser() {
      if (!isClerkLoaded || !isUserLoaded) {
        setIsLoading(true)
        return
      }

      if (!isSignedIn || !clerkId) {
        setIsLoading(false)
        setUserId(null)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        // Only attempt to create a user if we don't already have a userId
        if (existingUser) {
          // console.log("Found existing user:", existingUser._id)

          // Only set userId if component is still mounted
          if (isMounted) {
            setUserId(existingUser._id)
          }

          // Only update status if we have a valid user ID and it's different from current
          if (existingUser._id && existingUser.status !== "online") {
            await updateStatus({
              userId: existingUser._id,
              status: "online",
            })
          }
        } else if (clerkUser) {
          // console.log("Creating new user for:", clerkUser.id)
          // Create user in Convex if they don't exist
          const newUserId = await storeUser({
            clerkId,
            name:
              clerkUser.firstName && clerkUser.lastName
                ? `${clerkUser.firstName} ${clerkUser.lastName}`
                : clerkUser.username || "New User",
            email: clerkUser.emailAddresses[0]?.emailAddress,
            avatar: clerkUser.imageUrl || "/placeholder.svg?height=200&width=200",
          })
          // console.log("Created new user with ID:", newUserId)

          // Only set userId if component is still mounted
          if (isMounted) {
            setUserId(newUserId)
          }
        }
      } catch (err) {
        // console.error("Error in createOrGetUser:", err)

        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Failed to authenticate"))
          setUserId(null)

          toast({
            title: "Authentication Error",
            description: "There was a problem connecting to the server. Please try again.",
            variant: "destructive",
          })
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    createOrGetUser()

    // Cleanup function
    return () => {
      isMounted = false

      // Only set offline status when the component is actually unmounting from the app
      // NOT on every re-render
      if (userId && window.navigator.onLine === false) {
        updateStatus({
          userId: userId as Id<"users">,
          status: "offline",
        }).catch(console.error)
      }
    }
  }, [clerkId, clerkUser, existingUser, isClerkLoaded, isUserLoaded, isSignedIn, storeUser, toast, updateStatus])

  // Also update the visibility change handler to prevent unnecessary status updates
  useEffect(() => {
    // Add a debounce mechanism to prevent rapid status changes
    let statusChangeTimeout: NodeJS.Timeout | null = null

    const handleVisibilityChange = () => {
      if (!userId) return

      // Clear any pending status change
      if (statusChangeTimeout) {
        clearTimeout(statusChangeTimeout)
      }

      // Set a timeout to debounce status changes
      statusChangeTimeout = setTimeout(() => {
        if (document.visibilityState === "visible") {
          updateStatus({
            userId: userId as Id<"users">,
            status: "online",
          }).catch(console.error)
        } else {
          // Only set to away if the tab is hidden for more than 5 seconds
          updateStatus({
            userId: userId as Id<"users">,
            status: "away",
          }).catch(console.error)
        }
      }, 5000) // 5 second debounce
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
      if (statusChangeTimeout) {
        clearTimeout(statusChangeTimeout)
      }
    }
  }, [userId, updateStatus])

  const updateUserStatus = async (status: "online" | "offline" | "away") => {
    if (!userId) return

    await updateStatus({
      userId: userId as Id<"users">,
      status,
    })
  }

  const updateUserProfile = async (data: {
    statusMessage?: string
    mood?: "happy" | "sad" | "excited" | "bored" | "busy" | "relaxed"
  }) => {
    if (!userId) return

    await updateUser({
      userId: userId as Id<"users">,
      ...data,
    })
  }

  return (
    <AuthContext.Provider
      value={{
        userId,
        user: existingUser as User | null,
        isLoading: isLoading || !isClerkLoaded || !isUserLoaded,
        isAuthenticated: Boolean(userId),
        error,
        updateUserStatus,
        updateUserProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
