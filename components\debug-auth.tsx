// "use client"

// import { useAuth, useUser } from "@clerk/nextjs"
// import { useConvexUser } from "@/hooks/use-convex-auth"
// import { Button } from "@/components/ui/button"

// export function DebugAuth() {
//   const { isLoaded: isClerkLoaded, userId: clerkId, isSignedIn } = useAuth()
//   const { user: clerkUser } = useUser()
//   const { userId: convexUserId, user: convexUser, isLoading, error } = useConvexUser()

//   return (
//     <div className="fixed bottom-4 right-4 p-4 bg-white border rounded-lg shadow-lg max-w-md z-50">
//       <h3 className="font-bold mb-2">Auth Debug</h3>
//       <div className="text-xs space-y-1">
//         <p>Clerk Loaded: {isClerkLoaded ? "Yes" : "No"}</p>
//         <p>Clerk Signed In: {isSignedIn ? "Yes" : "No"}</p>
//         <p>Clerk ID: {clerkId || "None"}</p>
//         <p>Clerk User: {clerkUser ? clerkUser.fullName : "None"}</p>
//         <p>Convex Loading: {isLoading ? "Yes" : "No"}</p>
//         <p>Convex User ID: {convexUserId || "None"}</p>
//         <p>Convex User: {convexUser ? convexUser.name : "None"}</p>
//         {error && <p className="text-red-500">Error: {error.message}</p>}
//       </div>
//       <Button size="sm" variant="outline" className="mt-2" onClick={() => window.location.reload()}>
//         Refresh
//       </Button>
//     </div>
//   )
// }

