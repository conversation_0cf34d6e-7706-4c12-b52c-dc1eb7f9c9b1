"use client"

import { cn } from "@/lib/utils"

type UserStatus = "online" | "offline" | "away"

interface UserStatusBadgeProps {
  status: UserStatus
  className?: string
  showLabel?: boolean
}

export function UserStatusBadge({ status, className, showLabel = false }: UserStatusBadgeProps) {
  const statusColors = {
    online: "bg-green-500",
    offline: "bg-gray-400",
    away: "bg-yellow-500",
  }

  const statusLabels = {
    online: "Online",
    offline: "Offline",
    away: "Away",
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <span className={cn("h-2 w-2 rounded-full", statusColors[status])} />
      {showLabel && <span className="text-xs font-medium">{statusLabels[status]}</span>}
    </div>
  )
}

