// components/user-status-badge.tsx
"use client"

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { cn } from "@/lib/utils"; // Assuming you have a utility for class names

interface UserStatusBadgeProps {
  userId: Id<"users">;
  showLabel?: boolean; // Whether to show "Online", "Away", "Offline" text
  className?: string;
}

export function UserStatusBadge({ userId, showLabel = false, className }: UserStatusBadgeProps) {
  const statusData = useQuery(api.presence.getUserPresence, { userId });

  // Define the status type and default to 'offline' if status is not available
  type Status = 'online' | 'away' | 'offline';
  const isValidStatus = (s: string | undefined): s is Status => 
    s === 'online' || s === 'away' || s === 'offline';
  const status: Status = isValidStatus(statusData?.status) ? statusData.status : 'offline';

  const statusColors = {
    online: "bg-green-500",
    away: "bg-yellow-500",
    offline: "bg-gray-400",
  } as const;

  const statusColorClass = statusColors[status];

  const statusLabel = status.charAt(0).toUpperCase() + status.slice(1);

  return (
    <span
      className={cn(
        "flex items-center gap-1",
        className
      )}
      title={`Status: ${statusLabel}`}
    >
      <span
        className={cn(
          "block h-2.5 w-2.5 rounded-full",
          statusColorClass,
          { "border-2 border-background": showLabel } // Add border if label is shown for better contrast
        )}
      ></span>
      {showLabel && <span className="text-xs text-muted-foreground">{statusLabel}</span>}
    </span>
  );
}