"use client"

import { useEffect, useState } from "react"
import { ChatSidebar } from "@/components/chat/chat-sidebar"
import { ChatWindow } from "@/components/chat/chat-window"
import { AuthFallback } from "@/components/auth-fallback"
import { useAuth } from "@/hooks/use-auth"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

export default function ChatDetailPage() {
  const { isLoading, error } = useAuth()
  const [authFailed, setAuthFailed] = useState(false)

  useEffect(() => {
    // If there's an authentication error, show the fallback after a delay
    if (error) {
      const timer = setTimeout(() => {
        setAuthFailed(true)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [error])

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (authFailed) {
    return <AuthFallback />
  }

  return (
    <div className="flex h-full">
      <div className="w-80 border-r h-full md:block hidden">
        <ChatSidebar />
      </div>
      <div className="flex-1">
        <ChatWindow />
      </div>
    </div>
  )
}
