"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useUser } from "@clerk/nextjs"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { formatMoolaAmount } from "@/lib/moola-services"
import { Co<PERSON>, Users, Gift } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export function ReferralSystem() {
  const { user } = useUser()
  const { toast } = useToast()
  
  // Get user data
  const userData = useQuery(api.users.getUserByClerkId, {
    clerkId: user?.id || ""
  })
  
  // Generate referral link
  const referralLink = user ? `${window.location.origin}/sign-up?ref=${user.id}` : ""
  
  // Copy to clipboard
  const copyToClipboard = () => {
    if (!referralLink) return
    
    navigator.clipboard.writeText(referralLink)
    toast({
      title: "Copied!",
      description: "Referral link copied to clipboard.",
    })
  }
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5 text-blue-500" />
          Invite Friends & Earn
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="referral-link">Your Referral Link</Label>
          <div className="flex gap-2">
            <Input
              id="referral-link"
              value={referralLink}
              readOnly
              className="font-mono text-sm"
            />
            <Button 
              variant="outline" 
              size="icon"
              onClick={copyToClipboard}
              disabled={!referralLink}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="rounded-lg border p-4">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <Gift className="h-4 w-4 text-yellow-500" />
              <span className="text-sm">Your Earnings</span>
            </div>
            <div className="text-2xl font-bold">
              {formatMoolaAmount(userData?.referralCount || 0 * 100)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {formatMoolaAmount(100)} per referral
            </p>
          </div>
          
          <div className="rounded-lg border p-4">
            <div className="flex items-center gap-2 text-muted-foreground mb-1">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="text-sm">Referred Friends</span>
            </div>
            <div className="text-2xl font-bold">
              {userData?.referralCount || 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Friends joined with your link
            </p>
          </div>
        </div>
        
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
            <Gift className="h-4 w-4 text-blue-500" />
            How It Works
          </h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• Share your referral link with friends</li>
            <li>• They sign up using your link</li>
            <li>• You both earn {formatMoolaAmount(100)} when they join!</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
