"use client"

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useChats } from "@/hooks/use-chats";
import { useContacts } from "@/hooks/use-contacts";
import { useToast } from "@/components/ui/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { clearBrowserCache } from "./cache-utils";
import { SearchHeader } from "./search-header";
import { ChatList } from "./chat-list";
import { ChatSidebarProps } from "./types";

export function ChatSidebar({ clearCacheOnNavigate = false }: ChatSidebarProps) {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { userId, isLoading: isAuthLoading } = useAuth();
  const [search, setSearch] = useState("");
  const [initialError, setInitialError] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Data hooks
  const {
    chats,
    isLoading: isChatsLoading,
    startDirectChat,
    removeContactFromList,
  } = useChats();
  
  const {
    contacts,
    isLoading: isContactsLoading,
  } = useContacts();

  const currentChatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId;
  const isCoreDataLoading = isAuthLoading || isChatsLoading;
  const areContactsReady = !isContactsLoading && Array.isArray(contacts);

  // Effects
  useEffect(() => {
    if (!isAuthLoading && !userId) {
      setInitialError("Authentication error. Please try refreshing.");
    } else if (!isAuthLoading && !isChatsLoading && !chats) {
      setInitialError("Failed to load chat data. Please try refreshing.");
    } else {
      setInitialError(null);
    }
  }, [userId, isAuthLoading, chats, isChatsLoading]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleResize = () => setIsMobile(window.innerWidth < 768);
      handleResize();
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  // Event handlers
  const handleStartChat = async (contactUserId: string) => {
    try {
      if (clearCacheOnNavigate) {
        await clearBrowserCache({ aggressive: false, cacheNamePrefix: "your-app-cache-" });
      }
      const chatId = await startDirectChat(contactUserId);
      if (chatId) {
        router.replace(`/chat/${chatId}?t=${Date.now()}`);
      } else {
        throw new Error("Failed to get chat ID after creation/retrieval.");
      }
    } catch (error: any) {
      console.error("Error starting direct chat:", error);
      toast({
        title: "Error Starting Chat",
        description: error?.message || "Could not initiate the chat. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveContact = async (contactRelationshipId: string) => {
    try {
      await removeContactFromList(contactRelationshipId);
      toast({
        title: "Contact Removed",
        description: "Contact has been successfully removed from your list.",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error removing contact:", error);
      toast({
        title: "Error Removing Contact",
        description: error?.message || "Failed to remove the contact. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSearchChange = (value: string) => setSearch(value);
  const handleChatSelect = (chatId: string) => router.push(`/chat/${chatId}`);
  const handleManageContacts = () => {
    router.push(isMobile ? "/chat/mobile/contacts" : "/contacts");
  };

  // Loading state
  if (isCoreDataLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-4 border-r bg-background">
        <LoadingSpinner />
        <p className="mt-4 text-sm text-muted-foreground">Loading data...</p>
      </div>
    );
  }

  // Error state
  if (initialError) {
    return (
      <div className="p-4 border-r bg-background h-full">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{initialError}</AlertDescription>
        </Alert>
        <Button className="w-full mt-4" onClick={() => window.location.reload()}>
          Retry Loading
        </Button>
      </div>
    );
  }

  // Main render
  return (
    <div className={`flex flex-col h-full border-r bg-background ${isMobile ? "pb-16" : ""}`}>
      <SearchHeader 
        search={search}
        onSearchChange={handleSearchChange}
        onManageContacts={handleManageContacts}
      />
      
      <ChatList
        chats={chats || []}
        currentChatId={currentChatId}
        currentUserId={userId}
        contacts={contacts || []}
        isContactsReady={areContactsReady}
        onChatSelect={handleChatSelect}
        onRemoveContact={handleRemoveContact}
        searchQuery={search}
      />
    </div>
  );
}
