import { Button } from "@/components/ui/button";
import { ArrowDown } from "lucide-react";
import { ScrollToBottomButtonProps } from "../types";

export const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  visible,
  count,
  onClick,
}) => {
  if (!visible) return null;

  return (
    <Button
      variant="secondary"
      size="sm"
      className="absolute bottom-16 right-4 rounded-full shadow-lg flex items-center justify-center z-10"
      onClick={onClick}
    >
      <ArrowDown className="h-4 w-4 mr-1" />
      <span className="text-xs">
        {count > 0 ? `${count} new message${count > 1 ? 's' : ''}` : 'New messages'}
      </span>
    </Button>
  );
};
