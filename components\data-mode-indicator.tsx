"use client"

import { useState } from "react"
import { useDataMode } from "@/hooks/use-data-mode"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Wifi, Zap, AlertTriangle } from "lucide-react"
import { MoolaIcon } from "@/components/moola-icon"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { formatMoolaAmount } from "@/lib/moola-services"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Progress } from "@/components/ui/progress"
import { Id } from "@/convex/_generated/dataModel"

export function DataModeIndicator() {
  const {
    dataMode,
    toggleDataMode,
    isDataModeAvailable,
    messagesRemaining,
    minutesRemaining,
    kbRemaining,
    usageStats,
    emergencyMoolaAvailable,
    useEmergencyMoola,
    isLowMoolaBalance,
  } = useDataMode()

  const { userId } = useAuth()
  const [showDetails, setShowDetails] = useState(false)
  const [usingEmergencyMoola, setUsingEmergencyMoola] = useState(false)

  // Get user's Moola balance
  const moolaBalance = useQuery(api.moola.getMoolaBalance, userId ? { userId: userId as Id<"users"> } : "skip") || 0

  const handleUseEmergencyMoola = () => {
    setUsingEmergencyMoola(true)
    useEmergencyMoola()
    setShowDetails(false)
    setUsingEmergencyMoola(false)
  }

  return (
    <div className="flex items-center gap-2">
      <Popover open={showDetails} onOpenChange={setShowDetails}>
        <PopoverTrigger asChild>
          <Button
            variant={dataMode === "mobile" ? "outline" : "default"}
            size="sm"
            className={`h-8 gap-1 px-2 ${isLowMoolaBalance && dataMode !== "mobile" ? "animate-pulse" : ""}`}
            onClick={() => setShowDetails(true)}
          >
            {dataMode === "mobile" ? (
              <>
                <Wifi className="h-3.5 w-3.5" />
                <span className="text-xs">Mobile Data</span>
              </>
            ) : dataMode === "emergency" ? (
              <>
                <Zap className="h-3.5 w-3.5 text-yellow-500" />
                <span className="text-xs">Emergency Moola</span>
              </>
            ) : (
              <>
                <MoolaIcon className="h-3.5 w-3.5 text-yellow-500" />
                <span className="text-xs">Moola Data</span>
                {isLowMoolaBalance && <AlertTriangle className="h-3.5 w-3.5 text-yellow-500 ml-1" />}
              </>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="font-medium">Data Mode</h4>
              <div className="flex items-center gap-1">
                <MoolaIcon className="h-4 w-4 text-yellow-500" />
                <span className="font-medium">{formatMoolaAmount(moolaBalance)}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={dataMode === "mobile" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  toggleDataMode()
                  setShowDetails(false)
                }}
                className="justify-start"
              >
                <Wifi className="h-4 w-4 mr-2" />
                <span>Mobile Data</span>
              </Button>

              <Button
                variant={dataMode === "moola" ? "default" : "outline"}
                size="sm"
                onClick={() => {
                  toggleDataMode()
                  setShowDetails(false)
                }}
                disabled={!isDataModeAvailable}
                className="justify-start"
              >
                <MoolaIcon className="h-4 w-4 mr-2 text-yellow-500" />
                <span>Moola Data</span>
              </Button>
            </div>

            {emergencyMoolaAvailable && (
              <div className="pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUseEmergencyMoola}
                  disabled={usingEmergencyMoola}
                  className="w-full justify-start"
                >
                  <Zap className="h-4 w-4 mr-2 text-yellow-500" />
                  <span>Use Emergency Moola</span>
                </Button>
                <p className="text-xs text-muted-foreground mt-1">
                  One-time grant for 5 messages when you're out of data and Moola
                </p>
              </div>
            )}

            {dataMode !== "mobile" && (
              <div className="space-y-2 pt-2 border-t">
                <h5 className="text-sm font-medium">With your current balance:</h5>
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div className="bg-muted p-2 rounded-md">
                    <p className="text-lg font-bold">{messagesRemaining}</p>
                    <p className="text-xs">Messages</p>
                  </div>
                  <div className="bg-muted p-2 rounded-md">
                    <p className="text-lg font-bold">{minutesRemaining}</p>
                    <p className="text-xs">Minutes</p>
                  </div>
                  <div className="bg-muted p-2 rounded-md">
                    <p className="text-lg font-bold">{Math.floor(kbRemaining / 1024)}</p>
                    <p className="text-xs">MB Data</p>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>Today's usage:</span>
                    <span>{usageStats.todayMoolaUsed.toFixed(1)} Moola</span>
                  </div>
                  <Progress value={(usageStats.todayMoolaUsed / (usageStats.todayMoolaUsed + moolaBalance)) * 100} />
                </div>
              </div>
            )}

            <div className="pt-2 border-t">
              <h5 className="text-sm font-medium mb-1">Moola Rates:</h5>
              <ul className="text-xs space-y-1 text-muted-foreground">
                <li>• 1 Moola = 5 text messages</li>
                <li>• 1 Moola = 2 minutes active chat</li>
                <li>• 1 Moola = 200KB data transfer</li>
                <li>• Images cost 1 Moola each</li>
              </ul>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-1 bg-muted px-3 py-1 rounded-full">
              <MoolaIcon className="h-3.5 w-3.5 text-yellow-500" />
              <span className="text-xs font-medium">{formatMoolaAmount(moolaBalance)}</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Your Moola Balance</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}

