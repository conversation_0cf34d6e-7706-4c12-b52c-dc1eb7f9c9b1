"use client"

import type { ReactNode } from "react"
import { ConvexReactClient } from "convex/react"
import { ConvexProviderWithClerk } from "convex/react-clerk"
import { useAuth } from "@clerk/clerk-react"

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!)

export function ConvexProvider({ children }: { children: ReactNode }) {
  const { getToken } = useAuth()

  return (
    <ConvexProviderWithClerk client={convex} useAuth={useAuth} getToken={getToken}>
      {children}
    </ConvexProviderWithClerk>
  )
}

