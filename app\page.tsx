import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { redirect } from "next/navigation";
// Assuming useAuth is correctly set up for client-side rendering
// If this is a Server Component, auth logic might need adjustment
// For simplicity, assuming it works as intended client-side or pattern is adapted
// import { useAuth } from "@/hooks/use-auth"; // Keep if client component
import { MixitLogo } from "@/components/mixit-logo";
import { MoolaIcon } from "@/components/moola-icon";
import { auth } from "@clerk/nextjs/server"; // Using Clerk example for Server Component auth

// --- Placeholder Icons (Replace with your actual icon components or SVGs) ---
const ChatBubbleIcon = ({ className }: { className?: string }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
);
const UsersIcon = ({ className }: { className?: string }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
);
const ShoppingCartIcon = ({ className }: { className?: string }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="8" cy="21" r="1"></circle><circle cx="19" cy="21" r="1"></circle><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path></svg>
);
const DataSignalSlashIcon = ({ className }: { className?: string }) => (
    // Simple combination idea - replace with a better icon if available
    <svg className={className} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20"/>
        <path d="M2 8a13 13 0 0 1 13 13M12 20h.01"/>
        <path d="m2 2 20 20"/> {/* Slash */}
    </svg>
);
// --- End Placeholder Icons ---

export default async function LandingPage() {
  // Server-side auth check example (adjust if using client-side useAuth)
  const { userId } = await auth();

  // If user is already logged in, redirect to chat
  if (userId) {
    redirect("/chat");
  }

  return (
    <div className="flex min-h-screen flex-col bg-background text-foreground">
      <header className="sticky top-0 z-50 flex h-16 items-center border-b bg-background/95 backdrop-blur px-4 md:px-6">
        <div className="flex items-center gap-2">
          <img src="/icon-512.svg" alt="XITchat" className="h-8 w-8" />
          <span className="text-xl font-bold hidden sm:inline">XIT<span className="text-primary">chat</span></span>
          {/* Optional: Add Proudly SA Flag/Text here if desired */}
          {/* <span className="ml-4 text-sm text-muted-foreground hidden sm:inline">Proudly South African 🇿🇦</span> */}
          <span className="text-xl font-bold sm:hidden">XC</span> {/* Show abbreviation */}
        </div>
        <div className="ml-auto flex gap-2">
          <Link href="/sign-in">
            <Button variant="ghost" size="sm">Sign In</Button>
          </Link>
          <Link href="/sign-up">
            <Button size="sm" className="sm:w-auto">Get Started</Button> {/* Slightly more inviting CTA */}
          </Link>
        </div>
      </header>
      <main className="flex-1">
        {/* Hero Section - USP Focused */}
        <section className="w-full py-16 md:py-28 lg:py-36 bg-gradient-to-b from-background to-muted/50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-10 lg:grid-cols-[1fr_450px] lg:gap-16 items-center">
            <div className="space-y-4 md:space-y-5 text-center lg:text-left"> {/* Center text on mobile, left-align on desktop */}
                {/* Headline Option A - Focus on Data */}
                <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl text-balance">
                  XitChat: Chat On, <span className="text-primary">Even When Data Off.</span>
                </h1>
                <p className="max-w-[650px] text-muted-foreground mx-auto lg:mx-0 md:text-xl"> {/* Centered text via mx-auto on mobile */}
                  Your Mzansi chat app is back! Reconnect with friends, join local chatrooms & trade on the Tradepost. Use <strong className="text-primary font-semibold">Moola</strong> to keep chatting when your data runs low. The Mxit magic, reimagined for SA.
                </p>
 {/* Online Count Badge */}
 <div className="mt-4 mb-5 flex justify-center lg:justify-start"> {/* Center badge on mobile */}
                    <span className="inline-block bg-green-100 text-green-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                      {/* NOTE: Dynamically fetch this if possible, otherwise use a realistic placeholder */}
                      1,243 South Africans Online Now!
                    </span>
                 </div>
                       {/* Buttons Stack Vertically by Default */}
                       <div className="flex flex-col gap-3 sm:gap-4 max-w-xs mx-auto lg:mx-0 lg:max-w-none lg:flex-row"> {/* Stacked buttons, centered on mobile, row on lg+ */}
                  <Link href="/sign-up">
                    <Button size="lg" className="w-full min-[400px]:w-auto ">
                      Get Started Free
                    </Button>
                  </Link>
                  {/* Changed secondary CTA to focus on Moola benefit */}
                  <Link href="#how-it-works"> {/* Link to the new features section */}
                    <Button size="lg" variant="outline" className="w-full min-[400px]:w-auto border-primary/50 text-primary hover:bg-primary/10">
                      How Moola Data Works
                    </Button>
                  </Link>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                    Sign up now and get <strong className="text-primary">100 Moola FREE!</strong> (Limited time offer)
                </p>
              </div>
              {/* Visual Element - Improved Placeholder */}
              <div className="flex items-center justify-center p-6 lg:p-0">
                {/*
                  IDEAL VISUAL:
                  - A split screen showing regular chat vs. "Moola Data Mode" active (maybe grayscale/simplified UI).
                  - OR An animation cycling through Chat -> Chatroom -> Tradepost -> Moola Balance screens.
                  - Below is a static placeholder suggesting these elements.
                */}
                <div className="relative w-full max-w-md">
                  <div className="absolute -inset-2 rounded-xl bg-gradient-to-r from-primary to-secondary opacity-30 blur-xl"></div>
                  <div className="relative bg-card border rounded-lg shadow-lg p-4 space-y-3">
                    <p className="text-sm font-medium text-center text-muted-foreground mb-2">XitChat App Preview</p>
                    <div className="h-48 rounded-md bg-muted/50 flex flex-col items-center justify-center p-4 space-y-2 border">
                        <p className="text-center text-sm font-semibold">Chat Screen</p>
                        <p className="text-xs text-muted-foreground text-center">"Hey bru! Lekker to see you here. Running low on data..."</p>
                        <div className="w-full h-px bg-border my-2"></div>
                        <p className="text-center text-sm font-semibold text-primary">[ Moola Data Active ]</p>
                        <p className="text-xs text-muted-foreground text-center">"...but can still chat thanks to Moola!"</p>
                    </div>
                    <div className="flex items-center justify-between text-xs font-medium">
                       <div className="flex items-center gap-1 text-primary">
                          <MoolaIcon className="h-4 w-4" />
                          <span>Balance: 150 Moola</span> {/* Example balance */}
                       </div>
                       <Link href="/tradepost" className="text-blue-600 hover:underline"> {/* Added link suggestion */}
                         Browse Tradepost
                       </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it Works / Features Section */}
        <section id="how-it-works" className="w-full py-16 md:py-24 lg:py-32 border-t">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">Key Features</div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Why Mzansi Loves <span className="text-primary">XitChat</span></h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Bringing back the connections you missed, with features built for South Africa today.
              </p>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-1 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8">
              {/* Feature 1: Chat with Moola */}
              <div className="flex flex-col items-center space-y-3 rounded-lg border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
                <div className="rounded-full bg-primary/10 p-3">
                   {/* Combined Icon Idea */}
                   <div className="relative h-8 w-8 text-primary">
                      <ChatBubbleIcon className="absolute top-0 left-0 h-6 w-6" />
                      <MoolaIcon className="absolute bottom-0 right-0 h-5 w-5 border-2 border-background rounded-full bg-background" />
                   </div>
                   {/* Alternative Simple Icon: <DataSignalSlashIcon className="h-8 w-8 text-primary" /> */}
                </div>
                <h3 className="text-xl font-semibold">Chat with Moola</h3>
                <p className="text-sm text-muted-foreground">
                  Data finished? No stress! Use your <strong className="text-primary">Moola</strong> balance to send and receive essential chat messages when you're offline. Keep the convo going!
                </p>
              </div>
              {/* Feature 2: Local Chatrooms */}
              <div className="flex flex-col items-center space-y-3 rounded-lg border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
                <div className="rounded-full bg-primary/10 p-3">
                  <UsersIcon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Mzansi Chatrooms</h3>
                <p className="text-sm text-muted-foreground">
                  Find your vibe! Join public chatrooms based on your location, interests, or just lekker local topics. Meet new people and share what's happening.
                </p>
              </div>
              {/* Feature 3: Tradepost Marketplace */}
              <div className="flex flex-col items-center space-y-3 rounded-lg border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
                <div className="rounded-full bg-primary/10 p-3">
                  <ShoppingCartIcon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Tradepost Marketplace</h3>
                <p className="text-sm text-muted-foreground">
                  Buy, sell, or trade goods and services directly within XitChat. Discover deals or list your own items in our community marketplace.
                </p>
              </div>
              {/* Optional Feature 4: SA Flavour */}
              {/*
              <div className="flex flex-col items-center space-y-3 rounded-lg border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
                 <div className="rounded-full bg-primary/10 p-3">
                   <PaintBrushIcon className="h-8 w-8 text-primary" /> // Placeholder icon
                 </div>
                 <h3 className="text-xl font-semibold">Local Flavour</h3>
                 <p className="text-sm text-muted-foreground">
                   Customize your experience with unique SA-themed stickers, sounds, and profile options. Make XitChat truly yours!
                 </p>
               </div>
              */}
            </div>
          </div>
        </section>

        {/* Installation Instructions Section */}
        <section id="install-app" className="w-full py-16 md:py-24 lg:py-32 border-t bg-muted/30">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <div className="inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm text-primary">Install XITchat</div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Use XITchat <span className="text-primary">Anywhere</span></h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Install XITchat on your device for the best experience - no app store needed!
              </p>
            </div>

            <div className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center">
              {/* Instructions */}
              <div className="space-y-6">
                <h3 className="text-2xl font-bold">How to Install XITchat</h3>

                {/* Android Instructions */}
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h4 className="text-xl font-semibold mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-green-500"><path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon></svg>
                    Android
                  </h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Open XITchat in Chrome browser</li>
                    <li>Tap the menu icon (three dots) in the top right</li>
                    <li>Select "Add to Home screen"</li>
                    <li>Confirm by tapping "Add"</li>
                    <li>XITchat will now appear on your home screen!</li>
                  </ol>
                </div>

                {/* iOS Instructions */}
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h4 className="text-xl font-semibold mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-blue-500"><rect x="2" y="4" width="20" height="16" rx="2"></rect><path d="M12 16a4 4 0 0 1-4-4 4 4 0 0 1 4-4 4 4 0 0 1 4 4 4 4 0 0 1-4 4z"></path></svg>
                    iPhone & iPad
                  </h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Open XITchat in Safari browser</li>
                    <li>Tap the Share icon at the bottom of the screen</li>
                    <li>Scroll down and select "Add to Home Screen"</li>
                    <li>Tap "Add" in the top right corner</li>
                    <li>XITchat will now appear on your home screen!</li>
                  </ol>
                </div>

                {/* Desktop Instructions */}
                <div className="rounded-lg border bg-card p-6 shadow-sm">
                  <h4 className="text-xl font-semibold mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 text-purple-500"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>
                    Desktop (Windows/Mac)
                  </h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Open XITchat in Chrome, Edge, or other modern browser</li>
                    <li>Look for the install icon in the address bar (usually on the right)</li>
                    <li>Click "Install XITchat" when prompted</li>
                    <li>XITchat will open in its own window like a native app!</li>
                  </ol>
                </div>
              </div>

              {/* Visual */}
              <div className="flex items-center justify-center">
                <div className="relative w-full max-w-md">
                  <div className="absolute -inset-2 rounded-xl bg-gradient-to-r from-primary to-secondary opacity-30 blur-xl"></div>
                  <div className="relative bg-card border rounded-lg shadow-lg p-6 space-y-4">
                    <h3 className="text-xl font-bold text-center">Benefits of Installing</h3>
                    <ul className="space-y-3">
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"><polyline points="20 6 9 17 4 12"></polyline></svg>
                        <span>Faster access - open XITchat with one tap</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"><polyline points="20 6 9 17 4 12"></polyline></svg>
                        <span>Full-screen experience without browser controls</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"><polyline points="20 6 9 17 4 12"></polyline></svg>
                        <span>Works offline with Moola when data is unavailable</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"><polyline points="20 6 9 17 4 12"></polyline></svg>
                        <span>Receive notifications for new messages</span>
                      </li>
                      <li className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5"><polyline points="20 6 9 17 4 12"></polyline></svg>
                        <span>Feels like a native app but uses less storage</span>
                      </li>
                    </ul>
                    <div className="pt-4 flex justify-center">
                      <Link href="/sign-up">
                        <Button size="lg" className="w-full">
                          Get Started Now
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t bg-muted/50 py-6 md:py-8">
        <div className="container flex flex-col items-center justify-between gap-4 px-4 md:px-6 sm:flex-row">
          <div className="text-center text-sm text-muted-foreground sm:text-left">
            © {new Date().getFullYear()} XitChat. All rights reserved.
          </div>
          {/* Subtle Proudly SA Mention */}
          <div className="text-center text-sm text-muted-foreground sm:text-right">
             🇿🇦 Built for South Africa
          </div>
        </div>
      </footer>
    </div>
  );
}

// Helper function/component if needed for PaintBrushIcon (if using the optional feature)
// const PaintBrushIcon = ({ className }: { className?: string }) => (
//  <svg className={className} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
// );