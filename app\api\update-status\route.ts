import { NextRequest, NextResponse } from "next/server"
import { auth } from "@clerk/nextjs/server"

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const { userId: clerkUserId } = await auth()
    
    if (!clerkUserId) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    // Parse the request body
    const body = await request.json()
    const { status } = body

    // Validate status
    if (!["online", "away", "offline"].includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 })
    }

    // For now, just return success
    // In a real implementation, you would update the database here
    console.log(`[API] Status update for ${clerkUserId}: ${status}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[API] Error updating status:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Handle FormData from sendBeacon
export async function PUT(request: NextRequest) {
  try {
    const formData = await request.formData()
    const userId = formData.get('userId') as string
    const status = formData.get('status') as string

    if (!userId || !status) {
      return NextResponse.json({ error: "Missing parameters" }, { status: 400 })
    }

    // Validate status
    if (!["online", "away", "offline"].includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 })
    }

    console.log(`[API] Beacon status update for ${userId}: ${status}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[API] Error updating status via beacon:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
