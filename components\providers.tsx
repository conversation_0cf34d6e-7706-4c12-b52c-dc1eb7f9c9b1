"use client"

import { type ReactNode, useEffect } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/clerk-react"
import { supabase } from "@/lib/supabase"

export function SupabaseProvider({ children }: { children: ReactNode }) {
  return (
    <ClerkProvider publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!}>
      <SupabaseAuthProvider>{children}</SupabaseAuthProvider>
    </ClerkProvider>
  )
}

function SupabaseAuthProvider({ children }: { children: ReactNode }) {
  const { getToken, userId } = useAuth()

  useEffect(() => {
    if (!userId) return

    const setupSupabaseAuth = async () => {
      const token = await getToken({ template: "supabase" })
      if (token) {
        await supabase.auth.setSession({
          access_token: token,
          refresh_token: "",
        })
      }
    }

    setupSupabaseAuth()

    // Set up listener for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "SIGNED_OUT" || !session) {
        // <PERSON>le sign out
      } else if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        // Handle sign in or token refresh
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [userId, getToken])

  return <>{children}</>
}

