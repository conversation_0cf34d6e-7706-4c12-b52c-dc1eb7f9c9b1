import { <PERSON><PERSON> } from "@/components/ui/button"
import { Wifi, WifiOff } from "lucide-react"
import Link from "next/link"

export default function OfflinePage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center">
      <WifiOff className="h-16 w-16 text-muted-foreground mb-4" />
      <h1 className="text-2xl font-bold mb-2">You're offline</h1>
      <p className="text-muted-foreground mb-6">
        XitChat requires an internet connection. Please check your connection and try again.
      </p>
      <div className="space-y-2">
        <Button asChild>
          <Link href="/">
            <Wifi className="mr-2 h-4 w-4" />
            Try Again
          </Link>
        </Button>
      </div>
    </div>
  )
}

