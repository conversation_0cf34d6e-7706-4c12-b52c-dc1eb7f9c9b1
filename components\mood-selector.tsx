"use client"

import { <PERSON>, <PERSON>, <PERSON>own, <PERSON><PERSON>, Clock, Briefcase, Coffee } from "lucide-react"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

interface MoodSelectorProps {
  value: string
  onChange: (value: string) => void
}

const moods = [
  { value: "happy", icon: Smile, label: "Happy", color: "bg-green-100 text-green-700 border-green-200" },
  { value: "sad", icon: Frown, label: "Sad", color: "bg-blue-100 text-blue-700 border-blue-200" },
  { value: "excited", icon: Zap, label: "Excited", color: "bg-yellow-100 text-yellow-700 border-yellow-200" },
  { value: "bored", icon: Clock, label: "Bored", color: "bg-gray-100 text-gray-700 border-gray-200" },
  { value: "busy", icon: Briefcase, label: "Busy", color: "bg-red-100 text-red-700 border-red-200" },
  { value: "relaxed", icon: Coffee, label: "Relaxed", color: "bg-purple-100 text-purple-700 border-purple-200" },
]

export function MoodSelector({ value, onChange }: MoodSelectorProps) {
  // Use a consistent layout for server and client initial render
  // Then update based on screen size after hydration
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Default to 3 columns for server rendering
  const gridClass = mounted && window.innerWidth < 640 ? "grid-cols-2" : "grid-cols-3"

  return (
    <div className={`grid ${gridClass} gap-2`}>
      {moods.map((mood) => {
        const isSelected = value === mood.value
        const Icon = mood.icon

        return (
          <button
            key={mood.value}
            type="button"
            className={cn(
              "relative flex flex-col items-center justify-center rounded-md border p-2 sm:p-3 hover:border-accent",
              mood.color,
              isSelected && "ring-2 ring-primary",
            )}
            onClick={() => onChange(mood.value)}
          >
            {isSelected && (
              <div className="absolute right-1 top-1 h-4 w-4 rounded-full bg-primary text-primary-foreground">
                <Check className="h-4 w-4" />
              </div>
            )}
            <Icon className="mb-1 h-5 w-5" />
            <span className="text-xs font-medium">{mood.label}</span>
          </button>
        )
      })}
    </div>
  )
}

