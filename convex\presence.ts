import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";

// Update user's presence status
export const updatePresence = mutation({
  args: {
    status: v.union(
      v.literal("online"),
      v.literal("away"),
      v.literal("offline")
    ),
  },
  handler: async (ctx, args) => {
    // Early return if not authenticated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      console.debug("[Presence] Not authenticated - skipping presence update");
      return { success: false, error: "Not authenticated" };
    }
    
    try {
      // Get the user from the database using clerk_id
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();

      if (!user) {
        console.debug("[Presence] User not found for clerkId:", identity.subject);
        return { success: false, error: "User not found" };
      }

      // Only update if the status is different or it's been a while since last update
      const shouldUpdate = 
        user.status !== args.status || 
        !user.lastActive || 
        Date.now() - user.lastActive > 60000; // 1 minute

      if (shouldUpdate) {
        await ctx.db.patch(user._id, {
          lastActive: Date.now(),
          lastSeen: Date.now(),
          status: args.status,
          updatedAt: Date.now(),
        });
        console.debug(`[Presence] Updated status to ${args.status} for user ${user._id}`);
      }

      return { success: true };
    } catch (error) {
      // Only log unexpected errors
      if (!(error instanceof Error && error.message.includes('Not authenticated'))) {
        console.error("[Presence] Error updating presence:", error);
      }
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  },
});

// Get user's presence status
export const getUserPresence = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return "offline";
    
    // If user was active in the last 5 minutes, they're online
    const FIVE_MINUTES = 5 * 60 * 1000;
    const lastSeen = user.lastSeen || 0;
    const isOnline = (Date.now() - lastSeen) < FIVE_MINUTES;
    
    return isOnline ? "online" : "offline";
  },
});

// Get multiple users' presence status
export const getUsersPresence = query({
  args: { userIds: v.array(v.id("users")) },
  handler: async (ctx, args) => {
    const result: Record<string, string> = {};
    const FIVE_MINUTES = 5 * 60 * 1000;
    const now = Date.now();
    
    // Get all users at once for better performance
    const users = await Promise.all(
      args.userIds.map(userId => ctx.db.get(userId as Id<"users">))
    );
    
    users.forEach((user, index) => {
      const userId = args.userIds[index];
      if (user) {
        const lastSeen = user.lastSeen || 0;
        const isOnline = (now - lastSeen) < FIVE_MINUTES;
        result[userId] = isOnline ? "online" : "offline";
      } else {
        result[userId] = "offline";
      }
    });
    
    return result;
  },
});

// Get presence status for all users
export const getAllPresence = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    const FIVE_MINUTES = 5 * 60 * 1000;
    const now = Date.now();
    const result: Record<string, string> = {};
    
    for (const user of users) {
      const lastSeen = user.lastSeen || 0;
      const isOnline = (now - lastSeen) < FIVE_MINUTES;
      result[user._id] = isOnline ? "online" : "offline";
    }
    
    return result;
  },
});
