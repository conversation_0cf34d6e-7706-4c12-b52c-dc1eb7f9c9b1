"use client"

import { useAuth } from "@/hooks/use-auth"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoolaIcon } from "@/components/moola-icon"
import { <PERSON>, <PERSON>u } from "lucide-react"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { SignOutButton } from "@clerk/nextjs"
import { StatusWithLabel } from "@/components/status/enhanced-status-indicator"

function formatMoolaDisplay(value: number): string {
  // Convert to string and take first 4 digits
  const numberStr = Math.floor(value).toString().slice(0, 4);
  // Add .0 at the end
  return `${numberStr}.0`;
}

export function MobileHeader() {
  const { userId, user } = useAuth()
  const pathname = usePathname()

  // Get notification count
  const notificationCount = useQuery(
    api.notifications.getUnreadNotificationCount,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  return (
    <div className="sticky top-0 z-50 bg-background border-b md:hidden">
      <div className="flex items-center h-12">
        {/* Left side with menu - fixed width */}
        <div className="w-[72px] px-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0">
              <div className="flex flex-col h-full">
                {/* App Logo */}
                <div className="p-4 border-b flex items-center justify-center">
                  <img src="/icon-512.svg" alt="XITchat" className="h-12 w-12 mr-2" />
                  <span className="font-bold text-xl">XIT<span className="text-primary">chat</span></span>
                </div>

                {/* User Profile Section */}
                <div className="p-4 border-b">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user?.avatar} alt={user?.name} />
                      <AvatarFallback>{user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-bold truncate">{user?.name}</p>
                      <p className="text-xs text-muted-foreground truncate">
                        {user?.statusMessage || "Set a status message"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="flex-1 overflow-auto py-2">
                  <div className="px-2 py-1 text-xs font-semibold text-muted-foreground uppercase">
                    Menu
                  </div>

                  <div className="space-y-1 px-2 mt-1">
                    <Link
                      href="/profile"
                      className="flex items-center px-3 py-2 rounded-md text-sm font-medium hover:bg-muted"
                    >
                      My Profile
                    </Link>
                    <Link
                      href="/settings"
                      className="flex items-center px-3 py-2 rounded-md text-sm font-medium hover:bg-muted"
                    >
                      Settings
                    </Link>
                    <Link
                      href="/moola/buy"
                      className="flex items-center px-3 py-2 rounded-md text-sm font-medium hover:bg-muted"
                    >
                      Buy Moola
                    </Link>
                    <Link
                      href="/notifications"
                      className="flex items-center px-3 py-2 rounded-md text-sm font-medium hover:bg-muted"
                    >
                      Notifications
                      {notificationCount && notificationCount > 0 && (
                        <Badge variant="destructive" className="ml-2">
                          {notificationCount}
                        </Badge>
                      )}
                    </Link>
                  </div>
                </div>

                {/* Sign Out Button */}
                <div className="p-4 border-t">
                  <SignOutButton>
                    <Button variant="outline" className="w-full">
                      Sign Out
                    </Button>
                  </SignOutButton>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Center title - flex center */}
        <div className="flex-1 flex justify-center items-center font-medium">
          <span>
            {pathname.includes('/chat') && 'Chats'}
            {pathname.includes('/contacts') && 'Contacts'}
            {pathname.includes('/rooms') && 'Chatrooms'}
            {pathname.includes('/trade') && 'Tradepost'}
            {pathname.includes('/settings') && 'Settings'}
            {pathname.includes('/profile') && 'Profile'}
            {pathname === '/' && 'XITchat'}
          </span>
        </div>

        {/* Right side with Moola and notifications - fixed width */}
        <div className="w-[72px] flex items-center justify-end space-x-2 px-4">
          {/* Compact Moola counter */}
          <div className="flex items-center bg-muted px-1.5 py-0.5 rounded-full">
            <MoolaIcon className="h-3 w-3 text-yellow-500 mr-0.5" />
            <span className="text-[11px] font-medium tabular-nums">
              {formatMoolaDisplay(user?.moola ?? 0)}
            </span>
          </div>

          <Link href="/notifications">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              {notificationCount && notificationCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
                >
                  {notificationCount > 99 ? "99+" : notificationCount}
                </Badge>
              )}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}