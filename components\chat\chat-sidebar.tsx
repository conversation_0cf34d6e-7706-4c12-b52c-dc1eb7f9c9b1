"use client"

import { useState, useR<PERSON>, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { Search, AlertCircle, UserPlus, Trash2 } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
// Assuming useChats returns removeContactFromList which expects contactRelationshipId
import { useChats } from "@/hooks/use-chats"
// Assuming useContacts returns contacts with the structure:
// { _id: Id<"users">, ..., contactRelationshipId: Id<"contacts">, ... other user fields }
// and also provides isLoading state
import { useContacts } from "@/hooks/use-contacts"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert"
import { UserStatusBadge } from "@/components/user-status-badge" // Assuming this component exists
import { useToast } from "@/components/ui/use-toast"

// --- Browser Cache Clearing Function ---
// Note: Consider if this is truly needed with Convex's reactivity model.
// Convex usually handles data updates automatically. This might be for
// other assets or specific non-Convex related caching strategies.
interface CacheInvalidationOptions {
  aggressive?: boolean
  cacheNamePrefix?: string
}
async function clearBrowserCache(options: CacheInvalidationOptions = {}): Promise<void> {
  if (typeof window === "undefined" || !("caches" in window)) {
    // console.warn("Cache API not supported or not in browser environment.")
    return
  }
  try {
    const { aggressive = false, cacheNamePrefix = "" } = options
    const cacheNames = await caches.keys()
    const cachesToDelete = aggressive
      ? cacheNames
      : cacheNames.filter((cacheName) => cacheName.startsWith(cacheNamePrefix))
    if (cachesToDelete.length > 0) {
        await Promise.all(cachesToDelete.map((cacheName) => caches.delete(cacheName)))
        // console.log(`Cleared ${cachesToDelete.length} browser caches matching prefix '${cacheNamePrefix}'.`)
    }
  } catch (e) {
    // console.error("Failed to clear caches:", e)
  }
}
// --- End Cache Clearing ---

interface ChatSidebarProps {
  clearCacheOnNavigate?: boolean;
}

export function ChatSidebar({ clearCacheOnNavigate = false }: ChatSidebarProps) {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [search, setSearch] = useState("")
  const [initialError, setInitialError] = useState<string | null>(null)
  const [isMobile, setIsMobile] = useState(false)

  const currentChatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId

  // --- Data Hooks ---
  const {
    chats,
    isLoading: isChatsLoading,
    startDirectChat,
    removeContactFromList // Expects contactRelationshipId
  } = useChats()
  const {
    contacts, // Expects array like [{ _id: Id<"users">, ..., contactRelationshipId: Id<"contacts"> }]
    isLoading: isContactsLoading
  } = useContacts()

  // --- Effects ---
  useEffect(() => {
    // Check for initial loading/authentication errors
    if (!isAuthLoading && !userId) {
      setInitialError("Authentication error. Please try refreshing.")
    } else if (!isAuthLoading && !isChatsLoading && !chats) {
      // Only set error if not loading and chats are still undefined/null after load attempt
      setInitialError("Failed to load chat data. Please try refreshing.")
    } else {
        setInitialError(null) // Clear error if conditions improve
    }
  }, [userId, isAuthLoading, chats, isChatsLoading])

  useEffect(() => {
    // Mobile detection
    if (typeof window !== "undefined") {
      const handleResize = () => setIsMobile(window.innerWidth < 768)
      handleResize() // Initial check
      window.addEventListener("resize", handleResize)
      return () => window.removeEventListener("resize", handleResize)
    }
  }, [])

  // --- Derived State & Data Processing ---
  const filteredChats =
    chats?.filter((chat) => {
      if (!chat) return false
      const otherUser = chat.type === "direct" ? chat.participants?.find((p) => p?._id !== userId) : null;
      const nameToSearch = chat.type === "direct" ? otherUser?.name : chat.name;
      // Ensure nameToSearch is a string before calling toLowerCase
      return typeof nameToSearch === 'string' ? nameToSearch.toLowerCase().includes(search.toLowerCase()) : false;
    }) || []

  const sortedChats = [...filteredChats].sort((a, b) => {
    // Robust sorting: handle potentially missing timestamps
    const timeA = a?.lastMessage?.timestamp ?? a?.updatedAt ?? 0;
    const timeB = b?.lastMessage?.timestamp ?? b?.updatedAt ?? 0;
    // Ensure consistent sorting if timestamps are identical (e.g., by creation time or ID)
    if (timeB !== timeA) {
        return timeB - timeA;
    }
    // Fallback sort (optional, e.g., by creation time descending)
    const creationA = a?._creationTime ?? 0;
    const creationB = b?._creationTime ?? 0;
    return creationB - creationA;
  });

  // Calculate loading states relevant for rendering
  const isCoreDataLoading = isAuthLoading || isChatsLoading;
  // Check if contacts are fully loaded AND available as an array
  const areContactsReady = !isContactsLoading && Array.isArray(contacts);


  // --- Event Handlers ---
  const handleStartChat = async (contactUserId: string) => {
    // Starts a chat given the *USER ID* of the contact
    try {
      if (clearCacheOnNavigate) {
        await clearBrowserCache({ aggressive: false, cacheNamePrefix: "your-app-cache-" }); // Adjust prefix if needed
      }
      const chatId = await startDirectChat(contactUserId);
      if (chatId) {
         // Use replace to avoid adding multiple chat entries to history if user clicks rapidly
         router.replace(`/chat/${chatId}?t=${Date.now()}`);
      } else {
          throw new Error("Failed to get chat ID after creation/retrieval.");
      }
    } catch (error: any) {
      console.error("Error starting direct chat:", error);
      toast({
        title: "Error Starting Chat",
        description: error?.message || "Could not initiate the chat. Please try again.",
        variant: "destructive",
      });
    }
  }

  const handleRemoveContact = async (contactRelationshipId: string) => {
    // Removes a contact given the *CONTACT RELATIONSHIP ID*
    try {
      await removeContactFromList(contactRelationshipId);
      toast({
        title: "Contact Removed",
        description: "Contact has been successfully removed from your list.",
        variant: "default",
      });
      // Note: UI update for the contact list happens via Convex reactivity.
      // The specific chat item might linger visually until chats are requeried or page refresh.
    } catch (error: any) {
      console.error("Error removing contact:", error);
      toast({
        title: "Error Removing Contact",
        description: error?.message || "Failed to remove the contact. Please try again.",
        variant: "destructive",
      });
    }
  }

  // --- Conditional Rendering: Loading ---
  if (isCoreDataLoading) { // Use combined loading state for initial display
    return (
      <div className="flex flex-col h-full items-center justify-center p-4 border-r bg-background">
        <LoadingSpinner />
        <p className="mt-4 text-sm text-muted-foreground">Loading data...</p>
      </div>
    )
  }

  // --- Conditional Rendering: Initial Error ---
  if (initialError) {
    return (
      <div className="p-4 border-r bg-background h-full">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{initialError}</AlertDescription>
        </Alert>
        <Button className="w-full mt-4" onClick={() => window.location.reload()}>
          Retry Loading
        </Button>
      </div>
    )
  }

  // --- Main Render ---
  return (
    <div className={`flex flex-col h-full border-r bg-background ${isMobile ? "pb-16" : ""}`}>
      {/* Header Section */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Messages</h2>
          <Button
            variant="ghost"
            size="icon"
            aria-label="Manage Contacts"
            title="Manage Contacts"
            onClick={() => router.push(isMobile ? "/chat/mobile/contacts" : "/contacts")}
          >
            <UserPlus className="h-5 w-5" />
          </Button>
        </div>
        <div className="relative">
          <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search chats..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8 w-full"
            aria-label="Search chats"
          />
        </div>
      </div>

      {/* Chat List Section */}
      <ScrollArea className="flex-1">
        <div className="space-y-1 p-2">
          {/* Empty States */}
          {sortedChats.length === 0 && search.length === 0 && (
             <p className="text-sm text-muted-foreground text-center p-4 italic">No active chats. Start a conversation!</p>
          )}
          {sortedChats.length === 0 && search.length > 0 && (
             <p className="text-sm text-muted-foreground text-center p-4 italic">No chats match your search.</p>
          )}

          {/* Chat List Items */}
          {sortedChats.map((chat, index) => {
             if (!chat || !chat.id) {
                console.warn("Skipping rendering chat due to missing data:", chat, index);
                return null; // Skip rendering if chat or id is invalid
             }

            const isActive = chat.id === currentChatId;
            const otherUser = chat.type === "direct" ? chat.participants?.find((p) => p?._id !== userId) : null;
            const chatName = chat.type === "direct" ? otherUser?.name : chat.name;
            const chatAvatar = chat.type === "direct" ? otherUser?.avatar : undefined;

            const lastMessageTime = chat.lastMessage?.timestamp
              ? formatDistanceToNow(new Date(chat.lastMessage.timestamp), { addSuffix: false })
              : chat.updatedAt ? formatDistanceToNow(new Date(chat.updatedAt), { addSuffix: false }) : "";

            const isOnline = chat.type === "direct" && otherUser?.status === "online";
            const uniqueKey = `chat-${chat.id}-${index}`; // Use chat ID and index

            // --- Logic for Delete Button ---
            // Try to find the contact relationship info *only if* contacts are ready
            const contactInfo = areContactsReady && otherUser?._id
                ? contacts.find(c => c?._id === otherUser._id)
                : undefined;
            const canAttemptDelete = !!(chat.type === 'direct' && otherUser?._id); // Can we identify the user to potentially delete?
            const isDeleteEnabled = canAttemptDelete && areContactsReady && !!contactInfo?.contactRelationshipId; // Is data ready AND relationship found?
            const deleteButtonTitle = !canAttemptDelete ? "Cannot identify contact"
                                    : !areContactsReady ? "Contacts loading..."
                                    : !contactInfo?.contactRelationshipId ? "Contact relationship not found"
                                    : `Remove ${otherUser?.name || 'contact'}`;
            // --- End Logic for Delete Button ---

            return (
              <div key={uniqueKey} className="relative group rounded-md"> {/* Added group and rounding */}
                {/* Main button for chat navigation */}
                <Button
                  variant="ghost"
                  className={`w-full justify-start px-2 py-3 h-auto text-left rounded-md transition-colors ${isActive ? "bg-muted hover:bg-muted" : "hover:bg-accent"}`}
                  aria-label={`Go to chat with ${chatName || 'Unknown'}`}
                  onClick={() => router.push(`/chat/${chat.id}`)}
                >
                  <div className="flex items-center gap-3 w-full">
                    {/* Avatar and Online Status */}
                    <div className="relative flex-shrink-0">
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={chatAvatar || undefined }
                          alt={chatName ? `${chatName}'s avatar` : 'Chat avatar'}
                        />
                        <AvatarFallback className="text-sm">
                          {chatName?.substring(0, 2).toUpperCase() || '?'}
                        </AvatarFallback>
                      </Avatar>
                      {isOnline && (
                        <span
                          className="absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-500 border-2 border-background"
                          title="Online"
                        />
                      )}
                    </div>

                    {/* Chat Name and Last Message */}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center mb-0.5">
                        <p className="font-medium truncate text-sm">{chatName || "Unknown Chat"}</p>
                        {lastMessageTime && (
                          <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                            {lastMessageTime}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        {/* Optional: UserStatusBadge if component exists and is needed */}
                        {chat.type === "direct" && otherUser && (
                          <UserStatusBadge status={otherUser.status || "offline"} />
                        )}
                        <p className="text-xs text-muted-foreground truncate">
                          {chat.lastMessage?.senderId === userId ? "You: " : ""}
                          {chat.lastMessage?.content || (chat.type === 'direct' ? "Chat started" : "Group created")}
                        </p>
                      </div>
                    </div>

                    {/* Unread Count Badge */}
                    {chat.unreadCount && chat.unreadCount > 0 && (
                       <Badge
                         variant="default"
                         className="ml-auto shrink-0 h-5 min-w-[1.25rem] flex items-center justify-center px-1.5 text-xs"
                        >
                         {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
                       </Badge>
                     )}
                  </div>
                </Button>

                {/* Delete Contact Button (only for direct chats) */}
                {canAttemptDelete && ( // Only render the button structure if it's a direct chat with an identifiable user
                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label={deleteButtonTitle} // Use dynamic title for accessibility
                    title={deleteButtonTitle} // Tooltip explaining state
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity disabled:opacity-30 disabled:cursor-not-allowed"
                    disabled={!isDeleteEnabled} // Disable based on calculated state
                    onClick={async (e) => {
                       e.stopPropagation(); // IMPORTANT: Prevent navigating to chat

                       // Final check before proceeding
                       if (!isDeleteEnabled || !contactInfo?.contactRelationshipId) {
                         console.error("Delete action attempted in invalid state.", { isDeleteEnabled, contactInfo, areContactsReady });
                         toast({ title: "Cannot Remove Contact", description: "Contact data may still be loading or unavailable.", variant: "destructive" });
                         return;
                       }

                       const contactName = otherUser?.name || "this contact";

                       if (window.confirm(`Are you sure you want to remove ${contactName} from your contacts?\nThis removes the contact relationship but does not delete the chat history.`)) {
                         // Pass the *RELATIONSHIP ID* to the handler
                         await handleRemoveContact(contactInfo.contactRelationshipId);
                       }
                     }}
                   >
                    <Trash2 className="h-4 w-4 text-muted-foreground group-hover:text-destructive group-disabled:hover:text-muted-foreground" /> {/* Adjust icon color when disabled */}
                  </Button>
                )}
              </div>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}