"use client"

import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { useCallback } from "react"
import { Id } from "@/convex/_generated/dataModel"

export function useEmoticons() {
  const { userId } = useAuth()

  // Get all emoticons
  const allEmoticons = useQuery(api.emoticons.getEmoticons, {})

  // Get user's purchased emoticons
  const userEmoticons = useQuery(api.emoticons.getUserEmoticons, userId ? { userId: userId as Id<"users"> } : "skip")

  // Purchase an emoticon
  const purchaseEmoticonMutation = useMutation(api.emoticons.purchaseEmoticon)

  const purchaseEmoticon = async (emoticonId: string) => {
    if (!userId) return { success: false, error: "Not authenticated" }

    try {
      const result = await purchaseEmoticonMutation({
        userId: userId as Id<"users">,
        emoticonId: emoticonId as Id<"emoticons">,
      })

      return result
    } catch (error) {
      console.error("Error purchasing emoticon:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }

  // Function to replace emoticon codes with images in a message
  const parseEmoticons = useCallback(
    (message: string) => {
      if (!allEmoticons) return message

      let parsedMessage = message

      // Sort emoticons by code length (longest first) to avoid partial replacements
      const sortedEmoticons = [...allEmoticons].sort((a, b) => b.code.length - a.code.length)

      for (const emoticon of sortedEmoticons) {
        // Create a regex that escapes special characters in the emoticon code
        const escapedCode = emoticon.code.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&")
        const regex = new RegExp(escapedCode, "g")

        // Replace the code with an image tag
        parsedMessage = parsedMessage.replace(
          regex,
          `<img src="${emoticon.imageUrl}" alt="${emoticon.name}" class="inline-block h-5 w-5" />`,
        )
      }

      return parsedMessage
    },
    [allEmoticons],
  )

  const getEmoticonsByCategory = useCallback(
    (category: string) => {
      return allEmoticons?.filter((emoticon) => emoticon.category === category) || []
    },
    [allEmoticons],
  )

  return {
    allEmoticons,
    getEmoticonsByCategory,
    userEmoticons,
    purchaseEmoticon,
    parseEmoticons,
  }
}

