"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  Home, 
  MessageSquare, 
  Users, 
  ShoppingBag, 
  User, 
  Menu, 
  X,
  Settings,
  Bell,
  LogOut
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAuth } from "@/hooks/use-auth"
import { MoolaIcon } from "@/components/moola-icon"
import { Badge } from "@/components/ui/badge"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { SignOutButton } from "@clerk/nextjs"

export function MobileNavigation() {
  const pathname = usePathname()
  const { userId, user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  
  // Get unread message count
  const unreadCount = useQuery(
    api.messages.getUnreadMessageCount, 
    userId ? { userId: userId as Id<"users"> } : "skip"
  )
  
  // Get notification count
  const notificationCount = useQuery(
    api.notifications.getUnreadNotificationCount,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  if (!mounted) return null
  
  const navItems = [
    {
      name: "Home",
      href: "/",
      icon: <Home className="h-5 w-5" />,
      active: pathname === "/"
    },
    {
      name: "Chats",
      href: "/chat",
      icon: <MessageSquare className="h-5 w-5" />,
      active: pathname.startsWith("/chat"),
      badge: unreadCount && unreadCount > 0 ? unreadCount : null
    },
    {
      name: "Contacts",
      href: "/contacts",
      icon: <Users className="h-5 w-5" />,
      active: pathname.startsWith("/contacts")
    },
    {
      name: "Tradepost",
      href: "/tradepost",
      icon: <ShoppingBag className="h-5 w-5" />,
      active: pathname.startsWith("/tradepost")
    },
    {
      name: "Profile",
      href: "/profile",
      icon: <User className="h-5 w-5" />,
      active: pathname.startsWith("/profile")
    }
  ]
  
  return (
    <>
      {/* Bottom Navigation Bar */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t md:hidden">
        <div className="flex items-center justify-around h-16">
          {navItems.map((item) => (
            <Link 
              key={item.name} 
              href={item.href}
              className="flex flex-col items-center justify-center w-full h-full"
            >
              <div className={`flex flex-col items-center justify-center ${item.active ? "text-primary" : "text-muted-foreground"}`}>
                <div className="relative">
                  {item.icon}
                  {item.badge && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
                    >
                      {item.badge > 99 ? "99+" : item.badge}
                    </Badge>
                  )}
                </div>
                <span className="text-xs mt-1">{item.name}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>
      
      {/* Mobile Header with Menu */}
      <div className="sticky top-0 z-50 bg-background border-b md:hidden">
        <div className="flex items-center justify-between px-4 h-14">
          <div className="flex items-center">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="mr-2">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[80%] sm:w-[350px] p-0">
                <div className="flex flex-col h-full">
                  {/* User Profile Section */}
                  <div className="p-4 border-b">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user?.avatar} alt={user?.name} />
                        <AvatarFallback>{user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{user?.name}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {user?.statusMessage || "Set a status message"}
                        </p>
                      </div>
                    </div>
                    
                    {/* Moola Balance */}
                    <div className="mt-3 flex items-center justify-between">
                      <div className="flex items-center">
                        <MoolaIcon className="h-4 w-4 text-yellow-500 mr-1" />
                        <span className="text-sm font-medium">{user?.moola || 0} Moola</span>
                      </div>
                      <Link href="/moola/buy">
                        <Button size="sm" variant="outline" onClick={() => setIsOpen(false)}>
                          Buy Moola
                        </Button>
                      </Link>
                    </div>
                  </div>
                  
                  {/* Navigation Links */}
                  <div className="flex-1 overflow-auto py-2">
                    <nav className="space-y-1 px-2">
                      {navItems.map((item) => (
                        <Link 
                          key={item.name} 
                          href={item.href}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                            item.active 
                              ? "bg-primary/10 text-primary" 
                              : "text-foreground hover:bg-muted"
                          }`}
                        >
                          <div className="relative mr-3">
                            {item.icon}
                            {item.badge && (
                              <Badge 
                                variant="destructive" 
                                className="absolute -top-2 -right-2 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
                              >
                                {item.badge > 99 ? "99+" : item.badge}
                              </Badge>
                            )}
                          </div>
                          {item.name}
                        </Link>
                      ))}
                    </nav>
                    
                    <div className="px-3 mt-4">
                      <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                        More
                      </h3>
                      <nav className="mt-2 space-y-1">
                        <Link 
                          href="/settings"
                          onClick={() => setIsOpen(false)}
                          className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-foreground hover:bg-muted"
                        >
                          <Settings className="h-5 w-5 mr-3" />
                          Settings
                        </Link>
                        <Link 
                          href="/notifications"
                          onClick={() => setIsOpen(false)}
                          className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-foreground hover:bg-muted"
                        >
                          <div className="relative mr-3">
                            <Bell className="h-5 w-5" />
                            {notificationCount && notificationCount > 0 && (
                              <Badge 
                                variant="destructive" 
                                className="absolute -top-2 -right-2 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
                              >
                                {notificationCount > 99 ? "99+" : notificationCount}
                              </Badge>
                            )}
                          </div>
                          Notifications
                        </Link>
                      </nav>
                    </div>
                  </div>
                  
                  {/* Sign Out Button */}
                  <div className="p-4 border-t">
                    <SignOutButton>
                      <Button variant="ghost" className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50/50">
                        <LogOut className="h-5 w-5 mr-3" />
                        Sign Out
                      </Button>
                    </SignOutButton>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            <Link href="/" className="flex items-center">
              <span className="text-xl font-bold">XIT<span className="text-primary">chat</span></span>
            </Link>
          </div>
          
          <div className="flex items-center space-x-2">
            <Link href="/notifications">
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                {notificationCount && notificationCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
                  >
                    {notificationCount > 99 ? "99+" : notificationCount}
                  </Badge>
                )}
              </Button>
            </Link>
            <Link href="/profile">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar} alt={user?.name} />
                <AvatarFallback>{user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Spacer for bottom navigation */}
      <div className="h-16 md:h-0 block md:hidden" />
    </>
  )
}
