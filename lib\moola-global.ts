// This file exposes global functions for Moola usage tracking
// to be used by components that don't have direct access to the useDataMode hook

import { MOOLA_RATES } from './moola-services';

// Define the global interface
declare global {
  interface Window {
    recordMessageSent?: (byteCount: number, hasAttachment: boolean) => Promise<void>;
    getMoolaBalance?: () => Promise<number>;
    getDataMode?: () => string;
  }
}

// Initialize the global functions if they don't exist
export function initMoolaGlobals() {
  if (typeof window === 'undefined') return;

  // Function to record message sent
  if (!window.recordMessageSent) {
    window.recordMessageSent = async (byteCount: number, hasAttachment: boolean) => {
      console.warn('recordMessageSent called but not properly initialized');
      throw new Error('Moola tracking not initialized');
    };
  }

  // Function to get Moola balance
  if (!window.getMoolaBalance) {
    window.getMoolaBalance = async () => {
      console.warn('getMoolaBalance called but not properly initialized');
      return 0;
    };
  }

  // Function to get current data mode
  if (!window.getDataMode) {
    window.getDataMode = () => {
      return localStorage.getItem('xitchat-data-mode') || 'mobile';
    };
  }
}

// Set up the global functions with actual implementations
export function setupMoolaGlobals(
  recordMessageSentFn: (byteCount: number, hasAttachment: boolean) => Promise<void>,
  getMoolaBalanceFn: () => Promise<number>,
  getDataModeFn: () => string
) {
  if (typeof window === 'undefined') return;

  window.recordMessageSent = recordMessageSentFn;
  window.getMoolaBalance = getMoolaBalanceFn;
  window.getDataMode = getDataModeFn;
}

// Calculate the Moola cost for a message
export function calculateMessageCost(byteCount: number, hasAttachment: boolean): number {
  let cost = MOOLA_RATES.DATA_PER_MESSAGE;
  
  // Add cost for message size
  const additionalKB = Math.floor(byteCount / 1024);
  cost += additionalKB * MOOLA_RATES.DATA_PER_KB;
  
  // Add cost for attachments
  if (hasAttachment) {
    cost += MOOLA_RATES.IMAGE_UPLOAD;
  }
  
  return cost;
}
