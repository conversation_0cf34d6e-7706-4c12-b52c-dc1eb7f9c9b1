// "use client"

// import type React from "react"
// import { useState } from "react"
// import { Input } from "@/components/ui/input"
// import { Button } from "@/components/ui/button"
// import { Plus, Search, Filter } from "lucide-react"
// import ChatroomCard from "./chatrooms/chatroom-card"
// import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

// export interface Chatroom {
//   _id: string
//   name: string
//   description?: string
//   isPrivate: boolean
//   creatorId: string
//   createdAt: number
//   thumbnailUrl?: string
//   location?: string
//   tags?: string[]
//   memberCount: number
// }

// interface ChatroomListProps {
//   chatrooms: Chatroom[]
//   userChatrooms: string[]
//   onJoinChatroom: (id: string) => void
//   onCreateChatroom: () => void
// }

// const ChatroomList: React.FC<ChatroomListProps> = ({ chatrooms, userChatrooms, onJoinChatroom, onCreateChatroom }) => {
//   const [searchQuery, setSearchQuery] = useState("")
//   const [locationFilter, setLocationFilter] = useState("")

//   const filteredChatrooms = chatrooms.filter(
//     (chatroom) =>
//       chatroom.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
//       (!locationFilter || (chatroom.location && chatroom.location.includes(locationFilter))),
//   )

//   return (
//     <div className="flex flex-col h-full">
//       <div className="p-4 border-b">
//         <div className="flex flex-wrap gap-2 mb-4">
//           <div className="relative flex-1">
//             <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
//             <Input
//               placeholder="Search chatrooms..."
//               className="pl-8"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//             />
//           </div>

//           <Button variant="default" size="icon" className="flex-shrink-0" onClick={onCreateChatroom}>
//             <Plus className="h-4 w-4" />
//           </Button>
//         </div>

//         <div className="flex items-center gap-2">
//           <Filter className="h-4 w-4 text-muted-foreground" />
//           <Input
//             placeholder="Filter by location (e.g. Cape Town, Johannesburg)"
//             className="text-xs"
//             value={locationFilter}
//             onChange={(e) => setLocationFilter(e.target.value)}
//           />
//         </div>
//       </div>

//       <div className="flex-1 overflow-y-auto p-4">
//         <Tabs defaultValue="all" className="w-full">
//           <TabsList className="w-full mb-4">
//             <TabsTrigger value="all" className="flex-1">
//               All Chatrooms
//             </TabsTrigger>
//             <TabsTrigger value="my" className="flex-1">
//               My Chatrooms
//             </TabsTrigger>
//           </TabsList>

//           <TabsContent value="all" className="m-0">
//             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//               {filteredChatrooms.length === 0 ? (
//                 <div className="col-span-full flex justify-center items-center h-40 text-muted-foreground">
//                   No chatrooms found
//                 </div>
//               ) : (
//                 filteredChatrooms.map((chatroom) => (
//                   <ChatroomCard
//                     key={chatroom._id}
//                     id={chatroom._id}
//                     name={chatroom.name}
//                     description={chatroom.description}
//                     memberCount={chatroom.memberCount}
//                     thumbnailUrl={chatroom.thumbnailUrl}
//                     location={chatroom.location}
//                     tags={chatroom.tags}
//                     createdAt={chatroom.createdAt}
//                     isPrivate={chatroom.isPrivate}
//                     onJoin={onJoinChatroom}
//                     isMember={userChatrooms.includes(chatroom._id)}
//                   />
//                 ))
//               )}
//             </div>
//           </TabsContent>

//           <TabsContent value="my" className="m-0">
//             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//               {filteredChatrooms.filter((c) => userChatrooms.includes(c._id)).length === 0 ? (
//                 <div className="col-span-full flex justify-center items-center h-40 text-muted-foreground">
//                   You haven't joined any chatrooms yet
//                 </div>
//               ) : (
//                 filteredChatrooms
//                   .filter((c) => userChatrooms.includes(c._id))
//                   .map((chatroom) => (
//                     <ChatroomCard
//                       key={chatroom._id}
//                       id={chatroom._id}
//                       name={chatroom.name}
//                       description={chatroom.description}
//                       memberCount={chatroom.memberCount}
//                       thumbnailUrl={chatroom.thumbnailUrl}
//                       location={chatroom.location}
//                       tags={chatroom.tags}
//                       createdAt={chatroom.createdAt}
//                       isPrivate={chatroom.isPrivate}
//                       onJoin={onJoinChatroom}
//                       isMember={true}
//                     />
//                   ))
//               )}
//             </div>
//           </TabsContent>
//         </Tabs>
//       </div>
//     </div>
//   )
// }

// export default ChatroomList

