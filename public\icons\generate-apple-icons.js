// This script generates placeholder Apple splash screens and icons
// In a real app, you would replace these with your actual icons

const fs = require("fs")
const path = require("path")

// Define splash screen sizes for different Apple devices
const splashSizes = [
  { width: 2048, height: 2732 }, // iPad Pro 12.9"
  { width: 1668, height: 2388 }, // iPad Pro 11"
  { width: 1536, height: 2048 }, // iPad 9.7"
  { width: 1125, height: 2436 }, // iPhone X/XS
  { width: 1242, height: 2688 }, // iPhone XS Max
  { width: 828, height: 1792 },  // iPhone XR
  { width: 1242, height: 2208 }, // iPhone 8 Plus
  { width: 750, height: 1334 },  // iPhone 8
  { width: 640, height: 1136 }   // iPhone SE
]

// Define Apple icon sizes
const iconSizes = [180, 152, 144, 120, 114, 76, 72, 60, 57]

const iconDir = path.join(__dirname)

// Ensure the directory exists
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true })
}

// Create a simple SVG for each splash screen
splashSizes.forEach(({ width, height }) => {
  const svg = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="${width}" height="${height}" fill="#4f46e5"/>
    <text x="${width / 2}" y="${height / 2}" font-family="Arial" font-size="${width / 10}" fill="white" text-anchor="middle">XITchat</text>
  </svg>`

  fs.writeFileSync(path.join(iconDir, `apple-splash-${width}-${height}.png`), svg)
  console.log(`Created apple-splash-${width}-${height}.png`)
})

// Create a simple SVG for each Apple icon
iconSizes.forEach((size) => {
  const svg = `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" fill="#4f46e5"/>
    <text x="${size / 2}" y="${size / 2 + size / 10}" font-family="Arial" font-size="${size / 2}" fill="white" text-anchor="middle">XC</text>
  </svg>`

  fs.writeFileSync(path.join(iconDir, `apple-icon-${size}.png`), svg)
  console.log(`Created apple-icon-${size}.png`)
})

console.log("Apple icon generation complete. Convert these SVGs to PNGs for production use.")
