import { NextResponse } from "next/server"
import * as fs from "fs"
import * as path from "path"
import { writeFile } from "fs/promises"
import sharp from "sharp"

export async function GET() {
  try {
    const sizes = [16, 32, 72, 96, 128, 144, 152, 167, 180, 192, 384, 512]
    const iconDir = path.join(process.cwd(), "public", "icons")

    // Ensure the directory exists
    if (!fs.existsSync(iconDir)) {
      await fs.promises.mkdir(iconDir, { recursive: true })
    }

    // Create icons for each size
    for (const size of sizes) {
      const iconPath = path.join(iconDir, `icon-${size}x${size}.png`)
      
      // Create a simple icon using sharp
      const buffer = Buffer.from(`
        <?xml version="1.0" encoding="UTF-8"?>
        <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
          <rect width="${size}" height="${size}" fill="#1a1a2e"/>
          <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="${size * 0.5}" fill="#fff">XC</text>
        </svg>
      `)

      await sharp(buffer)
        .resize(size, size)
        .png()
        .toFile(iconPath)
    }

    // Create special icons
    const specialIcons = [
      { name: "chat-icon-192x192.png", text: "CH", size: 192 },
      { name: "rooms-icon-192x192.png", text: "RM", size: 192 },
      { name: "offline-image.png", text: "Offline", size: 512, bg: "#f0f0f0", color: "#888888" },
    ]

    for (const icon of specialIcons) {
      const iconPath = path.join(iconDir, icon.name)
      
      // Create a simple icon using sharp
      const buffer = Buffer.from(`
        <?xml version="1.0" encoding="UTF-8"?>
        <svg width="${icon.size}" height="${icon.size}" viewBox="0 0 ${icon.size} ${icon.size}" xmlns="http://www.w3.org/2000/svg">
          <rect width="${icon.size}" height="${icon.size}" fill="${icon.bg || "#1a1a2e"}"/>
          <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="${icon.size * 0.25}" fill="${icon.color || "#fff"}">${icon.text}</text>
        </svg>
      `)

      await sharp(buffer)
        .resize(icon.size, icon.size)
        .png()
        .toFile(iconPath)
    }

    return NextResponse.json({ success: true, message: "Icons generated successfully" })
  } catch (error) {
    console.error("Error generating icons:", error)
    return NextResponse.json({ success: false, error: String(error) }, { status: 500 })
  }
}
