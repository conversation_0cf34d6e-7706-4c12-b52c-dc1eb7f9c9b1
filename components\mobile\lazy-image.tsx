"use client"

import { useState, useRef, useEffect, useMemo } from "react"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { ImageIcon, AlertCircle } from "lucide-react"

interface LazyImageProps {
  src: string
  alt: string
  className?: string
  fallback?: string
  placeholder?: React.ReactNode
  onLoad?: () => void
  onError?: () => void
  priority?: boolean
  quality?: number
}

export function LazyImage({
  src,
  alt,
  className,
  fallback,
  placeholder,
  onLoad,
  onError,
  priority = false,
  quality = 75
}: LazyImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) return // Skip lazy loading for priority images

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: "50px", // Start loading 50px before the image enters viewport
        threshold: 0.1
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [priority])

  const handleLoad = () => {
    setIsLoading(false)
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }

  // Optimize image URL with quality parameter if it's a supported service
  const optimizedSrc = useMemo(() => {
    if (!src) return src
    
    // Add quality parameter for common image services
    if (src.includes('cloudinary.com')) {
      return src.replace('/upload/', `/upload/q_${quality}/`)
    }
    
    if (src.includes('imagekit.io')) {
      const separator = src.includes('?') ? '&' : '?'
      return `${src}${separator}tr=q-${quality}`
    }
    
    return src
  }, [src, quality])

  const defaultPlaceholder = (
    <div className="flex items-center justify-center bg-muted">
      <ImageIcon className="h-8 w-8 text-muted-foreground" />
    </div>
  )

  const errorFallback = (
    <div className="flex flex-col items-center justify-center bg-muted text-muted-foreground">
      <AlertCircle className="h-6 w-6 mb-1" />
      <span className="text-xs">Failed to load</span>
    </div>
  )

  return (
    <div ref={containerRef} className={cn("relative overflow-hidden", className)}>
      {/* Loading placeholder */}
      {isLoading && !hasError && (
        <div className="absolute inset-0">
          {placeholder || <Skeleton className="w-full h-full" />}
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0">
          {fallback ? (
            <img
              src={fallback}
              alt={alt}
              className="w-full h-full object-cover"
              onError={() => {
                // If fallback also fails, show error message
                setHasError(true)
              }}
            />
          ) : (
            errorFallback
          )}
        </div>
      )}

      {/* Actual image */}
      {isInView && !hasError && (
        <img
          ref={imgRef}
          src={optimizedSrc}
          alt={alt}
          className={cn(
            "w-full h-full object-cover transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100"
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading={priority ? "eager" : "lazy"}
          decoding="async"
        />
      )}

      {/* Show placeholder when not in view and not priority */}
      {!isInView && !priority && (
        <div className="absolute inset-0">
          {placeholder || defaultPlaceholder}
        </div>
      )}
    </div>
  )
}

// Hook for preloading images
export function useImagePreloader(urls: string[]) {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set())

  useEffect(() => {
    const preloadImage = (url: string) => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image()
        img.onload = () => {
          setLoadedImages(prev => new Set(prev).add(url))
          resolve()
        }
        img.onerror = reject
        img.src = url
      })
    }

    // Preload images with a small delay to not block initial render
    const timer = setTimeout(() => {
      urls.forEach(url => {
        if (!loadedImages.has(url)) {
          preloadImage(url).catch(console.error)
        }
      })
    }, 100)

    return () => clearTimeout(timer)
  }, [urls, loadedImages])

  return loadedImages
}

// Progressive image component that loads low quality first
interface ProgressiveImageProps extends LazyImageProps {
  lowQualitySrc?: string
}

export function ProgressiveImage({
  src,
  lowQualitySrc,
  alt,
  className,
  ...props
}: ProgressiveImageProps) {
  const [highQualityLoaded, setHighQualityLoaded] = useState(false)

  return (
    <div className={cn("relative", className)}>
      {/* Low quality image */}
      {lowQualitySrc && !highQualityLoaded && (
        <LazyImage
          src={lowQualitySrc}
          alt={alt}
          className="absolute inset-0 filter blur-sm scale-105"
          priority
          quality={20}
        />
      )}

      {/* High quality image */}
      <LazyImage
        src={src}
        alt={alt}
        className={cn(
          "transition-opacity duration-500",
          highQualityLoaded ? "opacity-100" : "opacity-0"
        )}
        onLoad={() => setHighQualityLoaded(true)}
        {...props}
      />
    </div>
  )
}
