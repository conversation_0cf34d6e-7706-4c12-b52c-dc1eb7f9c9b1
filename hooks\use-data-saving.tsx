"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

type DataSavingContextType = {
  dataSavingMode: boolean
  toggleDataSavingMode: () => void
}

const DataSavingContext = createContext<DataSavingContextType | undefined>(undefined)

export function DataSavingProvider({ children }: { children: ReactNode }) {
  const [dataSavingMode, setDataSavingMode] = useState(false)

  useEffect(() => {
    // Load preference from localStorage
    const savedPreference = localStorage.getItem("xitchat-data-saving")
    if (savedPreference) {
      setDataSavingMode(savedPreference === "true")
    }

    // Check if user is on a slow connection
    if ("connection" in navigator && (navigator as any).connection) {
      const connection = (navigator as any).connection
      if (connection.saveData || connection.effectiveType === "slow-2g" || connection.effectiveType === "2g") {
        setDataSavingMode(true)
      }
    }
  }, [])

  const toggleDataSavingMode = () => {
    const newValue = !dataSavingMode
    setDataSavingMode(newValue)
    localStorage.setItem("xitchat-data-saving", String(newValue))
  }

  return (
    <DataSavingContext.Provider value={{ dataSavingMode, toggleDataSavingMode }}>{children}</DataSavingContext.Provider>
  )
}

export function useDataSaving() {
  const context = useContext(DataSavingContext)
  if (context === undefined) {
    throw new Error("useDataSaving must be used within a DataSavingProvider")
  }
  return context
}

