"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { useContacts } from "@/hooks/use-contacts"
import { useChats } from "@/hooks/use-chats"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { UserPlus, MessageSquare, Users } from "lucide-react"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/components/ui/use-toast"
import { Id } from "@/convex/_generated/dataModel"

export function OnlineUsersPanel() {
  const router = useRouter()
  const { toast } = useToast()
  const { userId, isLoading: isAuthLoading } = useAuth()
  const { contacts, addContact, isLoading: isContactsLoading } = useContacts()
  const { startDirectChat } = useChats()
  const [activeTab, setActiveTab] = useState("all")

  // Get all online users
  const allOnlineUsers = useQuery(api.users.getOnlineUsers, userId ? { userId: userId as Id<"users"> } : "skip")

  // Get online users in chatrooms
  const chatroomOnlineUsers = useQuery(api.chatrooms.getOnlineUsersInChatrooms, userId ? { userId: userId as Id<"users"> } : "skip")

  // Join chatroom mutation
  const joinChatroom = useMutation(api.chatrooms.joinChatroom)

  const isLoading = isAuthLoading || isContactsLoading || !allOnlineUsers || !chatroomOnlineUsers

  // Create a set of contact IDs for quick lookup
  const contactIds = new Set((contacts || []).map((contact) => contact?._id))

  // Filter online users who are not already contacts
  const nonContactOnlineUsers = (allOnlineUsers || []).filter((user) => !contactIds.has(user._id))

  const handleAddContact = async (contactId: string) => {
    try {
      await addContact(contactId)
      toast({
        title: "Contact added",
        description: "User has been added to your contacts",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add contact",
        variant: "destructive",
      })
    }
  }

  const handleStartChat = async (contactId: string) => {
    try {
      const chatId = await startDirectChat(contactId)
      if (chatId) {
        console.log(`Navigating to chat: ${chatId}`)
        router.push(`/chat/${chatId}`)
      } else {
        console.error("No chat ID returned from startDirectChat")
        toast({
          title: "Error",
          description: "Failed to start chat - no chat ID returned",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error in handleStartChat:", error)
      toast({
        title: "Error",
        description: "Failed to start chat",
        variant: "destructive",
      })
    }
  }

  const handleJoinChatroom = async (chatroomId: string) => {
    if (!userId) return

    try {
      await joinChatroom({ chatroomId: chatroomId as Id<"chats">, userId: userId as Id<"users"> })
      router.push(`/chat/${chatroomId}`)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to join chatroom",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Who's Online</CardTitle>
        <CardDescription>See who's online and connect with them</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="all">
              All Users
              <Badge variant="secondary" className="ml-2">
                {allOnlineUsers?.length || 0}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="chatrooms">
              In Chatrooms
              <Badge variant="secondary" className="ml-2">
                {chatroomOnlineUsers?.length || 0}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-4 space-y-4">
            {allOnlineUsers?.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">No users are currently online</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Online contacts */}
                {allOnlineUsers
                  ?.filter((user) => contactIds.has(user._id))
                  .map((user) => (
                    <div key={user._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar>
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span className="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-green-500 border-2 border-background"></span>
                        </div>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-xs text-muted-foreground">{user.statusMessage || "Online"}</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => handleStartChat(user._id)}>
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Chat
                      </Button>
                    </div>
                  ))}

                {/* Non-contact online users */}
                {nonContactOnlineUsers.length > 0 && (
                  <>
                    <div className="col-span-1 md:col-span-2 mt-4 mb-2">
                      <h3 className="text-sm font-medium text-muted-foreground">Other Online Users</h3>
                    </div>
                    {nonContactOnlineUsers.map((user) => (
                      <div key={user._id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <Avatar>
                              <AvatarImage src={user.avatar} />
                              <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                            </Avatar>
                            <span className="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-green-500 border-2 border-background"></span>
                          </div>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-xs text-muted-foreground">{user.statusMessage || "Online"}</p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleAddContact(user._id)}>
                            <UserPlus className="h-4 w-4 mr-2" />
                            Add
                          </Button>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="chatrooms" className="mt-4 space-y-4">
            {chatroomOnlineUsers?.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">No users are currently online in chatrooms</div>
            ) : (
              <div className="space-y-6">
                {chatroomOnlineUsers?.map((chatroom) => (
                  <div key={chatroom.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{chatroom.name}</h3>
                      <Button variant="outline" size="sm" onClick={() => handleJoinChatroom(chatroom.id)}>
                        <Users className="h-4 w-4 mr-2" />
                        Join Room
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {chatroom.onlineUsers.map((user) => (
                        <div key={user?._id} className="flex items-center justify-between p-2 border rounded-lg">
                          <div className="flex items-center gap-2">
                            <div className="relative">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={user?.avatar} />
                                <AvatarFallback>{user?.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                              </Avatar>
                              <span className="absolute bottom-0 right-0 h-1.5 w-1.5 rounded-full bg-green-500 border-2 border-background"></span>
                            </div>
                            <p className="text-sm font-medium">{user?.name}</p>
                          </div>
                          {user?._id && !contactIds.has(user._id) && (
                            <Button variant="ghost" size="sm" onClick={() => handleAddContact(user._id)}>
                              <UserPlus className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

