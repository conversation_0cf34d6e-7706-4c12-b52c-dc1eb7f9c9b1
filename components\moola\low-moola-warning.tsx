"use client"

import { useState, useEffect } from "react"
import { useDataMode } from "@/hooks/use-data-mode"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Id } from "@/convex/_generated/dataModel"

export function LowMoolaWarning() {
  const { userId } = useAuth()
  const router = useRouter()
  const { isLowMoolaBalance, dataMode, toggleDataMode, emergencyMoolaAvailable, useEmergencyMoola } = useDataMode()
  const [hasShownWarning, setHasShownWarning] = useState(false)

  // Get user's Moola balance
  const moolaBalance = useQuery(api.moola.getMoolaBalance, userId ? { userId: userId as Id<"users"> } : "skip") || 0

  useEffect(() => {
    // Only show warning once per session when conditions are met
    if (isLowMoolaBalance && dataMode === "moola" && !hasShownWarning) {
      setHasShownWarning(true)
      
      toast("Low Moola Balance", {
        description: `You have ${moolaBalance} Moola remaining. Would you like to buy more or switch to mobile data?`,
        action: {
          label: "Buy Moola",
          onClick: () => router.push("/profile?tab=moola")
        },
        cancel: {
          label: "Use Mobile",
          onClick: () => toggleDataMode()
        },
        duration: 0, // Stays until dismissed
      })

      // Show emergency Moola option separately if available
      if (emergencyMoolaAvailable) {
        toast("Emergency Moola Available", {
          description: "You can use emergency Moola credits",
          action: {
            label: "Use Emergency",
            onClick: () => useEmergencyMoola()
          },
          duration: 10000,
        })
      }
    }
  }, [isLowMoolaBalance, dataMode, moolaBalance, hasShownWarning, router, toggleDataMode, emergencyMoolaAvailable, useEmergencyMoola])

  return null
}

