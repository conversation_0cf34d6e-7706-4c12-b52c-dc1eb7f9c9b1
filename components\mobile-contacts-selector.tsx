"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useContacts } from "@/hooks/use-contacts"
import { useChats } from "@/hooks/use-chats"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Search, ArrowLeft, UserPlus } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export function MobileContactsSelector() {
  const router = useRouter()
  const { toast } = useToast()
  const [search, setSearch] = useState("")
  const { contacts, isLoading: isContactsLoading } = useContacts()
  const { startDirectChat } = useChats()

  const filteredContacts =
    contacts?.filter(
      (contact) =>
        contact.name.toLowerCase().includes(search.toLowerCase()) ||
        (contact.email && contact.email.toLowerCase().includes(search.toLowerCase())),
    ) || []

  // Group contacts by online status
  const onlineContacts = filteredContacts.filter((contact) => contact.status === "online")
  const offlineContacts = filteredContacts.filter((contact) => contact.status !== "online")

  const handleStartChat = async (contactId: string) => {
    try {
      // Force clear any cached data
      // console.log(`Starting chat with contact: ${contactId} at ${Date.now()}`)

      // First clear browser cache for this route
      if ("caches" in window) {
        try {
          const cacheNames = await caches.keys()
          await Promise.all(cacheNames.map((cacheName) => caches.delete(cacheName)))
          // console.log("Cleared browser caches")
        } catch (e) {
          // console.error("Failed to clear caches:", e)
        }
      }

      const chatId = await startDirectChat(contactId)
      // console.log(`Chat created/found with ID: ${chatId}`)

      // Use replace instead of push to avoid caching issues
      router.replace(`/chat/${chatId}?t=${Date.now()}`)
    } catch (error) {
      // console.error("Error starting chat:", error)
      toast({
        title: "Error",
        description: "Failed to start chat",
        variant: "destructive",
      })
    }
  }

  if (isContactsLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center gap-2 p-4 border-b">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-xl font-bold">Select Contact</h1>
        <Button variant="ghost" size="icon" className="ml-auto" onClick={() => router.push("/contacts")}>
          <UserPlus className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contacts..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        {contacts?.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-4 text-center">
            <p className="text-muted-foreground mb-2">You don't have any contacts yet</p>
            <Button variant="outline" onClick={() => router.push("/contacts")}>
              <UserPlus className="h-4 w-4 mr-2" />
              Add Contacts
            </Button>
          </div>
        ) : (
          <div className="p-2 space-y-4">
            {/* Online contacts */}
            {onlineContacts.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground px-2 py-1">Online</h3>
                <div className="space-y-1">
                  {onlineContacts.map((contact) => (
                    <Button
                      key={contact._id}
                      variant="ghost"
                      className="w-full justify-start px-2 py-3 h-auto"
                      onClick={() => handleStartChat(contact._id)}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <div className="relative">
                          <Avatar>
                            <AvatarImage src={contact.avatar} />
                            <AvatarFallback>{contact.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-background"></span>
                        </div>
                        <div>
                          <p className="font-medium">{contact.nickname || contact.name}</p>
                          <p className="text-xs text-muted-foreground">{contact.statusMessage || "Online"}</p>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Offline contacts */}
            {offlineContacts.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground px-2 py-1">Offline</h3>
                <div className="space-y-1">
                  {offlineContacts.map((contact) => (
                    <Button
                      key={contact._id}
                      variant="ghost"
                      className="w-full justify-start px-2 py-3 h-auto"
                      onClick={() => handleStartChat(contact._id)}
                    >
                      <div className="flex items-center gap-3 w-full">
                        <Avatar>
                          <AvatarImage src={contact.avatar} />
                          <AvatarFallback>{contact.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{contact.nickname || contact.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {contact?.status === "away" ? "Away" : "Offline"}
                          </p>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {filteredContacts.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No contacts found</p>
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  )
}

