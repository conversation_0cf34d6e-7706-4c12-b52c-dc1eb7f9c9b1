"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { Id } from "@/convex/_generated/dataModel"

interface EnhancedStatusIndicatorProps {
  userId: Id<"users">
  showLabel?: boolean
  showLastSeen?: boolean
  size?: "sm" | "md" | "lg"
  className?: string
  variant?: "dot" | "badge" | "text"
}

export function EnhancedStatusIndicator({
  userId,
  showLabel = false,
  showLastSeen = false,
  size = "md",
  className,
  variant = "dot"
}: EnhancedStatusIndicatorProps) {
  // Get user presence data
  const presenceData = useQuery(api.presence.getUserPresence, { userId })
  const userData = useQuery(api.users.getUser, { userId })

  // Determine status
  const getStatus = () => {
    if (!presenceData || !userData) return "offline"
    
    const now = Date.now()
    const lastSeen = userData.lastSeen || 0
    const timeSinceLastSeen = now - lastSeen
    
    // If last seen is within 2 minutes, use the stored status
    if (timeSinceLastSeen < 2 * 60 * 1000) {
      return userData.status || "offline"
    }
    
    // If last seen is within 5 minutes, mark as away
    if (timeSinceLastSeen < 5 * 60 * 1000) {
      return "away"
    }
    
    // Otherwise, mark as offline
    return "offline"
  }

  const status = getStatus()

  // Status configuration
  const statusConfig = {
    online: {
      color: "bg-green-500",
      textColor: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      label: "Online",
      icon: "●"
    },
    away: {
      color: "bg-yellow-500",
      textColor: "text-yellow-600",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      label: "Away",
      icon: "◐"
    },
    offline: {
      color: "bg-gray-400",
      textColor: "text-gray-500",
      bgColor: "bg-gray-50",
      borderColor: "border-gray-200",
      label: "Offline",
      icon: "○"
    }
  }

  const config = statusConfig[status as keyof typeof statusConfig]

  // Size configuration
  const sizeConfig = {
    sm: {
      dot: "w-2 h-2",
      text: "text-xs",
      badge: "text-xs px-2 py-1"
    },
    md: {
      dot: "w-3 h-3",
      text: "text-sm",
      badge: "text-sm px-2 py-1"
    },
    lg: {
      dot: "w-4 h-4",
      text: "text-base",
      badge: "text-sm px-3 py-1"
    }
  }

  const sizeClasses = sizeConfig[size]

  // Format last seen time
  const getLastSeenText = () => {
    if (!userData?.lastSeen || status === "online") return null
    
    try {
      return `Last seen ${formatDistanceToNow(userData.lastSeen, { addSuffix: true })}`
    } catch {
      return "Last seen recently"
    }
  }

  // Dot variant
  if (variant === "dot") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div
          className={cn(
            "rounded-full border-2 border-white shadow-sm",
            config.color,
            sizeClasses.dot
          )}
          title={config.label}
        />
        {showLabel && (
          <div className="flex flex-col">
            <span className={cn(config.textColor, sizeClasses.text, "font-medium")}>
              {config.label}
            </span>
            {showLastSeen && getLastSeenText() && (
              <span className={cn("text-gray-500", sizeConfig.sm.text)}>
                {getLastSeenText()}
              </span>
            )}
          </div>
        )}
      </div>
    )
  }

  // Badge variant
  if (variant === "badge") {
    return (
      <div className={cn("flex flex-col gap-1", className)}>
        <Badge
          variant="outline"
          className={cn(
            config.bgColor,
            config.borderColor,
            config.textColor,
            sizeClasses.badge,
            "font-medium"
          )}
        >
          <span className={cn("mr-1", config.color, "w-2 h-2 rounded-full inline-block")} />
          {config.label}
        </Badge>
        {showLastSeen && getLastSeenText() && (
          <span className={cn("text-gray-500", sizeConfig.sm.text)}>
            {getLastSeenText()}
          </span>
        )}
      </div>
    )
  }

  // Text variant
  if (variant === "text") {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <span className={cn(config.textColor, sizeClasses.text)}>
          {config.icon}
        </span>
        <span className={cn(config.textColor, sizeClasses.text, "font-medium")}>
          {config.label}
        </span>
        {showLastSeen && getLastSeenText() && (
          <span className={cn("text-gray-500", sizeConfig.sm.text, "ml-2")}>
            ({getLastSeenText()})
          </span>
        )}
      </div>
    )
  }

  return null
}

// Simplified status dot for use in lists
export function StatusDot({ 
  userId, 
  className 
}: { 
  userId: Id<"users">
  className?: string 
}) {
  return (
    <EnhancedStatusIndicator
      userId={userId}
      variant="dot"
      size="sm"
      className={className}
    />
  )
}

// Status with label for profile views
export function StatusWithLabel({ 
  userId, 
  showLastSeen = true,
  className 
}: { 
  userId: Id<"users">
  showLastSeen?: boolean
  className?: string 
}) {
  return (
    <EnhancedStatusIndicator
      userId={userId}
      variant="text"
      size="sm"
      showLabel
      showLastSeen={showLastSeen}
      className={className}
    />
  )
}

// Status badge for cards
export function StatusBadge({ 
  userId, 
  showLastSeen = false,
  className 
}: { 
  userId: Id<"users">
  showLastSeen?: boolean
  className?: string 
}) {
  return (
    <EnhancedStatusIndicator
      userId={userId}
      variant="badge"
      size="sm"
      showLastSeen={showLastSeen}
      className={className}
    />
  )
}
