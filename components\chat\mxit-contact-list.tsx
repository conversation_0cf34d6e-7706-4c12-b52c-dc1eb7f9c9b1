"use client"

import { useState } from "react"
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Search, MessageSquare, UserPlus, Users, Phone, Video } from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { StatusDot } from "@/components/status/enhanced-status-indicator"

interface Contact {
  _id: string
  name: string
  avatar?: string
  status: "online" | "away" | "offline"
  statusMessage?: string
  lastSeen?: number
  mood?: string
  unreadCount?: number
}

interface MxitContactListProps {
  contacts: Contact[]
  onContactSelect?: (contactId: string) => void
  onStartChat?: (contactId: string) => void
  showSearch?: boolean
  className?: string
}

export function MxitContactList({
  contacts,
  onContactSelect,
  onStartChat,
  showSearch = true,
  className
}: MxitContactListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showSearchInput, setShowSearchInput] = useState(false)

  // Filter contacts based on search
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.statusMessage?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Group contacts by status
  const groupedContacts = {
    online: filteredContacts.filter(c => c.status === "online"),
    away: filteredContacts.filter(c => c.status === "away"),
    offline: filteredContacts.filter(c => c.status === "offline")
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online": return "text-green-500"
      case "away": return "text-yellow-500"
      case "offline": return "text-gray-400"
      default: return "text-gray-400"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online": return "●"
      case "away": return "◐"
      case "offline": return "○"
      default: return "○"
    }
  }

  const ContactItem = ({ contact }: { contact: Contact }) => (
    <div
      className="flex items-center gap-3 p-3 hover:bg-blue-50 cursor-pointer transition-colors mxit-fade-in"
      onClick={() => onContactSelect?.(contact._id)}
    >
      {/* Avatar with status indicator */}
      <div className="relative">
        <Avatar className="h-12 w-12 mxit-avatar">
          <AvatarImage src={contact.avatar} alt={contact.name} />
          <AvatarFallback className="bg-blue-100 text-blue-700 font-bold">
            {contact.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        {/* Status indicator */}
        <div className={cn(
          "absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white flex items-center justify-center text-xs",
          contact.status === "online" && "bg-green-500",
          contact.status === "away" && "bg-yellow-500",
          contact.status === "offline" && "bg-gray-400"
        )}>
          <span className="text-white text-xs">
            {getStatusIcon(contact.status)}
          </span>
        </div>
      </div>

      {/* Contact info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-blue-900 truncate">{contact.name}</h3>
          {contact.unreadCount && contact.unreadCount > 0 && (
            <Badge className="mxit-badge ml-2">
              {contact.unreadCount > 99 ? "99+" : contact.unreadCount}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2 mt-1">
          <span className={cn("text-sm", getStatusColor(contact.status))}>
            {contact.status}
          </span>
          {contact.mood && (
            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
              {contact.mood}
            </span>
          )}
        </div>
        
        {contact.statusMessage && (
          <p className="text-sm text-gray-600 truncate mt-1">
            {contact.statusMessage}
          </p>
        )}
        
        {contact.status === "offline" && contact.lastSeen && (
          <p className="text-xs text-gray-500 mt-1">
            Last seen {formatDistanceToNow(contact.lastSeen, { addSuffix: true })}
          </p>
        )}
      </div>

      {/* Action buttons */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-blue-600 hover:bg-blue-100"
          onClick={(e) => {
            e.stopPropagation()
            onStartChat?.(contact._id)
          }}
        >
          <MessageSquare className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  return (
    <div className={cn("bg-white rounded-lg mxit-card", className)}>
      {/* Header */}
      <div className="p-4 border-b border-blue-100 mxit-card-header">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-bold text-blue-700 flex items-center gap-2">
            <Users className="h-5 w-5" />
            Contacts ({contacts.length})
          </h2>
          
          {showSearch && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-blue-600"
              onClick={() => setShowSearchInput(!showSearchInput)}
            >
              <Search className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search input */}
        {showSearchInput && (
          <div className="mt-3">
            <Input
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mxit-input"
              autoFocus
            />
          </div>
        )}
      </div>

      {/* Contact groups */}
      <div className="max-h-96 overflow-y-auto mxit-scrollbar">
        {/* Online contacts */}
        {groupedContacts.online.length > 0 && (
          <div>
            <div className="px-4 py-2 bg-green-50 border-b border-green-100">
              <h3 className="text-sm font-medium text-green-700 flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                Online ({groupedContacts.online.length})
              </h3>
            </div>
            {groupedContacts.online.map(contact => (
              <ContactItem key={contact._id} contact={contact} />
            ))}
          </div>
        )}

        {/* Away contacts */}
        {groupedContacts.away.length > 0 && (
          <div>
            <div className="px-4 py-2 bg-yellow-50 border-b border-yellow-100">
              <h3 className="text-sm font-medium text-yellow-700 flex items-center gap-2">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                Away ({groupedContacts.away.length})
              </h3>
            </div>
            {groupedContacts.away.map(contact => (
              <ContactItem key={contact._id} contact={contact} />
            ))}
          </div>
        )}

        {/* Offline contacts */}
        {groupedContacts.offline.length > 0 && (
          <div>
            <div className="px-4 py-2 bg-gray-50 border-b border-gray-100">
              <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                Offline ({groupedContacts.offline.length})
              </h3>
            </div>
            {groupedContacts.offline.map(contact => (
              <ContactItem key={contact._id} contact={contact} />
            ))}
          </div>
        )}

        {/* Empty state */}
        {filteredContacts.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12 text-gray-500">
            <Users className="h-12 w-12 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {searchQuery ? "No contacts found" : "No contacts yet"}
            </h3>
            <p className="text-sm text-center">
              {searchQuery 
                ? "Try a different search term" 
                : "Add some friends to start chatting!"}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
