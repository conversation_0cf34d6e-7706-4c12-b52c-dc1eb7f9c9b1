import { ConvexHttpClient } from "convex/browser";
import { api } from "../convex/_generated/api";
import { CONVEX_URL } from "@/lib/config";

async function main() {
  const client = new ConvexHttpClient(CONVEX_URL);
  
  try {
    console.log("Running chat category migration...");
    const result = await client.mutation(api.migrations.fix_chat_categories, {});
    console.log(`Migration completed. Updated ${result.updatedCount} chats.`);
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  }
}

main();
