// convex/localNews.ts

import { internalMutation, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api"; // For internal mutations/queries
import { url } from "inspector";

export const createLocalNews = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    url: v.optional(v.string()),
    userId: v.id("users"),
    userName: v.string(),
    userProfileImage: v.optional(v.string()),
    localNewsChatroomId: v.id("chats"), // The ID of the dedicated "Local News" chatroom
  },
  handler: async (ctx, { title, description, url, userId, userName, userProfileImage, localNewsChatroomId }) => {
    // 1. Insert the news item into the localNews table
    const newsItemId = await ctx.db.insert("localNews", {
        title,
        description,
        content: description, // Using description as content since they seem to serve the same purpose
        url,
        userId,
        userName,
        userProfileImage,
        authorId: userId,
        isSystem: false,
        isNews: true,
    });

    // 2. Post a message to the "Local News" chatroom for visibility
    await ctx.scheduler.runAfter(0, internal.localNews.postNewsToChatroom, {
      newsItemId,
      chatroomId: localNewsChatroomId,
      userId, // The user who submitted it
      userName,
      userProfileImage,
    });

    return newsItemId;
  },
});

export const postNewsToChatroom = internalMutation({
  args: {
    newsItemId: v.id("localNews"),
    chatroomId: v.id("chats"),
    userId: v.id("users"),
    userName: v.string(),
    userProfileImage: v.optional(v.string()),
  },
  handler: async (ctx, { newsItemId, chatroomId, userId, userName, userProfileImage }) => {
    const newsItem = await ctx.db.get(newsItemId);
    if (!newsItem) {
      console.error("News item not found:", newsItemId);
      return;
    }

    const content = `📰 **Local News Update!** Submitted by ${userName}\n\n**${newsItem.title}**\n${newsItem.description}${newsItem.url ? `\n\nRead more: ${newsItem.url}` : ''}`;

    await ctx.db.insert("messages", {
      chatId: chatroomId,
      senderId: userId, // Use senderId instead of userId
      content: content,
      timestamp: Date.now(),
      isRead: false,
      reactions: [],
      // Add any other required fields from the messages schema
      // that don't have default values
    });
  },
});


export const getRecentLocalNews = query({
  args: {},
  handler: async (ctx) => {
    // Fetch the 5 most recent local news submissions
    return await ctx.db
      .query("localNews")
      .order("desc") // Order by _creationTime in descending order
      .take(5); // Get the top 5
  },
});

export const getLocalNewsById = query({
  args: { newsItemId: v.id("localNews") },
  handler: async (ctx, { newsItemId }) => {
    return await ctx.db.get(newsItemId);
  },
});