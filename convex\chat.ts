// convex/moola.ts OR convex/chatrooms.ts (adjust filename/imports as needed)
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id, Doc } from "./_generated/dataModel"; // Import Doc/Id
import { api } from "./_generated/api"; // Import api if needed

// Note: getChatRooms query likely belongs in chatrooms.ts or chats.ts
// --- CORRECTED: Get Chat Rooms by Category ---
export const getChatroomsByCategory = query({
  args: {
    category: v.optional( // Allow fetching all active if category is omitted
      v.union(
        v.literal("flirt"), 
        v.literal("teen"), 
        v.literal("grown-up"),
        v.literal("kingdom"), 
        v.literal("topical"), 
        v.literal("geographical"),
        v.literal("kasi-vibes"),
        v.literal("local-news"),
        v.literal("anonymous"),
        ),
    ),
    // Optional: Add filter for public/private?
    // includePrivate: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    // Start query, always filter by type: "chatroom"
    let queryBuilder = ctx.db
      .query("chats")
      .filter((q) => q.eq(q.field("type"), "chatroom"))
      // --- Filter by Status (if filtering out non-active) ---
      // Assuming you don't have an 'isActive' field, maybe filter by 'isPublic' or other criteria?
      // Example: Only show public rooms unless requested otherwise
      // .filter((q) => q.eq(q.field("isPublic"), true)) // Uncomment if needed
      .order("desc"); // Order by creation time

    // Apply category filter if provided
    if (args.category) {
      // Use index if available: .withIndex("by_type_and_category", q => ...)
      queryBuilder = queryBuilder.filter((q) => q.eq(q.field("category"), args.category));
    }

    const rooms = await queryBuilder.collect();

    // Get participant count efficiently
    const roomsWithStats = await Promise.all(
      rooms.map(async (room) => {
        // Use index to fetch participants
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", room._id))
          .collect();

        return {
          ...room,
          participantCount: participants.length,
        };
      }),
    );

    return roomsWithStats;
  },
});


// --- CORRECTED: Send Message (Likely belongs in messages.ts) ---
export const sendMessage = mutation({
  args: {
    chatId: v.id("chats"), // Correct table name reference
    content: v.string(),
    // Removed userId from args - use identity
    // Removed type - use boolean flags based on intended message type
    // Add args for other features like replies, multimix if needed
    listingRefId: v.optional(v.id("listings")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    const user = await ctx.db.query("users").withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject)).unique();
    if (!user) throw new Error("User not found");
    const userId = user._id;

    // Get chat details, including Moola cost if applicable
    const chat = await ctx.db.get(args.chatId);
    if (!chat) throw new Error("Chat room not found");

    // Determine Moola cost (default to 0 if not set or not applicable type)
    const moolaCost = chat.moolaPerMessage ?? 0; // Use nullish coalescing for default

    if (moolaCost > 0) {
        // Check balance only if there's a cost
        if (user.moola < moolaCost) {
            // Consider throwing a specific error type the client can catch
            throw new Error("INSUFFICIENT_MOOLA");
        }

        // --- Deduct Moola and record transaction ---
        const newBalance = user.moola - moolaCost;
        await ctx.db.patch(userId, {
            moola: newBalance,
            updatedAt: Date.now(),
        });

        await ctx.db.insert("moolaTransactions", {
            userId: userId,
            amount: -moolaCost, // Negative amount for cost
            type: "message_cost",
            description: `Message sent in ${chat.name || `Chat ${chat._id.substring(0, 5)}`}`,
            timestamp: Date.now(),
            relatedListingId: args.listingRefId, // Pass listing ref if available
            // relatedUserId: undefined, // Not applicable here
        });
        // --- End Moola Handling ---
    }


    // --- Insert the message ---
    const now = Date.now();
    const messageData = {
        chatId: args.chatId,
        senderId: userId, // Sender is the authenticated user
        content: args.content,
        timestamp: now,
        moolaCost: moolaCost > 0 ? moolaCost : undefined, // Store cost only if applicable
        listingRefId: args.listingRefId,
        // Set defaults based on schema for boolean flags
        isRead: false,
        isSystemMessage: false, // This is a user message
        isNudge: false, // Assuming default is false
        isVoiceNote: false,
        isMultimix: false,
        isDeleted: false,
        reactions: [], // Default empty array
        // voiceNoteUrl, voiceNoteDuration, multimixStyle, replyTo would come from args if implemented
    };

    const messageId = await ctx.db.insert("messages", messageData as any); // Use 'as any' temporarily if needed

    // --- Update chat's last message and timestamp ---
    try {
        await ctx.db.patch(args.chatId, {
            lastMessageId: messageId,
            updatedAt: now,
        });
    } catch (patchError) {
        console.error(`Failed to update chat ${args.chatId} after sending message ${messageId}: ${patchError}`);
        // Decide if this should cause the mutation to fail overall
    }

    // TODO: Increment unread counts for other participants in the chat

    return messageId;
  },
});


// --- CORRECTED: Purchase Moola (Likely belongs in moola.ts) ---
export const purchaseMoola = mutation({
  args: {
    // Removed userId - Use identity
    amount: v.number(), // Should validate amount > 0?
    // Payment details likely needed here in a real app (e.g., paymentIntentId)
    // paymentMethod: v.union(v.literal("sms"), v.literal("card")), // Keep if needed
    paymentMethod: v.string(), // More flexible? Or specific union based on providers
    transactionReference: v.optional(v.string()), // e.g., Stripe charge ID
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    const user = await ctx.db.query("users").withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject)).unique();
    if (!user) throw new Error("User not found");
    const userId = user._id;

    if (args.amount <= 0) {
        throw new Error("Purchase amount must be positive.");
    }

    // !! IMPORTANT !!
    // This mutation assumes payment verification has ALREADY happened externally
    // (e.g., via a webhook handler from Stripe/Paystack).
    // DO NOT grant Moola based solely on a client request like this without
    // server-side verification of successful payment.
    // A webhook handler would typically call this mutation AFTER confirming payment.

    // Add Moola to user's balance
    const newBalance = user.moola + args.amount;
    await ctx.db.patch(userId, {
      moola: newBalance,
      updatedAt: Date.now(),
    });

    // Record transaction
    await ctx.db.insert("moolaTransactions", {
      userId: userId,
      amount: args.amount, // Positive amount for purchase
      type: "purchase",
      description: `Purchased ${args.amount} Moola via ${args.paymentMethod}`,
      timestamp: Date.now(),
      // Store payment reference if available
      // relatedTransactionRef: args.transactionReference, // Add this field to schema if needed
    });

    console.log(`User ${userId} purchased ${args.amount} Moola. New balance: ${newBalance}`);
    return { success: true, newBalance: newBalance };
  },
});