import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { useAuth } from "@/hooks/use-auth"
import { toast } from "sonner"
import { X, Circle } from "lucide-react"

type GameData = {
  board: (string | null)[][];
  status: 'waiting' | 'in_progress' | 'completed';
  currentPlayer: 'x' | 'o';
  winningLine?: number[][];
}

type Game = {
  _id: Id<"games">;
  _creationTime: number;
  status: 'waiting' | 'in_progress' | 'completed';
  players: {
    x: Id<"users"> | null;
    o: Id<"users"> | null;
    currentPlayer: 'x' | 'o';
  };
  gameData: GameData;
  winner: Id<"users"> | 'draw' | null;
}

interface TicTacToeProps {
  gameId: Id<"games">
  onGameEnd?: () => void
}

export function TicTacToe({ gameId, onGameEnd }: TicTacToeProps) {
  const { userId } = useAuth()
  const game = useQuery(api.games.getGame, { gameId }) as Game | null
  const makeMove = useMutation(api.games.makeMove)
  
  const [localBoard, setLocalBoard] = useState<(string | null)[][]>([
    [null, null, null],
    [null, null, null],
    [null, null, null],
  ])
  const [isLocalTurn, setIsLocalTurn] = useState(false)
  const [gameStatus, setGameStatus] = useState<{
    isGameOver: boolean
    winner: string | null
    winningLine: number[][] | null
  }>({ isGameOver: false, winner: null, winningLine: null })

  // Update local state when game data changes
  useEffect(() => {
    console.log('--- GAME STATE UPDATE ---');
    
    // Safe way to log the game object
    try {
      const gameData = game ? {
        id: game._id,
        status: game.status,
        players: game.players,
        gameData: {
          ...game.gameData,
          // Avoid logging large board data
          board: game.gameData?.board ? '[[...]]' : null
        },
        winner: game.winner
      } : 'No game data';
      
      console.log('Game state:', gameData);
    } catch (error) {
      console.log('Error logging game state:', error);
    }
    
    if (game) {
      console.log('Current user ID:', userId);
      console.log('Game players:', {
        x: game.players?.x,
        o: game.players?.o,
        currentPlayer: game.players?.currentPlayer
      });
      
      const { board, status, currentPlayer } = game.gameData as {
        board: (string | null)[][];
        status: string;
        currentPlayer: 'x' | 'o';
      };
      
      console.log('Current player symbol:', currentPlayer);
      console.log('Player for current turn:', game.players?.[currentPlayer]);
      console.log('Game status:', status);
      
      // Update local board
      if (board) {
        console.log('Updating board:', board);
        setLocalBoard(board);
      }
      
      // Update game status
      if (status === 'completed') {
        console.log('Game completed, winner:', game.winner);
        setGameStatus({
          isGameOver: true,
          winner: game.winner || null,
          winningLine: (game.gameData as any).winningLine || null
        });
        onGameEnd?.();
      } else {
        // Check if it's the current user's turn
        const playerForCurrentTurn = game.players?.[currentPlayer];
        const isPlayerTurn = playerForCurrentTurn === userId;
        
        console.log('Turn check:', {
          currentPlayer,
          playerForCurrentTurn,
          userId,
          isPlayerTurn,
          players: game.players
        });
        
        setIsLocalTurn(isPlayerTurn);
        
        // If it's not the player's turn, log who's turn it should be
        if (!isPlayerTurn) {
          console.log('Not your turn. Current player is:', playerForCurrentTurn, 'You are:', userId);
        }
      }
    }
  }, [game, userId, onGameEnd])

  const handleCellClick = async (row: number, col: number) => {
    console.log('Cell clicked:', { row, col, isLocalTurn, isGameOver: gameStatus.isGameOver, cellValue: localBoard[row][col], userId });
    if (!isLocalTurn) {
      console.log('Not your turn');
      return;
    }
    if (gameStatus.isGameOver) {
      console.log('Game is over');
      return;
    }
    if (localBoard[row][col] !== null) {
      console.log('Cell already taken');
      return;
    }
    if (!userId) {
      console.log('No user ID');
      return;
    }

    try {
      await makeMove({
        gameId,
        playerId: userId as Id<"users">,
        move: { row, col }
      })
    } catch (error) {
      console.error("Error making move:", error)
      toast.error("Failed to make move. Please try again.")
    }
  }

  const renderCell = (row: number, col: number) => {
    const value = localBoard[row][col]
    const isWinningCell = gameStatus.winningLine?.some(
      ([r, c]) => r === row && c === col
    )
    const isDisabled = !isLocalTurn || gameStatus.isGameOver || value !== null;
    
    console.log(`Cell [${row},${col}]:`, { value, isWinningCell, isDisabled, isLocalTurn, isGameOver: gameStatus.isGameOver });

    return (
      <Button
        key={`${row}-${col}`}
        variant="outline"
        className={`h-20 w-20 text-2xl font-bold rounded-none 
          ${isWinningCell ? 'bg-green-100 dark:bg-green-900' : 'hover:bg-accent'}
          ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onClick={() => handleCellClick(row, col)}
        disabled={isDisabled}
      >
        {value === 'x' ? (
          <X className="h-8 w-8 text-red-500" />
        ) : value === 'o' ? (
          <Circle className="h-8 w-8 text-blue-500" />
        ) : null}
      </Button>
    )
  }

  const getStatusMessage = () => {
    if (!game) return "Loading game..."
    
    if (gameStatus.isGameOver) {
      if (gameStatus.winner === userId) return "You won! 🎉"
      if (gameStatus.winner === 'draw') return "It's a draw!"
      return "You lost! Better luck next time!"
    }
    
    return isLocalTurn ? "Your turn" : "Waiting for opponent..."
  }

  const getCurrentPlayerSymbol = () => {
    if (!game) return null
    const currentSymbol = game.players.x === userId ? 'X' : 'O'
    return (
      <div className="flex items-center gap-2">
        <span>You are:</span>
        {currentSymbol === 'X' ? (
          <X className="h-5 w-5 text-red-500" />
        ) : (
          <Circle className="h-5 w-5 text-blue-500" />
        )}
      </div>
    )
  }

  if (!game) {
    return (
      <div className="flex items-center justify-center p-8">
        <p>Loading game...</p>
      </div>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-2">
        <CardTitle className="text-center">Tic-Tac-Toe</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            {getStatusMessage()}
          </div>
          {!gameStatus.isGameOver && (
            <div className="text-sm text-muted-foreground">
              {getCurrentPlayerSymbol()}
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-3 gap-1 bg-foreground/10 p-1 rounded-lg">
          {[0, 1, 2].map((row) =>
            [0, 1, 2].map((col) => renderCell(row, col))
          )}
        </div>
        
        {gameStatus.isGameOver && (
          <div className="flex justify-center pt-2">
            <Button 
              variant="outline"
              onClick={() => {
                // Reset the game
                setLocalBoard([
                  [null, null, null],
                  [null, null, null],
                  [null, null, null],
                ])
                setGameStatus({ isGameOver: false, winner: null, winningLine: null })
              }}
            >
              Play Again
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
