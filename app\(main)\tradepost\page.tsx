"use client"

import { useState } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import Link from "next/link"
import { Id } from "@/convex/_generated/dataModel"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Search, Filter, Tag } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { TradepostListing } from "@/components/tradepost/tradepost-listing"

export default function TradepostPage() {
  const { userId } = useAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [activeTab, setActiveTab] = useState("browse")

  // Get all listings
  const allListings = useQuery(api.tradepost.getListings)

  // Get listings by seller
  const myListings = useQuery(
    api.tradepost.getListingsBySeller,
    userId ? { sellerId: userId as Id<"users"> } : "skip"
  )

  // Get favorite listings
  const favoriteListings = useQuery(
    api.tradepost.getFavoriteListings,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  // Filter listings based on search query and category
  const filteredListings = allListings?.filter((listing: any) => {
    if (!listing) return false

    const matchesSearch =
      searchQuery === "" ||
      (listing.title && listing.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (listing.description && listing.description.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === "" || listing.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  return (
    <div className="container py-6 md:py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Tradepost</h1>
          <p className="text-muted-foreground">Buy, sell, and trade items with other users</p>
        </div>
        <Link href="/tradepost/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Listing
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Filters Sidebar */}
        <div className="md:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search listings..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="fashion">Fashion</SelectItem>
                    <SelectItem value="home_garden">Home & Garden</SelectItem>
                    <SelectItem value="vehicles">Vehicles</SelectItem>
                    <SelectItem value="property">Property</SelectItem>
                    <SelectItem value="hobbies_toys">Hobbies & Toys</SelectItem>
                    <SelectItem value="books_music_games">Books, Music & Games</SelectItem>
                    <SelectItem value="services">Services</SelectItem>
                    <SelectItem value="jobs">Jobs</SelectItem>
                    <SelectItem value="pets">Pets</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setSearchQuery("")
                  setSelectedCategory("")
                }}
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="md:col-span-3">
          <Tabs defaultValue="browse" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="browse">Browse</TabsTrigger>
              {userId && <TabsTrigger value="my-listings">My Listings</TabsTrigger>}
              {userId && <TabsTrigger value="favorites">Favorites</TabsTrigger>}
            </TabsList>

            <TabsContent value="browse" className="space-y-4">
              {allListings === undefined ? (
                <ListingsLoadingSkeleton />
              ) : allListings.length === 0 ? (
                <EmptyState message="No listings found" actionLabel={undefined} actionHref={undefined} />
              ) : filteredListings?.length === 0 ? (
                <EmptyState message="No listings match your filters" actionLabel={undefined} actionHref={undefined} />
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredListings?.filter(Boolean).map((listing) => (
                    listing && (
                    <TradepostListing
                      key={listing._id}
                      id={listing._id}
                      title={listing.title}
                      description={listing.description}
                      price={listing.price}
                      imageUrl={(listing as any).imageUrl || ""}
                      location={listing.location}
                      category={listing.category}
                      sellerId={listing.sellerId}
                      sellerName={listing.sellerName}
                      sellerAvatar={listing.sellerAvatar}
                      createdAt={listing.createdAt}
                    />)
                  ))}
                </div>
              )}
            </TabsContent>

            {userId && (
              <TabsContent value="my-listings" className="space-y-4">
                {!myListings ? (
                  <ListingsLoadingSkeleton />
                ) : myListings.length === 0 ? (
                  <EmptyState
                    message="You haven't created any listings yet"
                    actionLabel="Create Listing"
                    actionHref="/tradepost/create"
                  />
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {myListings?.filter(Boolean).map((listing) => (
                      listing && (
                      <TradepostListing
                        key={listing._id}
                        id={listing._id}
                        title={listing.title}
                        description={listing.description}
                        price={listing.price}
                        imageUrl={(listing as any).imageUrl || ""}
                        location={listing.location}
                        category={listing.category}
                        sellerId={listing.sellerId}
                        sellerName="You"
                        sellerAvatar=""
                        createdAt={listing.createdAt}
                        isFavorite={false}
                      />)
                    ))}
                  </div>
                )}
              </TabsContent>
            )}

            {userId && (
              <TabsContent value="favorites" className="space-y-4">
                {!favoriteListings ? (
                  <ListingsLoadingSkeleton />
                ) : favoriteListings.length === 0 ? (
                  <EmptyState message="You haven't favorited any listings yet" actionLabel={undefined} actionHref={undefined} />
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {favoriteListings?.filter(Boolean).map((listing) => (
                      listing && (
                      <TradepostListing
                        key={listing._id}
                        id={listing._id}
                        title={listing.title}
                        description={listing.description}
                        price={listing.price}
                        imageUrl={(listing as any).imageUrl || ""}
                        location={listing.location}
                        category={listing.category}
                        sellerId={listing.sellerId}
                        sellerName={listing.sellerName}
                        sellerAvatar={listing.sellerAvatar}
                        createdAt={listing.createdAt}
                        isFavorite={true}
                      />)
                    ))}
                  </div>
                )}
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </div>
  )
}

// Empty State Component
function EmptyState({ message, actionLabel, actionHref }: { message: string; actionLabel?: string; actionHref?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-lg bg-muted/30">
      <Tag className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium">{message}</h3>
      {actionLabel && actionHref && (
        <Link href={actionHref} className="mt-4">
          <Button>{actionLabel}</Button>
        </Link>
      )}
    </div>
  )
}

// Loading Skeleton
function ListingsLoadingSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {[1, 2, 3, 4, 5, 6].map((i) => (
        <Card key={i} className="overflow-hidden">
          <Skeleton className="aspect-video w-full" />
          <CardContent className="p-4">
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-5 w-1/3 mb-2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full mt-1" />
          </CardContent>
          <CardFooter className="p-4 pt-0">
            <div className="flex items-center gap-2 w-full justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-6 rounded-full" />
                <Skeleton className="h-4 w-20" />
              </div>
              <Skeleton className="h-4 w-16" />
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

