"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { MoolaIcon } from "@/components/moola-icon"
import { MessageSquare, Heart, Share } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { useRouter } from "next/navigation"
import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/hooks/use-auth"
import { Id } from "@/convex/_generated/dataModel"

interface TradepostListingProps {
  id: string
  title: string
  description: string
  price: string
  imageUrl?: string
  location?: string
  category?: string
  sellerId: string
  sellerName: string
  sellerAvatar?: string
  createdAt: number
  isFavorite?: boolean
  
}

export function TradepostListing({
  title,
  description,
  price,
  imageUrl,
  location,
  category,
  sellerId,
  sellerName,
  sellerAvatar,
  createdAt,
  isFavorite = false,
}: TradepostListingProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { userId } = useAuth()
  const [isLiked, setIsLiked] = useState(isFavorite)
  const [isCheckingBalance, setIsCheckingBalance] = useState(false)

  const processTransaction = useMutation(api.moola.processMarketplaceTransaction)
  const getMoolaBalance = useQuery(api.moola.getMoolaBalance, userId ? { userId: userId as Id<"users"> } : "skip")
  const createDirectChat = useMutation(api.chats.createDirectChat)

  const handlePurchase = async () => {
    if (!userId) {
      toast({
        title: "Not logged in",
        description: "You need to be logged in to make a purchase",
        variant: "destructive",
      })
      return
    }

    try {
      setIsCheckingBalance(true)
      
      // Parse the price string to get a number
      const priceString = price.replace(/[^0-9.]/g, '')
      const priceNumber = parseFloat(priceString)

      if (isNaN(priceNumber)) {
        throw new Error("Invalid price format")
      }

      // Check if user has enough Moola
      const userBalance = getMoolaBalance || 0
      if (userBalance < priceNumber) {
        throw new Error(`Insufficient Moola balance. You need ${priceNumber - userBalance} more Moola to complete this purchase.`)
      }

      // Process the transaction
      await processTransaction({
        buyerId: userId as Id<"users">,
        sellerId: sellerId as Id<"users">,
        itemName: title,
        amount: priceNumber,
      })

      // Create or get existing direct chat with the seller
      const chatId = await createDirectChat({
        userId: userId as Id<"users">,
        otherUserId: sellerId as Id<"users">
      })

      toast({
        title: "Purchase Successful",
        description: `You have purchased "${title}" for ${price} Moola`,
      })

      // Navigate to the chat
      router.push(`/chat/${chatId}`)
    } catch (error) {
      toast({
        title: "Purchase Failed",
        description: error instanceof Error ? error.message : "Failed to complete purchase",
        variant: "destructive",
      })
    } finally {
      setIsCheckingBalance(false)
    }
  }

  const handleContactSeller = () => {
    router.push(`/chat/${sellerId}`)
  }

  const timeAgo = formatDistanceToNow(new Date(createdAt), { addSuffix: true })

  return (
    <Card className="overflow-hidden">
      {imageUrl && (
        <div className="h-48 overflow-hidden">
          <img src={imageUrl || "/placeholder.svg"} alt={title} className="w-full h-full object-cover" />
        </div>
      )}

      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{title}</CardTitle>
          <div className="flex items-center">
            <MoolaIcon className="h-4 w-4 text-yellow-500 mr-1" />
            <span className="font-bold">{price}</span>
          </div>
        </div>
        <div className="flex gap-2 mt-1">
          <Badge variant="outline">{category}</Badge>
          <Badge variant="outline">{location}</Badge>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <p className="text-sm text-muted-foreground">{description}</p>

        <div className="flex items-center mt-4">
          <Avatar className="h-8 w-8 mr-2">
            <AvatarImage src={sellerAvatar} />
            <AvatarFallback>{sellerName.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-medium">{sellerName}</p>
            <p className="text-xs text-muted-foreground">{timeAgo}</p>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between pt-2">
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => setIsLiked(!isLiked)}>
            <Heart className={`h-4 w-4 ${isLiked ? "fill-red-500 text-red-500" : ""}`} />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={handleContactSeller}>
            <MessageSquare className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Share className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleContactSeller}>
            Contact
          </Button>
          <Button 
            className="w-full" 
            onClick={handlePurchase}
            disabled={isCheckingBalance}
          >
            {isCheckingBalance ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : (
              'Buy Now'
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
