"use client"

import { useState } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import type { ChatroomCategory } from "@/lib/types"
import { Id } from "@/convex/_generated/dataModel"

export function useChatrooms() {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [category, setCategory] = useState<ChatroomCategory | null>(null)

  // Get all chatrooms
  const allChatrooms = useQuery(api.chatrooms.getAllChatrooms, {})
    category ? { category } : "skip"

  // Get user's chatrooms
  const userChatrooms = useQuery(api.chatrooms.getUserChatrooms, userId ? { userId: userId as Id<"users"> } : "skip")

  // Create a chatroom
  const createChatroomMutation = useMutation(api.chatrooms.createChatroom)

  // Join a chatroom
  const joinChatroomMutation = useMutation(api.chatrooms.joinChatroom)

  const isLoading = isAuthLoading || allChatrooms === undefined

  const createChatroom = async (data: {
    name: string
    description?: string
    isPrivate: boolean
    location?: string
    tags?: string[]
    category?: ChatroomCategory
    moolaPerMessage?: number
  }) => {
    if (!userId) throw new Error("You must be logged in to create a chatroom")
  
    return await createChatroomMutation({
      name: data.name,
      description: data.description,
      isPrivate: data.isPrivate,  // Changed from isPublic to isPrivate
      location: data.location,
      tags: data.tags,
      category: data.category || 'topical', // Provide a default category
      moolaPerMessage: data.moolaPerMessage,
      creatorId: userId as Id<"users">,
    })
  }

  const joinChatroom = async (chatroomId: string) => {
    if (!userId) throw new Error("You must be logged in to join a chatroom")

    return await joinChatroomMutation({
      chatroomId: chatroomId as Id<"chats">,
      userId: userId as Id<"users">,
    })
  }

  return {
    chatrooms: allChatrooms || [],
    userChatrooms: userChatrooms || [],
    isLoading,
    setCategory,
    createChatroom,
    joinChatroom,
  }
}

