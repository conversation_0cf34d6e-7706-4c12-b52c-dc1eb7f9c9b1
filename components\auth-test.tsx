"use client"

import { useState } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@clerk/nextjs"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function AuthTest() {
  const { userId: clerkId, isLoaded, isSignedIn } = useAuth()
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Simple Convex query that doesn't require auth
  const testPublicQuery = useQuery(api.test.publicQuery)

  // Convex query that requires auth
  const testAuthQuery = useQuery(api.test.authQuery, isSignedIn ? { clerkId: clerkId || "" } : "skip")

  const testAuth = async () => {
    setError(null)
    setSuccess(null)

    try {
      // Test Clerk auth
      const clerkResponse = await fetch("/api/test-auth")
      const clerkData = await clerkResponse.json()

      if (!clerkResponse.ok) {
        throw new Error(`Clerk auth failed: ${clerkData.error || "Unknown error"}`)
      }

      // If we get here, Clerk auth is working
      setSuccess(`Clerk auth working. User ID: ${clerkData.userId}`)

      // Check Convex auth
      if (testAuthQuery === undefined) {
        setError("Convex auth query returned undefined - likely an auth issue")
      } else if (testAuthQuery === null) {
        setError("Convex auth query returned null - check your query implementation")
      } else {
        setSuccess((prev) => `${prev}\nConvex auth working. Response: ${JSON.stringify(testAuthQuery)}`)
      }
    } catch (err) {
      setError(`Auth test failed: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Authentication Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p>Clerk Status: {isLoaded ? (isSignedIn ? "Signed In" : "Signed Out") : "Loading..."}</p>
          <p>Clerk User ID: {clerkId || "None"}</p>
          <p>Public Query Result: {testPublicQuery ? "Working" : "Not Working"}</p>
          <p>Auth Query Result: {testAuthQuery ? "Working" : "Not Working"}</p>
        </div>

        {error && <div className="p-4 bg-red-100 text-red-800 rounded-md">{error}</div>}

        {success && <div className="p-4 bg-green-100 text-green-800 rounded-md whitespace-pre-line">{success}</div>}

        <Button onClick={testAuth}>Test Authentication</Button>
      </CardContent>
    </Card>
  )
}

