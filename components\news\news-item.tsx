// components/news/NewsItem.tsx
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";

export function NewsItem({ 
  title, 
  content, 
  source, 
  url, 
  imageUrl,
  onComment 
}: { 
  title: string; 
  content: string;
  source: string;
  url: string;
  imageUrl?: string;
  onComment?: () => void;
}) {
  return (
    <Card className="mb-4">
      <CardHeader>
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-sm text-muted-foreground">Source: {source}</p>
      </CardHeader>
      <CardContent>
        {imageUrl && (
          <img 
            src={imageUrl} 
            alt={title} 
            className="w-full h-48 object-cover rounded mb-4"
          />
        )}
        <p>{content}</p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="ghost" asChild>
          <a href={url} target="_blank" rel="noopener noreferrer">
            Read More
          </a>
        </Button>
        <Button variant="outline" onClick={onComment}>
          💬 Discuss
        </Button>
      </CardFooter>
    </Card>
  );
}