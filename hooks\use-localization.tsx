"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useAuth } from "@/hooks/use-auth"

// Supported languages
export const LANGUAGES = {
  en: "English",
  af: "Afrikaans",
  zu: "isiZulu",
  xh: "isiXhosa",
}

// Translation dictionaries
const translations: Record<string, Record<string, string>> = {
  en: {
    "app.name": "XitChat",
    "chat.placeholder": "Type a message...",
    "chat.send": "Send",
    "chat.empty": "No messages yet. Start the conversation!",
    "chatrooms.title": "Chatrooms",
    "chatrooms.search": "Search chatrooms...",
    "chatrooms.create": "Create Chatroom",
    "chatrooms.join": "Join <PERSON>troom",
    "chatrooms.enter": "Enter Chatroom",
    "profile.title": "Profile",
    "profile.account": "Account",
    "profile.moola": "Moola",
    "profile.settings": "Settings",
    "moola.balance": "Moola Balance",
    "moola.purchase": "Purchase",
    "moola.gift": "Gift Moola",
    "settings.theme": "Theme",
    "settings.language": "Language",
    "settings.dataSaving": "Data Saving Mode",
  },
  af: {
    "app.name": "XitChat",
    "chat.placeholder": "Tik 'n boodskap...",
    "chat.send": "Stuur",
    "chat.empty": "Nog geen boodskappe nie. Begin die gesprek!",
    "chatrooms.title": "Kletskamers",
    "chatrooms.search": "Soek kletskamers...",
    "chatrooms.create": "Skep Kletskamer",
    "chatrooms.join": "Sluit aan by Kletskamer",
    "chatrooms.enter": "Gaan na Kletskamer",
    "profile.title": "Profiel",
    "profile.account": "Rekening",
    "profile.moola": "Moola",
    "profile.settings": "Instellings",
    "moola.balance": "Moola Balans",
    "moola.purchase": "Koop",
    "moola.gift": "Skenk Moola",
    "settings.theme": "Tema",
    "settings.language": "Taal",
    "settings.dataSaving": "Databesparing",
  },
  zu: {
    "app.name": "XitChat",
    "chat.placeholder": "Thayipha umlayezo...",
    "chat.send": "Thumela",
    "chat.empty": "Ayikho imilayezo okwamanje. Qala ingxoxo!",
    "chatrooms.title": "Amagumbi Okuxoxa",
    "chatrooms.search": "Sesha amagumbi okuxoxa...",
    "chatrooms.create": "Dala Igumbi Lokuxoxa",
    "chatrooms.join": "Joyina Igumbi Lokuxoxa",
    "chatrooms.enter": "Ngena Egumbini Lokuxoxa",
    "profile.title": "Iphrofayela",
    "profile.account": "I-akhawunti",
    "profile.moola": "Moola",
    "profile.settings": "Izilungiselelo",
    "moola.balance": "Ibhalansi ye-Moola",
    "moola.purchase": "Thenga",
    "moola.gift": "Nikela nge-Moola",
    "settings.theme": "Indlela yokubukeka",
    "settings.language": "Ulimi",
    "settings.dataSaving": "Indlela yokonga idatha",
  },
  xh: {
    "app.name": "XitChat",
    "chat.placeholder": "Chwetheza umyalezo...",
    "chat.send": "Thumela",
    "chat.empty": "Akukho myalezo okwangoku. Qala incoko!",
    "chatrooms.title": "Amagumbi Okuncokola",
    "chatrooms.search": "Khangela amagumbi okuncokola...",
    "chatrooms.create": "Yenza Igumbi Lokuncokola",
    "chatrooms.join": "Joyina Igumbi Lokuncokola",
    "chatrooms.enter": "Ngena Kwigumbi Lokuncokola",
    "profile.title": "Iprofayile",
    "profile.account": "I-akhawunti",
    "profile.moola": "Moola",
    "profile.settings": "Iisethingi",
    "moola.balance": "Ibhalansi ye-Moola",
    "moola.purchase": "Thenga",
    "moola.gift": "Nika i-Moola",
    "settings.theme": "Indlela yokubukeka",
    "settings.language": "Ulwimi",
    "settings.dataSaving": "Indlela yokonga idatha",
  },
}

type LocalizationContextType = {
  language: string
  setLanguage: (language: string) => void
  t: (key: string) => string
}

const LocalizationContext = createContext<LocalizationContextType | undefined>(undefined)

export function LocalizationProvider({ children }: { children: ReactNode }) {
  const { user, updateUserProfile } = useAuth()
  const [language, setLanguageState] = useState("en")

  useEffect(() => {
    // Load language preference from user profile or localStorage
    if (user?.language) {
      setLanguageState(user.language)
    } else {
      const savedLanguage = localStorage.getItem("xitchat-language")
      if (savedLanguage && Object.keys(LANGUAGES).includes(savedLanguage)) {
        setLanguageState(savedLanguage)
      } else {
        // Try to detect browser language
        const browserLang = navigator.language.split("-")[0]
        if (Object.keys(LANGUAGES).includes(browserLang)) {
          setLanguageState(browserLang)
        }
      }
    }
  }, [user])

  const setLanguage = (lang: string) => {
    if (!Object.keys(LANGUAGES).includes(lang)) {
      return
    }

    setLanguageState(lang)
    localStorage.setItem("xitchat-language", lang)

    // Update user profile if logged in
    if (user) {
      updateUserProfile({ language: lang }).catch(console.error)
    }
  }

  const t = (key: string): string => {
    return translations[language]?.[key] || translations.en[key] || key
  }

  return <LocalizationContext.Provider value={{ language, setLanguage, t }}>{children}</LocalizationContext.Provider>
}

export function useLocalization() {
  const context = useContext(LocalizationContext)
  if (context === undefined) {
    throw new Error("useLocalization must be used within a LocalizationProvider")
  }
  return context
}

