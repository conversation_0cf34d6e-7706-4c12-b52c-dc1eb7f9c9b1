import type { ReactNode } from "react"
import { Inter } from "next/font/google"
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs"
import { ConvexProvider } from "@/components/providers/convex-provider"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { AuthProvider } from "@/hooks/use-auth"
import { DataSavingProvider } from "@/hooks/use-data-saving"
import { Toaster } from "@/components/ui/toaster"
import { cn } from "@/lib/utils"
import { LocalizationProvider } from "@/hooks/use-localization"
import { DataModeProvider } from "@/hooks/use-data-mode"
import { QueryProvider } from "@/components/providers/query-provider"
import Script from "next/script"
import "./globals.css"
import "../styles/mxit-theme.css"
import { OnlineStatusHeartbeat } from "@/components/online-status-heartbeat"
import { PresenceUpdater } from "@/components/presence/presence-updater"
import { EnhancedStatusProvider } from "@/components/enhanced-status-provider"

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Safari-specific meta tags */}
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="XITchat" />
        <meta name="application-name" content="XITchat" />

        {/* iOS icons - multiple sizes for different devices */}
        <link rel="apple-touch-icon" href="/icon-512.svg" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icon-512.svg" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icon-512.svg" />
        <link rel="apple-touch-icon" sizes="167x167" href="/icon-512.svg" />

        {/* iOS splash screens */}
        <link rel="apple-touch-startup-image" href="/icon-512.svg" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" sizes="any" />

        {/* Web app capability */}
        <meta name="mobile-web-app-capable" content="yes" />

        {/* Prevent standalone mode redirects */}
        <meta name="apple-itunes-app" content="app-id=none, app-argument=none" />
      </head>
      <body className={cn("min-h-screen bg-background antialiased mxit-theme", inter.className)}>
        <ClerkProvider>
          <ConvexProvider>
            <AuthProvider>
              <ThemeProvider attribute="class" defaultTheme="light">
                <QueryProvider>
                  <DataSavingProvider>
                    <DataModeProvider>
                      <LocalizationProvider>
                        <EnhancedStatusProvider>
                          {children}
                          <Toaster />
                          <PresenceUpdater />
                        </EnhancedStatusProvider>
                      </LocalizationProvider>
                    </DataModeProvider>
                  </DataSavingProvider>
                </QueryProvider>
              </ThemeProvider>
            </AuthProvider>
          </ConvexProvider>
        </ClerkProvider>

        {/* PWA Service Worker Registration */}
        <Script id="register-sw" strategy="afterInteractive">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js').then(function(registration) {
                  console.log('ServiceWorker registration successful with scope: ', registration.scope);
                }, function(err) {
                  console.log('ServiceWorker registration failed: ', err);
                });
              });
            }
          `}
        </Script>
      </body>
    </html>
  )
}



export const metadata = {
  title: 'XITchat',
  description: 'A modern messaging app with Moola system',
  generator: 'v0.dev',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'XITchat',
    startupImage: [
      {
        url: '/icons/apple-splash-1125-2436.svg',
        media: '(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)'
      }
    ]
  },
  icons: {
    icon: '/favicon.ico',
    apple: '/icon-512.svg'
  }
};

export const viewport = {
  themeColor: '#4f46e5'
};

