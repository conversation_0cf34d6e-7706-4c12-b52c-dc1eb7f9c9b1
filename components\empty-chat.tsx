"use client"

import { MessageSquare } from "lucide-react"

export function EmptyChat() {
  return (
    <div className="flex h-full items-center justify-center">
      <div className="text-center space-y-4">
        <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center">
          <MessageSquare className="h-12 w-12 text-muted-foreground" />
        </div>
        <h3 className="text-xl font-medium">Select a conversation</h3>
        <p className="text-muted-foreground">Choose a contact from the list to start chatting.</p>
      </div>
    </div>
  )
}

