"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import type { ChatWithDetails, Message } from "@/lib/types"
import { useToast } from "@/components/ui/use-toast"
import type { Id } from "@/convex/_generated/dataModel"

export function useChats() {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()

  // Get all chats for the current user
  const chats = useQuery(api.chats.getChats, userId ? { userId: userId as Id<"users"> } : "skip")

  // Create a direct chat
  const createDirectChat = useMutation(api.chats.createDirectChat)

  // Create a group chat
  const createGroupChat = useMutation(api.chats.createGroupChat)

  // Remove a contact
  const removeContact = useMutation(api.contacts.removeContact)

  useEffect(() => {
    if (isAuthLoading) {
      setIsLoading(true)
    } else if (userId && chats === undefined) {
      setIsLoading(true)
    } else {
      setIsLoading(false)
    }
  }, [chats, isAuthLoading, userId])

  const startDirectChat = async (otherUserId: string) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to start a chat",
        variant: "destructive",
      })
      throw new Error("You must be logged in to start a chat")
    }

    try {
      console.log(`Starting direct chat between ${userId} and ${otherUserId}`)

      // Make sure we're passing the correct ID types
      const chatId = await createDirectChat({
        userId: userId as Id<"users">,
        otherUserId: otherUserId as Id<"users">,
      })

      console.log(`Chat created/found with ID: ${chatId}`)

      // Return the chat ID as a string
      return chatId
    } catch (err) {
      console.error("Error in startDirectChat:", err)
      toast({
        title: "Error",
        description: "Failed to start chat",
        variant: "destructive",
      })
      throw err
    }
  }

  const startGroupChat = async (name: string, participantIds: string[], description?: string) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to create a group",
        variant: "destructive",
      })
      throw new Error("You must be logged in to create a group")
    }

    try {
      return await createGroupChat({
        name,
        description,
        creatorId: userId as Id<"users">,
        participantIds: participantIds.map((id) => id as Id<"users">),
        isPublic: false,
      })
    } catch (err) {
      console.error("Error in startGroupChat:", err)
      toast({
        title: "Error",
        description: "Failed to create group chat",
        variant: "destructive",
      })
      throw err
    }
  }

  const removeContactFromList = async (contactId: string) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to remove a contact",
        variant: "destructive",
      })
      throw new Error("You must be logged in to remove a contact")
    }

    try {
      await removeContact({
        contactRelationshipId: contactId as Id<"contacts">,
      })
    } catch (err) {
      console.error("Error in removeContactFromList:", err)
      toast({
        title: "Error",
        description: "Failed to remove contact",
        variant: "destructive",
      })
      throw err
    }
  }

  return {
    chats: Array.isArray(chats)
      ? chats.map((chat) => ({
          ...chat,
          id: chat?._id, // Ensure id is set for compatibility
        }))
      : undefined,
    isLoading: isLoading || isAuthLoading,
    error,
    startDirectChat,
    startGroupChat,
    removeContactFromList,
  }
}

export function useChat(chatId: string | null) {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  // Get a specific chat by ID
  const chat = useQuery(
    api.chats.getChatById,
    chatId
      ? {
          chatId: chatId as Id<"chats">,
          userId: (userId as Id<"users">),
        }
      : "skip",
  )

  useEffect(() => {
    if (isAuthLoading || !chatId) {
      setIsLoading(true)
    } else if (chat !== undefined) {
      setIsLoading(false)
    }
  }, [chat, isAuthLoading, chatId])

  return {
    chat: chat as ChatWithDetails | null,
    isLoading: isLoading || isAuthLoading,
  }
}

// useMessages hook has been moved to hooks/use-messages.tsx
// Import it from there instead of using this implementation
