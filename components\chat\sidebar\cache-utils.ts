import { CacheInvalidationOptions } from "./types";

export async function clearBrowserCache(options: CacheInvalidationOptions = {}): Promise<void> {
  if (typeof window === "undefined" || !("caches" in window)) {
    return;
  }
  try {
    const { aggressive = false, cacheNamePrefix = "" } = options;
    const cacheNames = await caches.keys();
    const cachesToDelete = aggressive
      ? cacheNames
      : cacheNames.filter((cacheName) => cacheName.startsWith(cacheNamePrefix));
    
    if (cachesToDelete.length > 0) {
      await Promise.all(cachesToDelete.map((cacheName) => caches.delete(cacheName)));
    }
  } catch (e) {
    // console.error("Failed to clear caches:", e);
  }
}
