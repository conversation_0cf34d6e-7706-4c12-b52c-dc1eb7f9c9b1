import { NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';

// Initialize Convex client
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL || '');

// This is a webhook endpoint for payment confirmations
// In a real application, you would verify the webhook signature
export async function POST(request: Request) {
  try {
    // Get the webhook payload
    const payload = await request.json();
    
    // In a real implementation, you would:
    // 1. Verify the webhook signature
    // 2. Check the event type
    // 3. Process the payment confirmation
    
    // For this example, we'll assume the payload contains:
    // - userId: the user who made the purchase
    // - amount: the amount of Moola purchased
    // - paymentMethod: the payment method used
    // - transactionId: the payment processor's transaction ID
    
    const { userId, amount, paymentMethod, transactionId } = payload;
    
    if (!userId || !amount || !paymentMethod || !transactionId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Call the Convex mutation to credit the user's account
    await convex.mutation(api.moola.purchaseMoola, {
      userId,
      amount,
      paymentMethod,
      transactionReference: transactionId,
      paymentVerified: true // This is verified by the payment processor
    });
    
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully'
    });
    
  } catch (error) {
    console.error('Error processing payment webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    );
  }
}
