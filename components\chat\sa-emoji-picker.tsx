"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

interface SAEmojiPickerProps {
  onEmojiSelect: (emoji: string) => void
  className?: string
}

// South African specific emoji sets
const SA_EMOJI_SETS = {
  mxit: [
    ":-)", ":-(", ";-)", ":-D", ":-P", ":-*", ":-O", ":-|", 
    ":-S", ":-$", ":-@", ":-#", ":-!", ":-Z", ":-X", ":'(",
    ":P", ":D", ":)", ":(", ";)", ":o", ":|", ":s", ":$",
    ":@", ":#", ":!", ":z", ":x", ":'(", "XD", "=)", "=("
  ],
  
  sa_slang: [
    "🤙", "👌", "🔥", "💯", "🙌", "👏", "🤝", "✊", 
    "🤘", "🤞", "👍", "👎", "💪", "🧠", "❤️", "💚",
    "🖤", "💛", "🤍", "💙", "🧡", "💜", "🤎", "💗"
  ],
  
  animals: [
    "🦁", "🐘", "🦏", "🦒", "🦓", "🐆", "🐃", "🦌",
    "🐧", "🦅", "🐍", "🦎", "🐊", "🐢", "🦈", "🐋",
    "🐒", "🦍", "🐨", "🐼", "🐻", "🦘", "🐨", "🦔"
  ],
  
  food: [
    "🥩", "🍖", "🌭", "🍗", "🥓", "🍕", "🍔", "🌮",
    "🥪", "🍞", "🧀", "🥚", "🍳", "🥞", "🧇", "🥖",
    "🍺", "🍻", "🥂", "🍷", "🥃", "☕", "🍵", "🧃"
  ],
  
  activities: [
    "⚽", "🏉", "🏏", "🎾", "🏀", "🏐", "🏓", "🏸",
    "🥊", "🤼", "🏊", "🏄", "🚴", "🏃", "🧗", "🏇",
    "🎵", "🎶", "🎤", "🎸", "🥁", "🎺", "🎷", "🎹"
  ],
  
  flags: [
    "🇿🇦", "🏴", "🏳️", "🏁", "🚩", "🏳️‍🌈", "🏴‍☠️"
  ]
}

// Popular SA expressions and their meanings
const SA_EXPRESSIONS = [
  { emoji: "🤙", meaning: "Howzit" },
  { emoji: "🔥", meaning: "Lekker" },
  { emoji: "💯", meaning: "Sharp" },
  { emoji: "🙌", meaning: "Eish" },
  { emoji: "👌", meaning: "Boet" },
  { emoji: "🤝", meaning: "My bru" },
  { emoji: "🇿🇦", meaning: "Proudly SA" },
  { emoji: "⚽", meaning: "Bafana" }
]

export function SAEmojiPicker({ onEmojiSelect, className }: SAEmojiPickerProps) {
  const [activeTab, setActiveTab] = useState("mxit")
  const [showMeanings, setShowMeanings] = useState(false)

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji)
    
    // Add haptic feedback if available
    if (navigator.vibrate) {
      navigator.vibrate(25)
    }
  }

  return (
    <Card className={cn("w-full max-w-sm shadow-lg", className)}>
      <div className="p-3">
        <Tabs defaultValue="mxit" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6 mb-3">
            <TabsTrigger value="mxit" className="text-xs">MXit</TabsTrigger>
            <TabsTrigger value="sa_slang" className="text-xs">SA</TabsTrigger>
            <TabsTrigger value="animals" className="text-xs">🦁</TabsTrigger>
            <TabsTrigger value="food" className="text-xs">🥩</TabsTrigger>
            <TabsTrigger value="activities" className="text-xs">⚽</TabsTrigger>
            <TabsTrigger value="flags" className="text-xs">🇿🇦</TabsTrigger>
          </TabsList>

          <ScrollArea className="h-48">
            {Object.entries(SA_EMOJI_SETS).map(([category, emojis]) => (
              <TabsContent key={category} value={category} className="mt-0">
                <div className="grid grid-cols-8 gap-1">
                  {emojis.map((emoji, index) => (
                    <Button
                      key={`${emoji}-${index}`}
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-lg hover:bg-muted"
                      onClick={() => handleEmojiClick(emoji)}
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>
              </TabsContent>
            ))}
          </ScrollArea>

          {/* SA Expressions with meanings */}
          {activeTab === "sa_slang" && (
            <div className="mt-3 pt-3 border-t">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMeanings(!showMeanings)}
                className="w-full text-xs"
              >
                {showMeanings ? "Hide" : "Show"} SA Meanings
              </Button>
              
              {showMeanings && (
                <div className="mt-2 space-y-1">
                  {SA_EXPRESSIONS.map((expr, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between text-xs p-1 rounded hover:bg-muted cursor-pointer"
                      onClick={() => handleEmojiClick(expr.emoji)}
                    >
                      <span className="text-base">{expr.emoji}</span>
                      <span className="text-muted-foreground">{expr.meaning}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* MXit classic emoticons help */}
          {activeTab === "mxit" && (
            <div className="mt-3 pt-3 border-t">
              <div className="text-xs text-muted-foreground text-center">
                Classic MXit emoticons - just like the old days! 😊
              </div>
            </div>
          )}
        </Tabs>
      </div>
    </Card>
  )
}
