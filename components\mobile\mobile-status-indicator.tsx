"use client"

import { useOnlineUsers } from "@/hooks/use-online-users"
import { useAuth } from "@/hooks/use-auth"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { Smartphone } from "lucide-react"
import { useEffect, useState } from "react"

export function MobileStatusIndicator() {
  const { isUserOnline } = useOnlineUsers()
  const { userId } = useAuth()
  const [isOnline, setIsOnline] = useState(false)
  const [isVisible, setIsVisible] = useState(true)

  // Update online status when it changes
  useEffect(() => {
    if (userId) {
      const online = isUserOnline(userId)
      setIsOnline(online)
      
      // Auto-hide after 5 seconds if online
      if (online) {
        setIsVisible(true)
        const timer = setTimeout(() => setIsVisible(false), 5000)
        return () => clearTimeout(timer)
      }
    }
  }, [isUserOnline, userId])

  // Show the indicator when the user comes online
  useEffect(() => {
    if (isOnline) {
      setIsVisible(true)
      const timer = setTimeout(() => setIsVisible(false), 5000)
      return () => clearTimeout(timer)
    }
  }, [isOnline])

  // Don't show anything if not online or no user ID
  if (!isOnline || !userId) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 20, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className={cn(
            "fixed bottom-4 right-4 z-50 flex items-center gap-2",
            "bg-background/90 backdrop-blur-sm px-3 py-2 rounded-full",
            "border border-green-500/30 shadow-lg",
            "text-sm text-green-500 font-medium"
          )}
          onClick={() => setIsVisible(false)}
        >
          <div className="relative">
            <div className="absolute -inset-1.5 bg-green-500/20 rounded-full animate-ping" />
            <Smartphone className="h-4 w-4 relative z-10" />
          </div>
          <span>You're online</span>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
