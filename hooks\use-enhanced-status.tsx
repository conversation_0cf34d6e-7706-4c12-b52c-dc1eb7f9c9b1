"use client"

import { useEffect, useCallback, useRef } from "react"
import { useAuth } from "@/hooks/use-auth"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"

export function useEnhancedStatus() {
  const { userId, isAuthenticated } = useAuth()
  const updatePresence = useMutation(api.presence.updatePresence)
  const updateUserStatus = useMutation(api.users.updateUserStatus)
  
  // Track if user is currently active
  const isActiveRef = useRef(true)
  const lastActivityRef = useRef(Date.now())
  const statusUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Update user activity timestamp
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now()
    isActiveRef.current = true
  }, [])

  // Set user status
  const setStatus = useCallback(async (status: "online" | "away" | "offline") => {
    if (!userId || !isAuthenticated) return

    try {
      // Update both presence and user status
      await Promise.all([
        updatePresence({ status }),
        updateUserStatus({
          userId: userId as Id<"users">,
          status
        })
      ])
      
      console.log(`[Status] Updated to: ${status}`)
    } catch (error) {
      console.error("[Status] Failed to update status:", error)
    }
  }, [userId, isAuthenticated, updatePresence, updateUserStatus])

  // Check if user should be marked as away
  const checkAwayStatus = useCallback(() => {
    const now = Date.now()
    const timeSinceActivity = now - lastActivityRef.current
    const AWAY_THRESHOLD = 5 * 60 * 1000 // 5 minutes

    if (timeSinceActivity > AWAY_THRESHOLD && isActiveRef.current) {
      isActiveRef.current = false
      setStatus("away")
    }
  }, [setStatus])

  // Set up activity listeners
  useEffect(() => {
    if (!isAuthenticated || !userId) return

    const activityEvents = [
      'mousedown', 'mousemove', 'keypress', 'scroll', 
      'touchstart', 'click', 'focus'
    ]

    // Activity event handler
    const handleActivity = () => {
      updateActivity()
      
      // If user was away, bring them back online
      if (!isActiveRef.current) {
        isActiveRef.current = true
        setStatus("online")
      }
    }

    // Add activity listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true })
    })

    // Set initial online status
    setStatus("online")

    // Set up heartbeat to maintain online status
    heartbeatIntervalRef.current = setInterval(() => {
      if (isActiveRef.current) {
        setStatus("online")
      }
      checkAwayStatus()
    }, 30000) // Every 30 seconds

    // Clean up
    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity)
      })
      
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }
    }
  }, [isAuthenticated, userId, setStatus, updateActivity, checkAwayStatus])

  // Handle visibility change (tab focus/blur)
  useEffect(() => {
    if (!isAuthenticated || !userId) return

    const handleVisibilityChange = () => {
      // Clear any pending status updates
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current)
        statusUpdateTimeoutRef.current = null
      }

      if (document.hidden) {
        // User switched away from tab - set to away after delay
        statusUpdateTimeoutRef.current = setTimeout(() => {
          isActiveRef.current = false
          setStatus("away")
        }, 60000) // 1 minute delay
      } else {
        // User came back to tab - set to online immediately
        updateActivity()
        isActiveRef.current = true
        setStatus("online")
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current)
      }
    }
  }, [isAuthenticated, userId, setStatus, updateActivity])

  // Handle page unload (set offline)
  useEffect(() => {
    if (!isAuthenticated || !userId) return

    const handleBeforeUnload = () => {
      // Use sendBeacon for reliable offline status update
      if (navigator.sendBeacon && userId) {
        const data = new FormData()
        data.append('userId', userId)
        data.append('status', 'offline')
        navigator.sendBeacon('/api/update-status', data)
      }
    }

    const handlePageHide = () => {
      // Fallback for mobile browsers
      setStatus("offline")
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handlePageHide)
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handlePageHide)
      
      // Set offline when component unmounts
      setStatus("offline")
    }
  }, [isAuthenticated, userId, setStatus])

  // Manual status setters
  const goOnline = useCallback(() => {
    updateActivity()
    isActiveRef.current = true
    setStatus("online")
  }, [updateActivity, setStatus])

  const goAway = useCallback(() => {
    isActiveRef.current = false
    setStatus("away")
  }, [setStatus])

  const goOffline = useCallback(() => {
    isActiveRef.current = false
    setStatus("offline")
  }, [setStatus])

  return {
    goOnline,
    goAway,
    goOffline,
    updateActivity,
    isActive: isActiveRef.current
  }
}
