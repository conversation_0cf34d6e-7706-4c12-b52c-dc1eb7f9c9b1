// convex/contacts.ts

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// =============================================================================
// Helper Function - Get Authenticated User's Convex ID
// =============================================================================

/**
 * Retrieves the Convex Document ID (_id) for the currently authenticated user.
 * Throws an error if the user is not authenticated or not found in the users table.
 * @param ctx - The Convex context object (query or mutation context).
 * @returns Promise<Id<"users">> - The Convex ID of the authenticated user.
 */
const getAuthenticatedUserConvexId = async (
  ctx: any // Use 'any' for broader compatibility between QueryCtx and MutationCtx
): Promise<Id<"users">> => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("User must be authenticated.");
  }
  // Assumes identity.subject holds the ID from your auth provider (e.g., Clerk ID)
  const authProviderId = identity.subject;

  // Look up the user in your 'users' table using the auth provider ID
  // Requires an index named "by_clerk_id" on the 'clerkId' field of the 'users' table
  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", authProviderId))
    .unique();

  if (!user) {
    // This indicates a potential issue with user synchronization after authentication
    console.error(
      `Authenticated user issue: No user found in 'users' table for auth ID: ${authProviderId}`
    );
    throw new Error(
      `Authenticated user (${authProviderId}) not found in users table.`
    );
  }

  // Return the Convex Document ID (_id)
  return user._id;
};

// =============================================================================
// Mutations
// =============================================================================

/**
 * Adds a specified user as a contact for the currently authenticated user.
 * Updates the relationship if it already exists.
 */
export const addContact = mutation({
  args: {
    // The *Convex ID* of the user to add as a contact
    contactUserId: v.id("users"),
    nickname: v.optional(v.string()),
    isFavorite: v.optional(v.boolean()), // Optional: set favorite status on add
  },
  handler: async (ctx, args) => {
    // Get the Convex ID of the authenticated user performing the action
    const userConvexId = await getAuthenticatedUserConvexId(ctx);

    // Prevent adding self as contact
    if (userConvexId === args.contactUserId) {
      throw new Error("Cannot add yourself as a contact.");
    }

    // Optional but recommended: Verify the user being added actually exists
    const contactUserExists = await ctx.db.get(args.contactUserId);
    if (!contactUserExists) {
      throw new Error(
        `Cannot add contact: User with ID ${args.contactUserId} not found.`
      );
    }

    try {
      // Check if this contact relationship already exists using Convex IDs
      const existingContact = await ctx.db
        .query("contacts")
        .withIndex("by_user_and_contact", (q) =>
          q.eq("userId", userConvexId).eq("contactId", args.contactUserId)
        )
        .first();

      const now = Date.now();

      if (existingContact) {
        // Contact relationship exists, update it (e.g., nickname, favorite, unblock)
        await ctx.db.patch(existingContact._id, {
          nickname: args.nickname, // Update nickname if provided
          isFavorite: args.isFavorite ?? existingContact.isFavorite, // Update favorite if provided
          isBlocked: false, // Assume adding/re-adding unblocks
          updatedAt: now,
        });
        console.log(
          `Contact relationship updated: ${existingContact._id} for user ${userConvexId}`
        );
        return existingContact._id; // Return the ID of the existing relationship
      } else {
        // Contact relationship doesn't exist, create a new one
        const newContactRelationshipId = await ctx.db.insert("contacts", {
          userId: userConvexId, // Convex ID of the owner of this relationship
          contactId: args.contactUserId, // Convex ID of the user who is the contact
          nickname: args.nickname,
          isFavorite: args.isFavorite ?? false, // Default favorite to false
          isBlocked: false, // Default blocked to false
          createdAt: now,
          updatedAt: now,
        });
        console.log(
          `New contact relationship created: ${newContactRelationshipId} for user ${userConvexId}`
        );
        return newContactRelationshipId; // Return the ID of the new relationship
      }
    } catch (error) {
      console.error(
        `Error adding contact ${args.contactUserId} for user ${userConvexId}:`,
        error
      );
      // Check if it's a schema validation error before re-throwing a generic one
      if (
        error instanceof Error &&
        error.message.includes("does not match the schema")
      ) {
        console.error(
          "Schema validation failed. Check data types being inserted/patched."
        );
      }
      throw error; // Re-throw error for frontend handling
    }
  },
});

/**
 * Removes a specific contact relationship for the authenticated user.
 */
export const removeContact = mutation({
  args: {
    // The Convex ID of the *contact relationship document* itself (from the 'contacts' table)
    contactRelationshipId: v.id("contacts"),
  },
  handler: async (ctx, args) => {
    // Get the Convex ID of the authenticated user performing the action
    const userConvexId = await getAuthenticatedUserConvexId(ctx);

    try {
      // 1. Get the contact relationship document to verify ownership
      const contactToRemove = await ctx.db.get(args.contactRelationshipId);

      // 2. Check if the relationship document exists
      if (!contactToRemove) {
        console.warn(
          `Contact relationship ${args.contactRelationshipId} not found for removal by user ${userConvexId}. Assuming success.`
        );
        return true; // Return true as the desired state (relationship gone) is achieved
      }

      // 3. Verify Ownership: Ensure the authenticated user owns this relationship entry
      if (contactToRemove.userId !== userConvexId) {
        throw new Error(
          "Unauthorized: User cannot remove a contact relationship they do not own."
        );
      }

      // 4. Delete the relationship document if authorized
      await ctx.db.delete(args.contactRelationshipId);
      console.log(
        `Contact relationship ${args.contactRelationshipId} removed by user ${userConvexId}.`
      );
      return true; // Indicate success
    } catch (error) {
      console.error(
        `Error removing contact relationship ${args.contactRelationshipId} for user ${userConvexId}:`,
        error
      );
      if (error instanceof Error && error.message.includes("Unauthorized")) {
        throw error; // Re-throw specific auth errors
      }
      // Throw other unexpected errors
      throw new Error(`Failed to remove contact: ${error}`);
    }
  },
});

/**
 * Blocks or unblocks a specific contact relationship for the authenticated user.
 */
export const blockContact = mutation({
  args: {
    // The Convex ID of the *contact relationship document* itself
    contactRelationshipId: v.id("contacts"),
    isBlocked: v.boolean(), // The desired blocked state (true to block, false to unblock)
  },
  handler: async (ctx, args) => {
    // Get the Convex ID of the authenticated user performing the action
    const userConvexId = await getAuthenticatedUserConvexId(ctx);

    try {
      // 1. Get the contact relationship document
      const contactToModify = await ctx.db.get(args.contactRelationshipId);

      // 2. Check if the relationship document exists
      if (!contactToModify) {
        throw new Error(
          `Contact relationship ${args.contactRelationshipId} not found.`
        );
      }

      // 3. Verify Ownership
      if (contactToModify.userId !== userConvexId) {
        throw new Error(
          "Unauthorized: User cannot modify a contact relationship they do not own."
        );
      }

      // 4. Patch the document if authorized
      await ctx.db.patch(args.contactRelationshipId, {
        isBlocked: args.isBlocked,
        updatedAt: Date.now(),
        // Optional: Automatically un-favorite when blocking?
        // isFavorite: args.isBlocked ? false : contactToModify.isFavorite,
      });
      console.log(
        `Contact relationship ${args.contactRelationshipId} ${
          args.isBlocked ? "blocked" : "unblocked"
        } by user ${userConvexId}.`
      );
      return true; // Indicate success
    } catch (error) {
      console.error(
        `Error updating block status for contact relationship ${args.contactRelationshipId} by user ${userConvexId}:`,
        error
      );
      if (error instanceof Error && error.message.includes("Unauthorized")) {
        throw error; // Re-throw specific auth errors
      }
      throw new Error(`Failed to update contact block status: ${error}`);
    }
  },
});

// =============================================================================
// Queries
// =============================================================================

/**
 * Gets the list of contacts for the currently authenticated user.
 * Enriches the list with details of the contacted users.
 */
export const getUserContacts = query({
  args: {}, // No arguments needed, user is determined from authentication context
  handler: async (ctx) => {
    try {
      // Get the Convex ID of the authenticated user
      const userConvexId = await getAuthenticatedUserConvexId(ctx);

      // 1. Fetch all contact relationship documents owned by this user
      // Requires an index named "by_user" on the 'userId' field of the 'contacts' table
      const userContactRelationships = await ctx.db
        .query("contacts")
        .withIndex("by_user", (q) => q.eq("userId", userConvexId))
        .order("desc") // Optional: Order by creation date or name later
        .collect();

      // 2. Enrich each relationship with the actual user data of the contact
      const enrichedContacts = await Promise.all(
        userContactRelationships.map(async (relationship) => {
          // Get the user document for the person who IS the contact
          const contactUser = await ctx.db.get(relationship.contactId);

          // Handle case where the contacted user's account might have been deleted
          if (!contactUser) {
            console.warn(
              `User data not found for contactId: ${relationship.contactId} in relationship ${relationship._id}`
            );
            return null; // Skip this contact if the user doesn't exist
          }

          // Combine the contact user's data with the relationship details
          return {
            // Spread user fields (like _id, name, avatar, email, status etc.)
            ...contactUser,
            // Add relationship-specific fields
            nickname: relationship.nickname,
            isFavorite: relationship.isFavorite,
            isBlocked: relationship.isBlocked,
            // IMPORTANT: Add the ID of the relationship document itself for actions (remove/block)
            contactRelationshipId: relationship._id,
            // Note: contactUser._id is the ID of the *contacted user*
            //       relationship._id (aliased as contactRelationshipId) is the ID of the *contact entry*
          };
        })
      );

      // 3. Filter out any null results (where contacted user was deleted)
      return enrichedContacts.filter(Boolean);
    } catch (error) {
      // Log error but return empty array to prevent breaking frontend UI
      console.error("Error getting user contacts:", error);
      return [];
    }
  },
});

/**
 * Searches for users based on a query string (name or email).
 * Excludes the current user and indicates which results are already contacts.
 */
export const searchUsers = query({
  args: {
    searchQuery: v.string(), // The query string entered by the user
  },
  handler: async (ctx, args) => {
    // Get the Convex ID of the authenticated user performing the search
    const userConvexId = await getAuthenticatedUserConvexId(ctx);

    // Avoid searching on very short or empty queries
    const processedQuery = args.searchQuery.trim();
    if (processedQuery.length < 2) {
      return []; // Return empty if query is too short
    }

    try {
      // --- Basic Search (Consider Convex Search Index for better performance/features) ---
      // This uses basic filters which can be slow on large user bases.
      // A Convex Search Index on 'name' and 'email' is recommended for production.
      const potentialContacts = await ctx.db
        .query("users")
        // Filter 1: Exclude the user performing the search
        .filter((q) => q.neq(q.field("_id"), userConvexId))
        // Filter 2: Match name OR email (case-sensitive contains)
        .filter((q) =>
          q.or(
            // Adjust field names ("name", "email") if they are different in your 'users' table schema
            q.contains(q.field("name"), processedQuery),
            q.contains(q.field("email"), processedQuery)
          )
        )
        .take(15) // Limit the number of search results
        .collect();
      // --- End Basic Search ---

      // --- Check which results are already contacts ---
      if (potentialContacts.length === 0) {
        return []; // No users found matching search query
      }

      // Fetch all existing contact relationships for the authenticated user
      const existingContactRelationships = await ctx.db
        .query("contacts")
        .withIndex("by_user", (q) => q.eq("userId", userConvexId))
        .collect();

      // Create a Set of user IDs (Convex IDs) that are already contacts for quick lookup
      const existingContactUserIds = new Set(
        existingContactRelationships.map((rel) => rel.contactId)
      );
      // --- End Contact Check ---

      // Map search results and add the 'isContact' flag
      const searchResults = potentialContacts.map((user) => ({
        ...user, // Spread all fields from the user document
        isContact: existingContactUserIds.has(user._id), // True if user's ID is in the Set
      }));

      return searchResults;
    } catch (error) {
      console.error(
        `Error searching users with query "${args.searchQuery}" for user ${userConvexId}:`,
        error
      );
      return []; // Return empty array on error
    }
  },
});