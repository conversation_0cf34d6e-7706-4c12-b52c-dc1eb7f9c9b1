import { mutation } from "../_generated/server";

// This migration updates any chat with category 'kingdom' to 'kasi-vibes'
// and ensures all chats have a valid category

export default mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    const validCategories = [
      "kasi-vibes",
      "campus",
      "sports",
      "music",
      "flirt",
      "local-news",
      "anonymous",
      "teen",
      "grown-up",
      "topical"
    ];

    // Get all chats
    const allChats = await ctx.db.query("chats").collect();
    let updatedCount = 0;

    for (const chat of allChats) {
      // Skip if no category (optional field)
      if (!chat.category) continue;
      
      // If category is 'kingdom' or not in valid categories, update it
      if (chat.category === "kingdom" || !validCategories.includes(chat.category)) {
        console.log(`Updating chat ${chat._id} from '${chat.category}' to 'kasi-vibes'`);
        await ctx.db.patch(chat._id, {
          category: "kasi-vibes",
          updatedAt: now,
        });
        updatedCount++;
      }
    }

    return { updatedCount };
  },
});
