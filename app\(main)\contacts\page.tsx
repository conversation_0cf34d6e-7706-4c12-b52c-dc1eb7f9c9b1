"use client"

import { useState } from "react"
import { useContacts } from "@/hooks/use-contacts"
import { useChats } from "@/hooks/use-chats"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Search, UserPlus, MessageSquare, MoreHorizontal, UserMinus, UserX } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"

export default function ContactsPage() {
  const router = useRouter()
  const { contacts, searchResults, searchQuery, setSearchQuery, addContact, removeContact, blockContact, isLoading } =
    useContacts()
  const { startDirectChat } = useChats()
  const [activeTab, setActiveTab] = useState("contacts")

  const handleStartChat = async (contactId: string) => {
    try {
      console.log(`Starting chat with contact: ${contactId}`)
      const chatId = await startDirectChat(contactId)

      if (chatId) {
        console.log(`Successfully created/found chat: ${chatId}, navigating...`)
        router.push(`/chat/${chatId}`)
      } else {
        console.error("No chat ID returned from startDirectChat")
        toast({
          title: "Error",
          description: "Failed to start chat - no chat ID returned",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error starting chat:", error)
      toast({
        title: "Error",
        description: "Failed to start chat",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-5xl">
      <Card>
        <CardHeader>
          <CardTitle>Contacts</CardTitle>
          <CardDescription>Manage your contacts and find new people to chat with</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="contacts" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="contacts">
                My Contacts
                {contacts.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {contacts.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="find">Find People</TabsTrigger>
            </TabsList>

            <TabsContent value="contacts" className="mt-4">
              {contacts.length === 0 ? (
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium">No contacts yet</h3>
                  <p className="text-muted-foreground mt-1">Switch to "Find People" tab to add your first contact</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {contacts.map((contact) => (
                    <div key={contact._id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={contact.avatar} />
                          <AvatarFallback>{contact.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{contact.nickname || contact.name}</p>
                          <p className="text-sm text-muted-foreground">{contact.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="icon" onClick={() => handleStartChat(contact._id)}>
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => removeContact(contact.contactId)}>
                              <UserMinus className="h-4 w-4 mr-2" />
                              Remove Contact
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => blockContact(contact.contactId, !contact.isBlocked)}>
                              <UserX className="h-4 w-4 mr-2" />
                              {contact.isBlocked ? "Unblock Contact" : "Block Contact"}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="find" className="mt-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name or email..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {searchQuery.length < 2 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Type at least 2 characters to search for users</p>
                  </div>
                ) : searchResults.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No users found matching "{searchQuery}"</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {searchResults.map((user) => (
                      <div key={user._id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                          </div>
                        </div>
                        <Button
                          variant={user.isContact ? "outline" : "default"}
                          size="sm"
                          onClick={() => !user.isContact && addContact(user._id)}
                          disabled={user.isContact}
                        >
                          {user.isContact ? (
                            "Added"
                          ) : (
                            <>
                              <UserPlus className="h-4 w-4 mr-2" />
                              Add Contact
                            </>
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push("/chat")}>
            Back to Chat
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

