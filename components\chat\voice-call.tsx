"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog" // Import DialogTitle
import { <PERSON>, Mic, <PERSON>c<PERSON><PERSON>, PhoneOff } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"

interface VoiceCallProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  recipientId: string
  recipientName: string
  recipientAvatar?: string
  isIncoming?: boolean
  onAccept?: () => void
  onReject?: () => void
}

export function VoiceCall({
  open,
  onOpenChange,
  recipientId,
  recipientName,
  recipientAvatar,
  isIncoming = false,
  onAccept,
  onReject,
}: VoiceCallProps) {
  const { toast } = useToast()
  const [isMuted, setIsMuted] = useState(false)
  const [isCallConnected, setIsCallConnected] = useState(!isIncoming)
  const [callDuration, setCallDuration] = useState(0)
  const audioStreamRef = useRef<MediaStream | null>(null)

  // Handle microphone access
  useEffect(() => {
    if (open) {
      const startMicrophone = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          })

          audioStreamRef.current = stream

          // In a real app, you would connect to a WebRTC service here
          // For demo purposes, we'll simulate a connected call after 2 seconds
          if (!isIncoming) {
            setTimeout(() => {
              setIsCallConnected(true)
            }, 2000)
          }

          return stream
        } catch (error) {
          console.error("Error accessing microphone:", error)
          toast({
            title: "Microphone access denied",
            description: "Please allow microphone access to make voice calls",
            variant: "destructive",
          })
        }
      }

      const streamPromise = startMicrophone()

      // Cleanup function
      return () => {
        streamPromise.then((stream) => {
          if (stream) {
            stream.getTracks().forEach((track) => track.stop())
          }
        })
      }
    }
  }, [open, isIncoming, toast])

  // Call timer
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isCallConnected) {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isCallConnected])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleAcceptCall = () => {
    setIsCallConnected(true)
    onAccept?.()
  }

  const handleRejectCall = () => {
    onReject?.()
    onOpenChange(false)
  }

  const handleEndCall = () => {
    onOpenChange(false)
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)

    // In a real app, you would update the audio track here
    if (audioStreamRef.current) {
      audioStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = isMuted // Toggle the current state
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden bg-gray-900 text-white">
        <DialogTitle className="sr-only">Voice Call</DialogTitle>  {/* ADDED: Hidden Title for Screen Readers */}
        <div className="relative h-[60vh] sm:h-[40vh] flex flex-col items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 left-4 text-white"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-6 w-6" />
          </Button>

          <div className="text-center">
            <Avatar className="h-24 w-24 mx-auto mb-6">
              <AvatarImage src={recipientAvatar} alt={recipientName} />
              <AvatarFallback className="text-4xl">{recipientName.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <h2 className="text-xl font-semibold mb-2">{recipientName}</h2>
            {isCallConnected ? (
              <p className="text-gray-400 mb-8">{formatDuration(callDuration)}</p>
            ) : (
              <p className="text-gray-400 mb-8">{isIncoming ? "Incoming call..." : "Calling..."}</p>
            )}
          </div>

          {/* Call controls */}
          <div className="flex justify-center gap-4 mt-auto mb-8">
            {isIncoming && !isCallConnected ? (
              <>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleRejectCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-green-600 hover:bg-green-700"
                  onClick={handleAcceptCall}
                >
                  <Mic className="h-6 w-6" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-12 w-12 rounded-full bg-gray-800 border-gray-700"
                  onClick={toggleMute}
                >
                  {isMuted ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleEndCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}