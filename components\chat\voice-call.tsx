"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog" // Import DialogTitle
import { X, Mic, <PERSON>c<PERSON><PERSON>, PhoneOff } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"

interface VoiceCallProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  recipientId: string
  recipientName: string
  recipientAvatar?: string
  isIncoming?: boolean
  onAccept?: () => void
  onReject?: () => void
}

export function VoiceCall({
  open,
  onOpenChange,
  recipientId,
  recipientName,
  recipientAvatar,
  isIncoming = false,
  onAccept,
  onReject,
}: VoiceCallProps) {
  const { toast } = useToast()
  const [isMuted, setIsMuted] = useState(false)
  const [isCallConnected, setIsCallConnected] = useState(!isIncoming)
  const [callDuration, setCallDuration] = useState(0)
  const [hasMicAccess, setHasMicAccess] = useState(false)
  const [micError, setMicError] = useState<string | null>(null)
  const audioStreamRef = useRef<MediaStream | null>(null)

  // Handle microphone access
  useEffect(() => {
    if (open) {
      const startMicrophone = async () => {
        try {
          // First check if we already have permission
          const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName })
          
          if (permissionStatus.state === 'denied') {
            throw new Error('Microphone access was previously denied')
          }
          
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          })

          audioStreamRef.current = stream
          setHasMicAccess(true)
          setMicError(null)

          // In a real app, you would connect to a WebRTC service here
          // For demo purposes, we'll simulate a connected call after 2 seconds
          if (!isIncoming) {
            setTimeout(() => {
              setIsCallConnected(true)
            }, 2000)
          }

          return stream
        } catch (error) {
          console.error("Error accessing microphone:", error)
          const errorMessage = error instanceof Error ? error.message : 'Failed to access microphone'
          setMicError(errorMessage)
          setHasMicAccess(false)
          
          // Only show toast for permission-related errors
          if (error instanceof DOMException && error.name === 'NotAllowedError') {
            toast({
              title: "Microphone access denied",
              description: "Please allow microphone access in your browser settings to make voice calls",
              variant: "destructive",
              action: (
                <Button variant="outline" size="sm" onClick={() => window.open('chrome://settings/content/microphone', '_blank')}>
                  Open Settings
                </Button>
              ),
            })
          } else {
            toast({
              title: "Microphone error",
              description: errorMessage,
              variant: "destructive",
            })
          }
        }
      }

      const streamPromise = startMicrophone()

      // Cleanup function
      return () => {
        streamPromise.then((stream) => {
          if (stream) {
            stream.getTracks().forEach((track) => track.stop())
          }
        })
      }
    }
  }, [open, isIncoming, toast])

  // Call timer
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isCallConnected) {
      interval = setInterval(() => {
        setCallDuration((prev) => prev + 1)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isCallConnected])

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleAcceptCall = () => {
    setIsCallConnected(true)
    onAccept?.()
  }

  const handleRejectCall = () => {
    onReject?.()
    onOpenChange(false)
  }

  const handleEndCall = () => {
    onOpenChange(false)
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)

    // In a real app, you would update the audio track here
    if (audioStreamRef.current) {
      audioStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = isMuted // Toggle the current state
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] p-0 overflow-hidden bg-gray-900 text-white">
        <DialogTitle className="sr-only">Voice Call with {recipientName}</DialogTitle>
        {micError && (
          <div className="bg-red-900/80 text-red-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Microphone access required</p>
                <p className="text-sm mt-1 opacity-90">{micError}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-white border-red-700 hover:bg-red-800/50"
                onClick={async () => {
                  try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
                    stream.getTracks().forEach(track => track.stop())
                    setMicError(null)
                    setHasMicAccess(true)
                  } catch (error) {
                    console.error("Still can't access microphone:", error)
                  }
                }}
              >
                Try Again
              </Button>
            </div>
          </div>
        )}
        <div className="relative h-[60vh] sm:h-[40vh] flex flex-col items-center justify-center p-6">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 left-4 text-white"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-6 w-6" />
          </Button>

          <div className="text-center">
            <Avatar className="h-24 w-24 mx-auto mb-6">
              <AvatarImage src={recipientAvatar} alt={recipientName} />
              <AvatarFallback className="text-4xl">{recipientName.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <h2 className="text-xl font-semibold mb-2">{recipientName}</h2>
            {isCallConnected ? (
              <p className="text-gray-400 mb-8">{formatDuration(callDuration)}</p>
            ) : (
              <p className="text-gray-400 mb-8">{isIncoming ? "Incoming call..." : "Calling..."}</p>
            )}
          </div>

          {/* Call controls */}
          <div className="flex justify-center gap-4 mt-auto mb-8">
            {isIncoming && !isCallConnected ? (
              <>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleRejectCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-green-600 hover:bg-green-700"
                  onClick={handleAcceptCall}
                >
                  <Mic className="h-6 w-6" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-12 w-12 rounded-full bg-gray-800 border-gray-700"
                  onClick={toggleMute}
                >
                  {isMuted ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleEndCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}