// src/components/chat/message-display.tsx (or similar, conceptual)

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { NewsItem } from "@/components/news/news-item";
import { formatRelative } from "date-fns"; // Example for date formatting
import { useConvexUser } from "@/hooks/use-convex-auth";
import { LoadingSpinner } from "../loading-spinner";

interface MessageProps {
  message: {
    _id: Id<"messages">;
    _creationTime: number;
    userId: Id<"users">;
    content: string;
    messageType?: string; // "text" | "news_item"
    newsItemId?: Id<"localNews">; // Only for news_item type
    // ... other message fields like `user` (if you denormalize or join)
  };
  currentUserIsSender: boolean;
}

// Assume you have a component to display a regular chat message
const RegularChatMessage = ({ message, currentUserIsSender }: any) => (
  <div className={`flex gap-2 p-2 ${currentUserIsSender ? 'justify-end' : 'justify-start'}`}>
    <div className={`max-w-[70%] rounded-lg p-3 ${currentUserIsSender ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`}>
      <p className="text-sm">{message.content}</p>
      <span className="text-xs text-gray-400 mt-1 block">
        {formatRelative(new Date(message._creationTime), new Date())}
      </span>
    </div>
  </div>
);


export function MessageList({ chatroomId }: { chatroomId: Id<"chats"> }) {
  const messages = useQuery(api.messages.getMessagesInChatroom, { chatroomId });
  const { userId: currentUserId } = useConvexUser(); // Get current user's ID

  if (!messages) return <LoadingSpinner />;

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.map((message) => {
        const currentUserIsSender = message.userId === currentUserId;

        if (message.messageType === "news_item" && message.newsItemId) {
          // Fetch the actual news item content for rich display
          const newsItem = useQuery(api.localNews.getLocalNewsById, { newsItemId: message.newsItemId });

          if (!newsItem) {
            return (
              <RegularChatMessage
                key={message._id}
                message={{ ...message, content: "Loading news item..." }}
                currentUserIsSender={currentUserIsSender}
              />
            );
          }

          return (
            <div key={message._id} className="my-4 p-3 border rounded-lg bg-white shadow-sm">
              <p className="text-xs text-gray-500 mb-2">
                Submitted by {newsItem.userName} {formatRelative(new Date(newsItem._creationTime), new Date())}
              </p>
              <NewsItem
                title={newsItem.title}
                description={newsItem.description}
                url={newsItem.url}
                source={`Submitted by ${newsItem.userName}`}
                publishedAt={new Date(newsItem._creationTime).toLocaleString()}
                imageUrl={newsItem.userProfileImage}
              />
            </div>
          );
        }

        // Default to regular chat message rendering
        return (
          <RegularChatMessage
            key={message._id}
            message={message}
            currentUserIsSender={currentUserIsSender}
          />
        );
      })}
    </div>
  );
}