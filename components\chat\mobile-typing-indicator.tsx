"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface MobileTypingIndicatorProps {
  users: Array<{
    id: string
    name: string
    avatar?: string
  }>
  className?: string
}

export function MobileTypingIndicator({ users, className }: MobileTypingIndicatorProps) {
  if (users.length === 0) return null

  const displayText = users.length === 1 
    ? `${users[0].name} is typing...`
    : users.length === 2
    ? `${users[0].name} and ${users[1].name} are typing...`
    : `${users[0].name} and ${users.length - 1} others are typing...`

  return (
    <div className={cn("flex items-center gap-2 px-3 py-2", className)}>
      {/* Show avatar for single user */}
      {users.length === 1 && (
        <Avatar className="h-6 w-6">
          <AvatarImage src={users[0].avatar} alt={users[0].name} />
          <AvatarFallback className="text-xs">
            {users[0].name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}

      {/* Typing bubble */}
      <div className="bg-muted rounded-2xl rounded-bl-md px-3 py-2 flex items-center gap-2">
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
      </div>

      {/* Typing text */}
      <span className="text-xs text-muted-foreground">
        {displayText}
      </span>
    </div>
  )
}
