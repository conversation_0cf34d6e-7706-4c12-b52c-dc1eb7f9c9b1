// "use client"

// import type React from "react"
// import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
// import { Button } from "@/components/ui/button"
// import { Users, MapPin, Tag, Lock } from "lucide-react"
// import { cn } from "@/lib/utils"
// import { formatDistanceToNow } from "date-fns"
// import { Badge } from "@/components/ui/badge"
// import { MoolaIcon } from "@/components/moola-icon"
// import { categories } from "./chatroom-categories"

// interface ChatroomCardProps {
//   id: string
//   name: string
//   description?: string
//   memberCount: number
//   thumbnailUrl?: string
//   location?: string
//   tags?: string[]
//   category?: string
//   isAnonymous?: boolean
//   createdAt: number
//   isPrivate: boolean
//   moolaPerMessage?: number
//   onJoin: (id: string) => void
//   isMember?: boolean
// }

// const ChatroomCard: React.FC<ChatroomCardProps> = ({
//   id,
//   name,
//   description,
//   memberCount,
//   thumbnailUrl,
//   location,
//   tags,
//   category,
//   isAnonymous = false,
//   createdAt,
//   isPrivate,
//   moolaPerMessage = 0,
//   onJoin,
//   isMember = false,
// }) => {
//   const timeAgo = formatDistanceToNow(new Date(createdAt), { addSuffix: true })
//   const categoryInfo = category ? categories.find(c => c.id === category) : null

//   return (
//     <Card className={cn(
//       "overflow-hidden transition-all duration-300 hover:shadow-md border-2 flex flex-col h-full",
//       isMember ? "border-primary/50" : "border-transparent",
//     )}>
//       {/* Thumbnail Section */}
//       <div className="relative h-32">
//         {thumbnailUrl ? (
//           <div 
//             className="h-full w-full bg-cover bg-center" 
//             style={{ backgroundImage: `url(${thumbnailUrl})` }} 
//           />
//         ) : (
//           <div className="h-full w-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
//             <span className="text-2xl font-bold text-primary/70">{name.charAt(0)}</span>
//           </div>
//         )}

//         {/* Category Badge */}
//         {categoryInfo && (
//           <div className="absolute top-2 left-2 flex items-center gap-1">
//             <Badge 
//               className={cn(
//                 "text-xs font-medium px-2 py-0.5 flex items-center gap-1",
//                 categoryInfo.ageRestriction >= 18 
//                   ? "bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300" 
//                   : "bg-primary/10"
//               )}
//             >
//               <span>{categoryInfo.icon}</span>
//               <span>{categoryInfo.name}</span>
//               {categoryInfo.ageRestriction > 0 && (
//                 <span className="text-[10px] opacity-75">{categoryInfo.ageRestriction}+</span>
//               )}
//             </Badge>
//           </div>
//         )}

//         {/* Privacy & Moola Badges */}
//         <div className="absolute top-2 right-2 flex flex-col items-end gap-1">
//           {isPrivate && (
//             <Badge variant="secondary" className="text-xs px-2 py-0.5">
//               <Lock className="h-3 w-3 mr-1" />
//               Private
//             </Badge>
//           )}
//           {moolaPerMessage > 0 && (
//             <Badge variant="secondary" className="text-xs px-2 py-0.5">
//               <MoolaIcon className="h-3 w-3 mr-1 text-yellow-500" />
//               {moolaPerMessage} per message
//             </Badge>
//           )}
//         </div>
//       </div>

//       {/* Content Section */}
//       <CardContent className="p-4 flex-1 flex flex-col">
//         <div className="space-y-2">
//           {/* Title & Member Count */}
//           <div className="flex justify-between items-start">
//             <h3 className="font-semibold text-lg line-clamp-2">
//               {isAnonymous ? `🔒 ${name}` : name}
//             </h3>
//             <div className="flex items-center gap-1 text-sm text-muted-foreground ml-2 whitespace-nowrap">
//               <Users className="h-4 w-4" />
//               <span>{memberCount.toLocaleString()}</span>
//             </div>
//           </div>

//           {/* Description */}
//           {description && (
//             <p className="text-sm text-muted-foreground line-clamp-2">
//               {isAnonymous ? '🔒 Chat anonymously in this private space' : description}
//             </p>
//           )}

//           {/* Location */}
//           {location && (
//             <div className="flex items-center text-xs text-muted-foreground pt-1">
//               <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
//               <span className="truncate">{location}</span>
//             </div>
//           )}

//           {/* Tags */}
//           {tags && tags.length > 0 && (
//             <div className="flex flex-wrap gap-1 pt-2">
//               {tags.slice(0, 3).map((tag) => (
//                 <div key={tag} className="bg-muted text-xs rounded-full px-2 py-0.5 flex items-center">
//                   <Tag className="h-2 w-2 mr-1" />
//                   {tag}
//                 </div>
//               ))}
//               {tags.length > 3 && (
//                 <div className="bg-muted text-xs rounded-full px-2 py-0.5">+{tags.length - 3} more</div>
//               )}
//             </div>
//           )}

//           {/* Anonymous Badge */}
//           {isAnonymous && (
//             <div className="pt-2">
//               <Badge variant="outline" className="text-xs">
//                 🎭 Anonymous Chat
//               </Badge>
//             </div>
//           )}
//         </div>
//       </CardContent>

//       {/* Footer with Join Button */}
//       <CardFooter className="p-4 pt-0 mt-auto">
//         <Button 
//           variant={isMember ? "secondary" : "default"} 
//           size="sm" 
//           className="w-full font-medium" 
//           onClick={() => onJoin(id)}
//         >
//           {isMember 
//             ? isAnonymous ? "Enter Anonymously" : "Enter Chatroom" 
//             : isAnonymous ? "Join Anonymously" : "Join Chatroom"}
//         </Button>
//       </CardFooter>
//     </Card>
//   )
// }                                

// export default ChatroomCard
