"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogTitle } from "@/components/ui/dialog" // Import DialogTitle
import { X, Mic, MicOff, Video, VideoOff, PhoneOff } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"

interface VideoCallProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  recipientId: string
  recipientName: string
  recipientAvatar?: string
  isIncoming?: boolean
  onAccept?: () => void
  onReject?: () => void
}

export function VideoCall({
  open,
  onOpenChange,
  recipientId,
  recipientName,
  recipientAvatar,
  isIncoming = false,
  onAccept,
  onReject,
}: VideoCallProps) {
  const { toast } = useToast()
  const [isMuted, setIsMuted] = useState(false)
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isCallConnected, setIsCallConnected] = useState(!isIncoming)
  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  // Handle camera access
  useEffect(() => {
    if (open && isVideoOn) {
      const startCamera = async () => {
        try {
          // First try to get both video and audio
          const stream = await navigator.mediaDevices
            .getUserMedia({
              video: true,
              audio: !isMuted,
            })
            .catch(async () => {
              // If video fails, try audio only
              setIsVideoOn(false)
              return await navigator.mediaDevices.getUserMedia({
                audio: !isMuted,
                video: false,
              })
            })

          if (localVideoRef.current) {
            localVideoRef.current.srcObject = stream
          }

          // In a real app, you would connect to a WebRTC service here
          // For demo purposes, we'll simulate a connected call after 2 seconds
          if (!isIncoming) {
            setTimeout(() => {
              setIsCallConnected(true)
              // Simulate remote video with the same stream for demo
              if (remoteVideoRef.current) {
                remoteVideoRef.current.srcObject = stream
              }
            }, 2000)
          }

          return stream
        } catch (error) {
          console.error("Error accessing camera or microphone:", error)
          setIsVideoOn(false)

          toast({
            title: "Device access error",
            description: "Could not access camera. Continuing with audio only.",
            variant: "destructive",
          })

          // Try to continue with audio only
          try {
            const audioStream = await navigator.mediaDevices.getUserMedia({
              audio: true,
              video: false,
            })

            if (localVideoRef.current) {
              localVideoRef.current.srcObject = audioStream
            }

            return audioStream
          } catch (audioError) {
            console.error("Error accessing microphone:", audioError)
            toast({
              title: "Device access error",
              description: "Could not access microphone. Call functionality limited.",
              variant: "destructive",
            })
            return null
          }
        }
      }

      const streamPromise = startCamera()

      // Cleanup function
      return () => {
        streamPromise.then((stream) => {
          if (stream) {
            stream.getTracks().forEach((track) => track.stop())
          }
        })
      }
    }
  }, [open, isVideoOn, isMuted, isIncoming, toast])

  const handleAcceptCall = () => {
    setIsCallConnected(true)
    onAccept?.()
  }

  const handleRejectCall = () => {
    onReject?.()
    onOpenChange(false)
  }

  const handleEndCall = () => {
    onOpenChange(false)
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)

    // In a real app, you would update the audio track here
    if (localVideoRef.current && localVideoRef.current.srcObject) {
      const stream = localVideoRef.current.srcObject as MediaStream
      stream.getAudioTracks().forEach((track) => {
        track.enabled = isMuted // Toggle the current state
      })
    }
  }

  const toggleVideo = () => {
    if (!isVideoOn) {
      // If video is currently off, try to enable it
      navigator.mediaDevices
        .getUserMedia({ video: true })
        .then((videoStream) => {
          // If successful, add the video track to the existing stream
          if (localVideoRef.current && localVideoRef.current.srcObject) {
            const currentStream = localVideoRef.current.srcObject as MediaStream
            videoStream.getVideoTracks().forEach((track) => {
              currentStream.addTrack(track)
            })
            setIsVideoOn(true)
          }
        })
        .catch((err) => {
          console.error("Could not enable video:", err)
          toast({
            title: "Camera unavailable",
            description: "Could not enable video. No camera found or permission denied.",
            variant: "destructive",
          })
        })
    } else {
      // If video is currently on, disable it
      if (localVideoRef.current && localVideoRef.current.srcObject) {
        const stream = localVideoRef.current.srcObject as MediaStream
        stream.getVideoTracks().forEach((track) => {
          track.enabled = false
          stream.removeTrack(track)
          track.stop()
        })
        setIsVideoOn(false)
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 overflow-hidden bg-black text-white">
        <DialogTitle className="sr-only">Video Call</DialogTitle> {/* Hidden Title for Screen Readers */}
        <div className="relative h-[80vh] sm:h-[60vh] flex flex-col">
          {/* Remote video (full screen) */}
          {isCallConnected ? (
            <video ref={remoteVideoRef} autoPlay playsInline className="absolute inset-0 w-full h-full object-cover" />
          ) : (
            <div className="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-900">
              <div className="text-center">
                <Avatar className="h-24 w-24 mx-auto mb-4">
                  <AvatarImage src={recipientAvatar} alt={recipientName} />
                  <AvatarFallback className="text-4xl">{recipientName.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-semibold mb-2">{recipientName}</h2>
                <p className="text-gray-400">{isIncoming ? "Incoming call..." : "Calling..."}</p>
              </div>
            </div>
          )}

          {/* Local video (picture-in-picture) */}
          <div className="absolute top-4 right-4 w-32 h-48 rounded-lg overflow-hidden border-2 border-primary shadow-lg">
            <video ref={localVideoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
            {!isVideoOn && (
              <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
                <Avatar className="h-12 w-12">
                  <AvatarFallback>You</AvatarFallback>
                </Avatar>
              </div>
            )}
          </div>

          {/* Call controls */}
          <div className="absolute bottom-8 left-0 right-0 flex justify-center gap-4">
            {isIncoming && !isCallConnected ? (
              <>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleRejectCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-green-600 hover:bg-green-700"
                  onClick={handleAcceptCall}
                >
                  <Video className="h-6 w-6" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-12 w-12 rounded-full bg-gray-800 border-gray-700"
                  onClick={toggleMute}
                >
                  {isMuted ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
                </Button>
                <Button
                  size="icon"
                  className="h-14 w-14 rounded-full bg-red-600 hover:bg-red-700"
                  onClick={handleEndCall}
                >
                  <PhoneOff className="h-6 w-6" />
                </Button>
                <Button
                  size="icon"
                  variant="outline"
                  className="h-12 w-12 rounded-full bg-gray-800 border-gray-700"
                  onClick={toggleVideo}
                >
                  {isVideoOn ? <Video className="h-5 w-5" /> : <VideoOff className="h-5 w-5" />}
                </Button>
              </>
            )}
          </div>

          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 left-4 text-white"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-6 w-6" />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}