"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog" // Import DialogTitle
import { X, Mic, MicOff, Video, VideoOff, PhoneOff } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/components/ui/use-toast"

import { Id } from "@/convex/_generated/dataModel";

interface VideoCallProps {
  chatId: Id<"chats">;
  onEndCall: () => void;
  onOpenChange?: (open: boolean) => void;
  recipientId?: string;
  recipientName?: string;
  recipientAvatar?: string;
  isIncoming?: boolean;
}

export function VideoCall({
  chatId,
  onEndCall,
  onOpenChange,
  recipientId,
  recipientName = 'Participant',
  recipientAvatar,
  isIncoming = false,
}: VideoCallProps) {
  const [open, setOpen] = useState(true);
  const { toast } = useToast()
  const [isMuted, setIsMuted] = useState(false)
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isCallConnected, setIsCallConnected] = useState(false)
  const localVideoRef = useRef<HTMLVideoElement>(null)
  const remoteVideoRef = useRef<HTMLVideoElement>(null)

  // Check if browser supports media devices
  const isMediaDevicesSupported = useCallback(() => {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }, []);

  // Handle camera access
  useEffect(() => {
    if (open && isVideoOn) {
      const startCamera = async (): Promise<MediaStream | null> => {
        // Check if media devices are supported
        if (!isMediaDevicesSupported()) {
          toast({
            title: "Media devices not supported",
            description: "Your browser does not support camera/microphone access. Please use a modern browser like Chrome or Firefox.",
            variant: "destructive",
          });
          setIsVideoOn(false);
          return null;
        }

        try {
          // First try to get both video and audio
          const stream = await navigator.mediaDevices
            .getUserMedia({
              video: true,
              audio: !isMuted,
            })
            .catch(async () => {
              // If video fails, try audio only
              setIsVideoOn(false)
              return await navigator.mediaDevices.getUserMedia({
                audio: !isMuted,
                video: false,
              })
            })

          if (localVideoRef.current) {
            localVideoRef.current.srcObject = stream
          }

          // In a real app, you would connect to a WebRTC service here
          // For demo purposes, we'll simulate a connected call after 2 seconds
          if (!isIncoming && isMediaDevicesSupported()) {
            const timer = setTimeout(() => {
              setIsCallConnected(true)
              // Simulate remote video with the same stream for demo
              if (remoteVideoRef.current) {
                remoteVideoRef.current.srcObject = stream
              }
            }, 2000)
            
            // Store cleanup function in a separate variable
            const cleanup = () => clearTimeout(timer)
            // Return the stream and attach cleanup to it
            ;(stream as any)._cleanup = cleanup
          }

          return stream
        } catch (error) {
          console.error("Error accessing camera or microphone:", error)
          setIsVideoOn(false)

          toast({
            title: "Device access error",
            description: "Could not access camera. Continuing with audio only.",
            variant: "destructive",
          })

          // Try to continue with audio only
          try {
            const audioStream = await navigator.mediaDevices.getUserMedia({
              audio: true,
              video: false,
            })

            if (localVideoRef.current) {
              localVideoRef.current.srcObject = audioStream
            }

            return audioStream
          } catch (audioError) {
            console.error("Error accessing microphone:", audioError)
            toast({
              title: "Device access error",
              description: "Could not access microphone. Call functionality limited.",
              variant: "destructive",
            })
            return null
          }
        }
      }

      const streamPromise = startCamera()

      // Cleanup function
      return () => {
        streamPromise
          ?.then((stream) => {
            if (stream) {
              // Stop all media tracks
              stream.getTracks().forEach((track) => track.stop())
              
              // Call cleanup if it exists
              if ((stream as any)._cleanup) {
                (stream as any)._cleanup()
              }
            }
          })
          .catch((error) => {
            console.error('Error during stream cleanup:', error)
          })
      }
    }
  }, [open, isVideoOn, isMuted, isIncoming, toast, isMediaDevicesSupported])

  const handleAcceptCall = () => {
    setIsCallConnected(true)
  }

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    // Call the onOpenChange prop if provided
    if (onOpenChange) {
      onOpenChange(isOpen);
    }
    if (!isOpen) {
      onEndCall();
    }
  };

  const handleRejectCall = () => {
    if (onOpenChange) {
      onOpenChange(false);
    } else {
      setOpen(false);
    }
    onEndCall();
  }

  const handleEndCall = useCallback(() => {
    onEndCall();
    setOpen(false);
  }, [onEndCall]);

  const toggleMute = () => {
    setIsMuted(!isMuted)

    // In a real app, you would update the audio track here
    if (localVideoRef.current && localVideoRef.current.srcObject) {
      const stream = localVideoRef.current.srcObject as MediaStream
      stream.getAudioTracks().forEach((track) => {
        track.enabled = isMuted // Toggle the current state
      })
    }
  }

  const toggleVideo = () => {
    if (!isVideoOn) {
      // Check if media devices are supported
      if (!isMediaDevicesSupported()) {
        toast({
          title: "Camera not available",
          description: "Your browser does not support camera access. Please use a modern browser like Chrome or Firefox.",
          variant: "destructive",
        });
        return;
      }
      
      // If video is currently off, try to enable it
      navigator.mediaDevices.getUserMedia({ video: true })
        .then((videoStream) => {
          // If successful, add the video track to the existing stream
          if (localVideoRef.current && localVideoRef.current.srcObject) {
            const currentStream = localVideoRef.current.srcObject as MediaStream
            videoStream.getVideoTracks().forEach((track) => {
              currentStream.addTrack(track)
              // Clean up the video stream after adding the track
              videoStream.getTracks().forEach(t => t !== track && t.stop())
            })
            setIsVideoOn(true)
          } else {
            // Clean up if we couldn't set up the video
            videoStream.getTracks().forEach(track => track.stop())
          }
        })
        .catch((error) => {
          console.error("Error accessing camera:", error)
          toast({
            title: "Camera access error",
            description: "Could not access the camera. Please check your permissions and try again.",
            variant: "destructive",
          })
        })
        .catch((err) => {
          console.error("Could not enable video:", err)
          toast({
            title: "Camera unavailable",
            description: "Could not enable video. No camera found or permission denied.",
            variant: "destructive",
          })
        })
    } else {
      // If video is currently on, disable it
      if (localVideoRef.current && localVideoRef.current.srcObject) {
        const stream = localVideoRef.current.srcObject as MediaStream
        stream.getVideoTracks().forEach((track) => {
          track.enabled = false
          stream.removeTrack(track)
          track.stop()
        })
        setIsVideoOn(false)
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-4xl p-0 overflow-hidden">
        <div className="relative w-full h-[70vh] bg-gray-900">
          {/* Local video */}
          <div className="absolute bottom-4 right-4 w-40 h-28 bg-black rounded-lg overflow-hidden z-10">
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          </div>

          {/* Remote video */}
          <div className="w-full h-full flex items-center justify-center">
            {isCallConnected ? (
              <video
                ref={remoteVideoRef}
                autoPlay
                playsInline
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="flex flex-col items-center justify-center text-white">
                <Avatar className="w-24 h-24 mb-4">
                  <AvatarImage src={recipientAvatar} />
                  <AvatarFallback>
                    {recipientName?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <h3 className="text-xl font-semibold">
                  Calling {recipientName}
                </h3>
              </div>
            )}
          </div>

          {/* Call controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-4 flex justify-center gap-4">
            <Button
              variant={isMuted ? 'secondary' : 'outline'}
              size="icon"
              className="rounded-full"
              onClick={toggleMute}
            >
              {isMuted ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="rounded-full"
              onClick={handleEndCall}
            >
              <PhoneOff className="h-5 w-5" />
            </Button>
            <Button
              variant={!isVideoOn ? 'secondary' : 'outline'}
              size="icon"
              className="rounded-full"
              onClick={toggleVideo}
            >
              {!isVideoOn ? <VideoOff className="h-5 w-5" /> : <Video className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}