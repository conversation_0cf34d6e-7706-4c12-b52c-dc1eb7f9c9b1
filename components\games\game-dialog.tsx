import { useState, useCallback } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { GameLauncher } from "./game-launcher"
import { GameList } from "./game-list"
import { TicTacToe } from "./tic-tac-toe"
import { Id } from "@/convex/_generated/dataModel"
import { useAuth } from "@/hooks/use-auth"

type GameType = "tictactoe" | "rps" | "dice" | "numberguess"

interface GameDialogProps {
  chatId: Id<"chats">
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

type View = 'list' | 'game'

export function GameDialog({ chatId, children, open: prop<PERSON><PERSON>, onO<PERSON><PERSON>hange }: GameDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("active")
  const [currentView, setCurrentView] = useState<View>('list')
  const [selectedGame, setSelectedGame] = useState<{id: Id<"games">, type: GameType} | null>(null)
  const { userId } = useAuth()
  
  // Use controlled open state if provided, otherwise use internal state
  const open = propOpen ?? internalOpen
  const setOpen = (value: boolean) => {
    if (onOpenChange) {
      onOpenChange(value);
    } else {
      setInternalOpen(value);
    }
    
    // Reset view when opening the dialog
    if (value) {
      setCurrentView('list');
      setSelectedGame(null);
    }
  };

  if (!userId) return null

  const handleGameClick = useCallback((gameId: Id<"games">, gameType: string) => {
    setSelectedGame({ id: gameId, type: gameType as GameType })
    setCurrentView('game')
  }, [])

  const renderGameView = () => {
    if (!selectedGame) return null
    
    const handleBackToList = () => {
      setCurrentView('list')
      setSelectedGame(null)
    }
    
    switch (selectedGame.type) {
      case 'tictactoe':
        return (
          <div className="space-y-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={handleBackToList}
              className="mb-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Games
            </Button>
            <TicTacToe 
              gameId={selectedGame.id} 
              onGameEnd={handleBackToList} 
            />
          </div>
        )
      // Add other game types here
      default:
        return (
          <div className="text-center p-4">
            <p>Game type not supported</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={handleBackToList}
            >
              Back to Games
            </Button>
          </div>
        )
    }
  }

  const renderListView = () => (
    <>
      <DialogHeader>
        <DialogTitle>Games</DialogTitle>
      </DialogHeader>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active">Active Games</TabsTrigger>
          <TabsTrigger value="new">New Game</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active" className="mt-4">
          <GameList chatId={chatId} onGameClick={handleGameClick} />
        </TabsContent>
        
        <TabsContent value="new" className="mt-4">
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Select a game to start playing with others in this chat
            </p>
            <GameLauncher chatId={chatId} userId={userId as Id<"users">} />
          </div>
        </TabsContent>
      </Tabs>
    </>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
        {currentView === 'game' && selectedGame ? (
          <div className="space-y-4">
            <Button 
              variant="ghost" 
              size="sm" 
              className="-ml-2"
              onClick={() => setCurrentView('list')}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Games
            </Button>
            {renderGameView()}
          </div>
        ) : (
          renderListView()
        )}
      </DialogContent>
    </Dialog>
  )
}
