import { mutation } from "../_generated/server";
import { v } from "convex/values";

/**
 * Migration to update any chats with category 'kingdom' to 'kasi-vibes'
 * This ensures consistency with the current category schema
 */
export default mutation({
  args: {},
  handler: async (ctx) => {
    // Find all chats with category 'kingdom'
    const allChats = await ctx.db.query("chats").collect();
    const kingdomChats = allChats.filter(chat => chat.category === "kingdom");

    console.log(`Found ${kingdomChats.length} chats with 'kingdom' category`);

    // Update each chat to use 'kasi-vibes' instead
    for (const chat of kingdomChats) {
      console.log(`Updating chat ${chat._id} from 'kingdom' to 'kasi-vibes'`);
      await ctx.db.patch(chat._id, {
        category: "kasi-vibes",
        updatedAt: Date.now(),
      });
    }

    return { updatedCount: kingdomChats.length };
  },
});
