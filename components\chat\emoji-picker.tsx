"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void
}

// Simple emoji sets
const EMOJI_SETS = {
  smileys: ["😀", "😁", "😂", "🤣", "😃", "😄", "😅", "😆", "😉", "😊", "😋", "😎", "😍", "🥰", "😘"],
  people: ["👋", "👌", "✌️", "🤞", "👍", "👎", "👏", "🙌", "🤝", "🙏", "💪", "🧠", "👀", "👄", "❤️"],
  animals: ["🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯", "🦁", "🐮", "🐷", "🐸", "🐵"],
  food: ["🍎", "🍐", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🍈", "🍒", "🍑", "🥭", "🍍", "🥥", "🥝"],
  mxit: [":-)", ":-(", ";-)", ":-D", ":-P", ":-*", ":-O", ":-|", ":-S", ":-$", ":-@", ":-#", ":-!", ":-Z", ":-X"],
}

export function EmojiPicker({ onEmojiSelect }: EmojiPickerProps) {
  const [activeTab, setActiveTab] = useState("smileys")

  return (
    <Card className="w-64 p-2 shadow-lg">
      <Tabs defaultValue="smileys" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="smileys">😊</TabsTrigger>
          <TabsTrigger value="people">👋</TabsTrigger>
          <TabsTrigger value="animals">🐶</TabsTrigger>
          <TabsTrigger value="food">🍎</TabsTrigger>
          <TabsTrigger value="mxit">:-)</TabsTrigger>
        </TabsList>

        {Object.entries(EMOJI_SETS).map(([category, emojis]) => (
          <TabsContent key={category} value={category} className="mt-2">
            <div className="grid grid-cols-5 gap-2">
              {emojis.map((emoji) => (
                <button
                  key={emoji}
                  className="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted"
                  onClick={() => onEmojiSelect(emoji)}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </Card>
  )
}

