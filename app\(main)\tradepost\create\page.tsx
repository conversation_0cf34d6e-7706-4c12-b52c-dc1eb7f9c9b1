"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/components/ui/use-toast"
import { MoolaIcon } from "@/components/moola-icon"
import { ImageIcon, Upload } from "lucide-react"

// Categories for marketplace listings - must match the schema validation in tradepost.ts
const CATEGORIES = [
  { label: "Electronics", value: "electronics" },
  { label: "Fashion", value: "fashion" },
  { label: "Home & Garden", value: "home_garden" },
  { label: "Vehicles", value: "vehicles" },
  { label: "Property", value: "property" },
  { label: "Hobbies & Toys", value: "hobbies_toys" },
  { label: "Books, Music & Games", value: "books_music_games" },
  { label: "Services", value: "services" },
  { label: "Jobs", value: "jobs" },
  { label: "Pets", value: "pets" },
  { label: "Other", value: "other" }
]

// South African locations
const LOCATIONS = [
  "Cape Town",
  "Johannesburg",
  "Durban",
  "Pretoria",
  "Port Elizabeth",
  "Bloemfontein",
  "East London",
  "Kimberley",
  "Polokwane",
  "Nelspruit",
  "Other",
]

export default function CreateListingPage() {
  const router = useRouter()
  const { userId } = useAuth()
  const { toast } = useToast()
  const createListing = useMutation(api.tradepost.createListing)

  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [price, setPrice] = useState<string>("")
  const [category, setCategory] = useState<"electronics" | "fashion" | "home_garden" | "vehicles" | "property" | "hobbies_toys" | "books_music_games" | "services" | "jobs" | "pets" | "other">("other")
  const [location, setLocation] = useState("")
  const [imageUrl, setImageUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!userId) {
      toast({
        title: "Not logged in",
        description: "You need to be logged in to create a listing",
        variant: "destructive",
      })
      return

    }

    if (!title || !description || !price || !category || !location) {
      toast({
        title: "Missing fields",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      if (!userId) {
        throw new Error("User ID is required")
      }
      
      // Ensure we have a valid user ID
      if (!userId) {
        throw new Error("User ID is required");
      }
      
      // Create the listing with properly typed sellerId
      await createListing({
        title,
        description,
        price,
        imageStorageIds: imageUrl || undefined,
        category,
        location,
        sellerId: userId as Id<"users">
      })

      toast({
        title: "Listing Created",
        description: "Your listing has been created successfully",
      })

      router.push("/tradepost")
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create listing",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleImageUpload = () => {
    // In a real app, you would implement file upload here
    // For now, we'll just use a placeholder
    setImageUrl("/placeholder.svg?height=300&width=300")

    toast({
      title: "Image Uploaded",
      description: "Your image has been uploaded successfully",
    })
  }

  return (
    <div className="container max-w-2xl mx-auto py-6 px-4">
      <h1 className="text-2xl font-bold mb-6">Create Tradepost Listing</h1>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Listing Details</CardTitle>
            <CardDescription>Create a new listing to sell an item for Moola</CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="What are you selling?"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe your item..."
                rows={4}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <div className="relative">
                <MoolaIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-yellow-500" />
                <Input
                  id="price"
                  type="text"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="pl-9"
                  placeholder="e.g., R 450 or 50 Moola"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={category} 
                  onValueChange={(value) => setCategory(value as typeof category)} 
                  required
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map((cat) => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Select value={location} onValueChange={setLocation} required>
                  <SelectTrigger id="location">
                    <SelectValue placeholder="Select location" />
                  </SelectTrigger>
                  <SelectContent>
                    {LOCATIONS.map((loc) => (
                      <SelectItem key={loc} value={loc}>
                        {loc}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Image</Label>
              <div className="border-2 border-dashed rounded-lg p-6 text-center">
                {imageUrl ? (
                  <div className="space-y-2">
                    <img
                      src={imageUrl || "/placeholder.svg"}
                      alt="Listing preview"
                      className="mx-auto h-40 object-contain"
                    />
                    <Button type="button" variant="outline" onClick={() => setImageUrl("")}>
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                      <ImageIcon className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <p className="text-sm text-muted-foreground">Drag and drop an image, or click to browse</p>
                    <Button type="button" variant="outline" onClick={handleImageUpload}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Image
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.push("/tradepost")}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Listing"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
