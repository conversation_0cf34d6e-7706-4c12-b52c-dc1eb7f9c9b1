// Run this script with Node.js to get the exact Clerk issuer URL
// node scripts/get-clerk-issuer.js

const { createClerkClient } = require("@clerk/clerk-sdk-node")

// Replace with your actual Clerk secret key
const clerk = createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY })

async function getJWTConfig() {
  try {
    const jwtConfig = await clerk.verifications.getJWTConfig()
    // console.log("Clerk JWT Issuer:", jwtConfig.issuer)
    // console.log("For Convex auth.config.js, use:", jwtConfig.issuer.replace(/^https?:\/\//, ""))
  } catch (error) {
    // console.error("Error getting JWT config:", error)
  }
}

getJWTConfig()

