"use client"

import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Users, MapPin, Tag } from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"

interface ChatroomCardProps {
  id: string
  name: string
  description?: string
  memberCount: number
  thumbnailUrl?: string
  location?: string
  tags?: string[]
  createdAt: number
  isPrivate: boolean
  onJoin: (id: string) => void
  isMember?: boolean
}

const ChatroomCard: React.FC<ChatroomCardProps> = ({
  id,
  name,
  description,
  memberCount,
  thumbnailUrl,
  location,
  tags,
  createdAt,
  isPrivate,
  onJoin,
  isMember = false,
}) => {
  const timeAgo = formatDistanceToNow(new Date(createdAt), { addSuffix: true })

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all duration-300 hover:shadow-md border-2",
        is<PERSON><PERSON><PERSON> ? "border-primary/50" : "border-transparent",
      )}
    >
      <div className="relative">
        {thumbnailUrl ? (
          <div className="h-32 w-full bg-cover bg-center" style={{ backgroundImage: `url(${thumbnailUrl})` }} />
        ) : (
          <div className="h-32 w-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
            <span className="text-2xl font-bold text-primary/70">{name.charAt(0)}</span>
          </div>
        )}
        {isPrivate && (
          <div className="absolute top-2 right-2 bg-background/80 text-foreground text-xs px-2 py-1 rounded-full">
            Private
          </div>
        )}
      </div>

      <CardHeader className="pb-2">
        <CardTitle className="flex justify-between items-center">
          <span className="truncate">{name}</span>
        </CardTitle>
      </CardHeader>

      <CardContent className="pb-4">
        {description && <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{description}</p>}

        <div className="flex flex-wrap gap-2 mt-2">
          <div className="flex items-center text-xs text-muted-foreground">
            <Users className="h-3 w-3 mr-1" />
            <span>
              {memberCount} member{memberCount !== 1 ? "s" : ""}
            </span>
          </div>

          {location && (
            <div className="flex items-center text-xs text-muted-foreground">
              <MapPin className="h-3 w-3 mr-1" />
              <span className="truncate max-w-[100px]">{location}</span>
            </div>
          )}

          <div className="flex items-center text-xs text-muted-foreground">
            <span>Created {timeAgo}</span>
          </div>
        </div>

        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {tags.slice(0, 3).map((tag) => (
              <div key={tag} className="bg-muted text-xs rounded-full px-2 py-0.5 flex items-center">
                <Tag className="h-2 w-2 mr-1" />
                {tag}
              </div>
            ))}
            {tags.length > 3 && (
              <div className="bg-muted text-xs rounded-full px-2 py-0.5">+{tags.length - 3} more</div>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter>
        <Button variant={isMember ? "secondary" : "default"} size="sm" className="w-full" onClick={() => onJoin(id)}>
          {isMember ? "Enter Chatroom" : "Join Chatroom"}
        </Button>
      </CardFooter>
    </Card>
  )
}

export default ChatroomCard

