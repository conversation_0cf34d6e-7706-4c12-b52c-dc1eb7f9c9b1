"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronDown } from "lucide-react"

interface ScrollToBottomButtonProps {
  visible: boolean
  onClick: () => void
}

export function ScrollToBottomButton({ visible, onClick }: ScrollToBottomButtonProps) {
  if (!visible) return null

  return (
    <div className="absolute bottom-16 right-4 z-10">
      <Button
        onClick={onClick}
        size="sm"
        className="rounded-full shadow-lg bg-primary hover:bg-primary/90 h-10 w-10 p-0"
      >
        <ChevronDown className="h-5 w-5" />
      </Button>
    </div>
  )
}

