"use client"

import { useTheme } from "@/components/providers/theme-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Moon, Sun, Monitor } from "lucide-react"
import { useState, useEffect } from "react"

export function ThemeSelector() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Use a consistent layout for server and client initial render
  const flexDirection = "flex-col sm:flex-row"

  return (
    <div className={`flex ${flexDirection} gap-2`}>
      <Button
        variant={theme === "light" ? "default" : "outline"}
        size="default"
        className="w-full justify-start sm:justify-center"
        onClick={() => setTheme("light")}
      >
        <Sun className="h-4 w-4 mr-2" />
        Light
      </Button>
      <Button
        variant={theme === "dark" ? "default" : "outline"}
        size="default"
        className="w-full justify-start sm:justify-center"
        onClick={() => setTheme("dark")}
      >
        <Moon className="h-4 w-4 mr-2" />
        Dark
      </Button>
      <Button
        variant={theme === "system" ? "default" : "outline"}
        size="default"
        className="w-full justify-start sm:justify-center"
        onClick={() => setTheme("system")}
      >
        <Monitor className="h-4 w-4 mr-2" />
        System
      </Button>
    </div>
  )
}

