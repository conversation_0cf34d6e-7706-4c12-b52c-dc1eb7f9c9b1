import { format, formatDistanceToNow } from "date-fns";
import { Message, Chat } from "./types";
import { Id } from "@/convex/_generated/dataModel";

export const formatMessageTime = (timestamp: number): string => {
  return format(new Date(timestamp), 'HH:mm');
};

export const formatMessageDate = (timestamp: number): string => {
  return format(new Date(timestamp), 'PPP');
};

export const formatRelativeTime = (timestamp: number): string => {
  return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
};

export const getMessageStatus = (message: Message, currentUserId: Id<"users">): string => {
  if (message.senderId !== currentUserId) return '';
  // Add logic for message status (sent, delivered, read, etc.)
  return '✓✓';
};

export const shouldShowDateSeparator = (
  messages: Message[],
  index: number,
  currentUserId: Id<"users">
): boolean => {
  if (index === 0) return true;
  
  const currentMessage = messages[index];
  const previousMessage = messages[index - 1];
  
  const currentDate = new Date(currentMessage.timestamp).toDateString();
  const previousDate = new Date(previousMessage.timestamp).toDateString();
  
  return currentDate !== previousDate;
};

export const shouldShowAvatar = (
  messages: Message[],
  index: number,
  currentUserId: Id<"users">
): boolean => {
  if (messages[index].senderId === currentUserId) return false;
  if (index === 0) return true;
  
  const currentMessage = messages[index];
  const previousMessage = messages[index - 1];
  
  return (
    previousMessage.senderId !== currentMessage.senderId ||
    currentMessage.timestamp - previousMessage.timestamp > 5 * 60 * 1000 // 5 minutes
  );
};

export const getChatTitle = (chat: Chat | null, currentUserId: Id<"users">): string => {
  if (!chat) return 'Chat';
  
  if (chat.type === 'direct') {
    const otherUser = chat.participants.find(p => p._id !== currentUserId);
    return otherUser?.name || 'Unknown User';
  }
  
  return chat.name || 'Group Chat';
};
