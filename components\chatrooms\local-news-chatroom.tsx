"use client"

import { useState, useEffect } from "react"
import { useQ<PERSON>y, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import { 
  Newspaper, 
  Plus, 
  MessageSquare, 
  ThumbsUp, 
  Share2, 
  ExternalLink,
  <PERSON><PERSON><PERSON>,
  Clock,
  User
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { Id } from "@/convex/_generated/dataModel"
import { StatusDot } from "@/components/status/enhanced-status-indicator"

interface NewsItem {
  _id: Id<"messages">
  title: string
  content: string
  description?: string
  url?: string
  senderId: Id<"users">
  senderName: string
  userName: string
  senderAvatar?: string
  userAvatar?: string
  timestamp: number
  reactions?: Array<{ emoji: string; userId: Id<"users"> }>
  commentCount?: number
}

interface LocalNewsChatroomProps {
  chatroomId: Id<"chats">
  className?: string
}

export function LocalNewsChatroom({ chatroomId, className }: LocalNewsChatroomProps) {
  const { userId } = useAuth()
  const { toast } = useToast()
  
  const [isSubmitDialogOpen, setIsSubmitDialogOpen] = useState(false)
  const [newsTitle, setNewsTitle] = useState("")
  const [newsDescription, setNewsDescription] = useState("")
  const [newsUrl, setNewsUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch recent news from the chatroom
  const recentNews = useQuery(api.chatrooms.getRecentLocalNews, {
    localNewsChatroomId: chatroomId
  })

  // Submit news mutation
  const submitNews = useMutation(api.chatrooms.submitLocalNews)

  // Handle news submission
  const handleSubmitNews = async () => {
    if (!newsTitle.trim() || !newsDescription.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both title and description",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)
    try {
      await submitNews({
        localNewsChatroomId: chatroomId,
        title: newsTitle.trim(),
        description: newsDescription.trim(),
        url: newsUrl.trim() || undefined
      })

      toast({
        title: "Success",
        description: "News submitted successfully!",
        variant: "default"
      })

      // Reset form
      setNewsTitle("")
      setNewsDescription("")
      setNewsUrl("")
      setIsSubmitDialogOpen(false)
    } catch (error) {
      console.error("Error submitting news:", error)
      toast({
        title: "Error",
        description: "Failed to submit news. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle news reaction
  const handleReaction = async (newsId: Id<"messages">, emoji: string) => {
    // TODO: Implement reaction functionality
    console.log("React to news:", newsId, emoji)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <Card className="mxit-card">
        <CardHeader className="pb-3 mxit-card-header">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Newspaper className="h-5 w-5" />
              📰 Local News
            </CardTitle>
            
            {userId && (
              <Dialog open={isSubmitDialogOpen} onOpenChange={setIsSubmitDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="mxit-button">
                    <Plus className="h-4 w-4 mr-1" />
                    Submit News
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-blue-700">Submit Local News</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-blue-700">Title *</label>
                      <Input
                        placeholder="News headline..."
                        value={newsTitle}
                        onChange={(e) => setNewsTitle(e.target.value)}
                        className="mxit-input"
                        maxLength={100}
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-blue-700">Description *</label>
                      <Textarea
                        placeholder="Tell us more about this news..."
                        value={newsDescription}
                        onChange={(e) => setNewsDescription(e.target.value)}
                        className="mxit-input min-h-[80px]"
                        maxLength={500}
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-blue-700">Source URL (optional)</label>
                      <Input
                        placeholder="https://..."
                        value={newsUrl}
                        onChange={(e) => setNewsUrl(e.target.value)}
                        className="mxit-input"
                        type="url"
                      />
                    </div>
                    
                    <div className="flex gap-2 pt-2">
                      <Button
                        onClick={handleSubmitNews}
                        disabled={isSubmitting}
                        className="flex-1 mxit-button"
                      >
                        {isSubmitting ? "Submitting..." : "Submit News"}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsSubmitDialogOpen(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
          
          <p className="text-sm text-blue-600">
            Stay updated with the latest local news and community announcements
          </p>
        </CardHeader>
      </Card>

      {/* News Feed */}
      <ScrollArea className="h-[600px] mxit-scrollbar">
        <div className="space-y-4">
          {recentNews && recentNews.length > 0 ? (
            recentNews.map((news) => (
              <Card key={news._id} className="mxit-card hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  {/* News Header */}
                  <div className="flex items-start gap-3 mb-3">
                    <Avatar className="h-10 w-10 mxit-avatar">
                      <AvatarImage src={news.userAvatar} alt={news.userName} />
                      <AvatarFallback className="bg-blue-100 text-blue-700 font-bold">
                        {news.userName.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-blue-900 truncate">
                          {news.userName}
                        </h4>
                        <StatusDot userId={news.senderId} />
                        <Badge variant="outline" className="text-xs">
                          <User className="h-3 w-3 mr-1" />
                          Reporter
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                        <Clock className="h-3 w-3" />
                        {formatDistanceToNow(news.timestamp, { addSuffix: true })}
                        <MapPin className="h-3 w-3 ml-2" />
                        Local
                      </div>
                    </div>
                  </div>

                  {/* News Content */}
                  <div className="space-y-3">
                    <h3 className="font-bold text-lg text-blue-900 leading-tight">
                      {news.title}
                    </h3>
                    
                    <p className="text-gray-700 leading-relaxed">
                      {news.description || news.content}
                    </p>
                    
                    {news.url && (
                      <a
                        href={news.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Read full article
                      </a>
                    )}
                  </div>

                  {/* News Actions */}
                  <div className="flex items-center gap-4 mt-4 pt-3 border-t border-blue-100">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-600 hover:bg-blue-50"
                      onClick={() => handleReaction(news._id, "👍")}
                    >
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      {news.reactions?.filter(r => r.emoji === "👍").length || 0}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-600 hover:bg-blue-50"
                    >
                      <MessageSquare className="h-4 w-4 mr-1" />
                      {news.commentCount || 0} Comments
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-blue-600 hover:bg-blue-50"
                    >
                      <Share2 className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card className="mxit-card">
              <CardContent className="flex flex-col items-center justify-center py-12 text-gray-500">
                <Newspaper className="h-12 w-12 mb-4" />
                <h3 className="text-lg font-medium mb-2">No news yet</h3>
                <p className="text-sm text-center">
                  Be the first to share local news with the community!
                </p>
                {userId && (
                  <Button
                    onClick={() => setIsSubmitDialogOpen(true)}
                    className="mt-4 mxit-button"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Submit First News
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
