"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api" // Verify path
import { useAuth } from "@/hooks/use-auth" // Verify path
import { Id } from "@/convex/_generated/dataModel" // Verify path
import { useToast } from "@/components/ui/use-toast"; // Verify path & import if needed

// Define payment method types if needed elsewhere
export type PaymentMethod = "sms" | "card" | "airtime"; // Added airtime as a payment method

export function useMoola() {
  const { userId, user, isLoading: isAuthLoading } = useAuth()
  const { toast } = useToast()

  // Add state for local Moola balance
  const [localMoolaBalance, setLocalMoolaBalance] = useState<number>(0)
  const [pendingTransactions, setPendingTransactions] = useState<any[]>([])

  // --- Queries ---

  // Get user's moola transactions
  const transactionsQuery = useQuery(
    api.moola.getTransactions,
    // Only fetch if we have a userId, otherwise skip
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  // --- Mutations ---

  // Purchase moola mutation
  // !!! SECURITY WARNING !!! This mutation MUST NOT grant Moola directly without
  // !!! server-side verification of successful payment (e.g., via webhook).
  // !!! It should ideally only be callable AFTER payment confirmation.
  // !!! Assumes backend uses identity for the user, not the 'userId' arg.
  const purchaseMoolaMutation = useMutation(api.moola.purchaseMoola)

  // --- State and Derived Values ---

  // Loading state combines auth and transaction query loading
  const isLoading = isAuthLoading || (!!userId && transactionsQuery?.isLoading);

  // Safely get balance from the user object provided by useAuth
  const serverBalance = user?.moola ?? 0 // Use nullish coalescing

  // Effect to initialize local balance from user data or localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (user?.moola !== undefined) {
        // User data is available, use it as source of truth
        setLocalMoolaBalance(user.moola);
        localStorage.setItem('xitchat-moola-balance', String(user.moola));
      } else {
        // Try to load from localStorage
        const savedBalance = localStorage.getItem('xitchat-moola-balance');
        if (savedBalance) {
          try {
            const parsedBalance = Number(savedBalance);
            setLocalMoolaBalance(parsedBalance);
          } catch (e) {
            console.error('Failed to parse local Moola balance:', e);
            localStorage.removeItem('xitchat-moola-balance');
          }
        }
      }

      // Load pending transactions
      const savedTransactions = localStorage.getItem('xitchat-pending-transactions');
      if (savedTransactions) {
        try {
          const parsedTransactions = JSON.parse(savedTransactions);
          setPendingTransactions(parsedTransactions);
        } catch (e) {
          console.error('Failed to parse pending transactions:', e);
          localStorage.removeItem('xitchat-pending-transactions');
        }
      }
    }
  }, [user?.moola]);

  // Effect to sync pending transactions when coming back online
  useEffect(() => {
    if (!userId || pendingTransactions.length === 0) return;

    const syncTransactions = async () => {
      if (navigator.onLine) {
        toast({
          title: "Syncing transactions",
          description: `Processing ${pendingTransactions.length} pending transactions...`
        });

        let successCount = 0;

        for (const transaction of pendingTransactions) {
          try {
            // Attempt to process the transaction on the server
            await purchaseMoolaMutation({
              userId: userId as Id<"users">,
              amount: transaction.amount,
              paymentMethod: transaction.paymentMethod,
              transactionReference: transaction.transactionReference,
              paymentVerified: true // This should be properly verified in production
            });
            successCount++;
          } catch (error) {
            console.error("Failed to sync transaction:", error);
          }
        }

        // Clear pending transactions
        setPendingTransactions([]);
        localStorage.removeItem('xitchat-pending-transactions');

        toast({
          title: "Sync complete",
          description: `${successCount} transactions processed`
        });
      }
    };

    // Add event listener for online events
    window.addEventListener('online', syncTransactions);

    // Check if we're already online and have pending transactions
    if (navigator.onLine && pendingTransactions.length > 0) {
      syncTransactions();
    }

    return () => window.removeEventListener('online', syncTransactions);
  }, [pendingTransactions, userId, purchaseMoolaMutation, toast]);

  // Get the balance - use local balance when offline, server balance when online
  const balance = navigator.onLine ? serverBalance : localMoolaBalance

  // --- Actions ---

  /**
   * Initiates the Moola purchase process.
   * !!! SECURITY WARNING !!! Ensure the corresponding backend mutation (`api.moola.purchaseMoola`)
   * does NOT grant Moola balance directly. It should either:
   *   a) Only record the *intent* to purchase and require webhook confirmation, OR
   *   b) Perform its own robust server-side payment verification before granting Moola.
   * Calling a mutation that grants currency directly from the client without verification is insecure.
   */
  const purchaseMoola = async (amount: number, paymentMethod: PaymentMethod, transactionReference?: string) => {
    if (!userId) {
        toast({ title: "Authentication Required", description: "You must be logged in to purchase Moola.", variant: "destructive"})
        throw new Error("You must be logged in to purchase Moola")
    }
     if (amount <= 0) {
        toast({ title: "Invalid Amount", description: "Purchase amount must be positive.", variant: "destructive"})
        throw new Error("Purchase amount must be positive.")
    }

    // Check if we're offline
    if (!navigator.onLine) {
      // Store the transaction locally
      const pendingTransaction = {
        id: crypto.randomUUID(),
        userId,
        amount,
        paymentMethod,
        transactionReference,
        timestamp: Date.now()
      };

      // Add to pending transactions
      const newPendingTransactions = [...pendingTransactions, pendingTransaction];
      setPendingTransactions(newPendingTransactions);
      localStorage.setItem('xitchat-pending-transactions', JSON.stringify(newPendingTransactions));

      // Update local balance
      const newBalance = localMoolaBalance + amount;
      setLocalMoolaBalance(newBalance);
      localStorage.setItem('xitchat-moola-balance', String(newBalance));

      toast({
        title: "Offline Purchase",
        description: `Added ${amount} Moola to your account. Will sync when online.`
      });

      return { success: true, newBalance };
    }

    try {
      // Process payment based on payment method
      let paymentVerified = false;
      let paymentId = transactionReference;

      if (paymentMethod === "card") {
        // Integrate with card payment processor (e.g., Stripe)
        try {
          // Create a payment intent
          const response = await fetch('/api/create-payment-intent', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ amount, userId })
          });

          if (!response.ok) {
            throw new Error('Failed to create payment intent');
          }

          const { clientSecret } = await response.json();

          // In a real implementation, you would use Stripe Elements or similar
          // to collect card details and confirm the payment
          // For now, we'll simulate a successful payment
          paymentId = `sim_${Date.now()}`;
          paymentVerified = true;

        } catch (error) {
          console.error('Payment processing error:', error);
          throw new Error('Payment processing failed');
        }
      }
      else if (paymentMethod === "airtime") {
        // Simulate airtime payment integration
        // In a real app, this would integrate with mobile carrier APIs
        paymentId = `airtime_${Date.now()}`;
        paymentVerified = true;
      }
      else if (paymentMethod === "sms") {
        // Simulate SMS payment integration
        // In a real app, this would integrate with SMS payment providers
        paymentId = `sms_${Date.now()}`;
        paymentVerified = true;
      }

      // For development/testing purposes only - in production, verification would be done server-side
      if (!paymentVerified) {
        throw new Error('Payment verification failed');
      }

      // Call the mutation with the userId and payment details
      const result = await purchaseMoolaMutation({
        userId: userId as Id<"users">,
        amount,
        paymentMethod,
        transactionReference: paymentId,
        paymentVerified // This should be removed in production
      })

      toast({ title: "Purchase Successful", description: `Added ${amount} Moola to your account.`})

      // Update local balance to match server
      if (user?.moola !== undefined) {
        const newBalance = user.moola + amount;
        setLocalMoolaBalance(newBalance);
        localStorage.setItem('xitchat-moola-balance', String(newBalance));
      }

      return result;

    } catch (error: any) {
        console.error("Error purchasing Moola:", error);
        toast({ title: "Purchase Failed", description: error.message || "Could not complete the Moola purchase.", variant: "destructive"})
        throw error; // Re-throw for potential upstream handling
    }
  }

  // --- Return Value ---

  return {
    balance: navigator.onLine ? serverBalance : localMoolaBalance, // Use local balance when offline
    serverBalance, // Original server balance
    localBalance: localMoolaBalance, // Local balance
    // Safely return transactions data or empty array
    transactions: transactionsQuery?.data ?? [],
    pendingTransactions, // Expose pending transactions
    isLoading,
    isOffline: !navigator.onLine, // Expose online status
    purchaseMoola, // Expose the purchase function
    error: transactionsQuery?.error // Expose query error state
  }
}