// convex/users.ts
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel"; // Import Doc and Id if needed for typing

// Query to get a user by their Clerk ID
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .first();
      return user;
    } catch (error) {
      console.error("Error in getUserByClerkId:", error);
      return null; // Return null on error for queries
    }
  },
});

// Query to get the currently authenticated user based on Convex identity
export const getCurrentUser = query({
  args: {}, // No arguments needed, uses context
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      return null; // Not logged in
    }

    try {
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject))
        .unique();

      return user; // Return the user document or null if not found
    } catch (error) {
      console.error("Error fetching current user by identity:", error);
      return null; // Return null on error
    }
  },
});

// Mutation to store or update user data from Clerk webhook or initial sign-in
export const storeUser = mutation({
  args: {
    clerkId: v.string(),
    name: v.string(),
    avatar: v.string(),
    email: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    try {
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .unique();

      if (existingUser) {
        // Patch only if data has actually changed
        const needsUpdate = existingUser.name !== args.name ||
                            existingUser.avatar !== args.avatar ||
                            existingUser.email !== args.email; // Compare optional email correctly

        if (needsUpdate) {
             await ctx.db.patch(existingUser._id, {
                name: args.name,
                avatar: args.avatar,
                email: args.email, // Pass optional value directly
                updatedAt: now,
             });
        }
        return existingUser._id;
      }

      // Create new user if they don't exist
      const userId = await ctx.db.insert("users", {
        clerkId: args.clerkId,
        name: args.name,
        email: args.email, // Pass optional value directly
        avatar: args.avatar,
        status: "online", // Default status
        moola: 100,       // Default Moola
        createdAt: now,
        updatedAt: now,
        // --- Add other default fields based on your 'users' schema ---
        language: 'en',
        dataSavingMode: false,
        // statusMessage: '', // Example if defined in schema
        // mood: 'happy',    // Example if defined in schema
        // theme: 'light',   // Example if defined in schema
        // lastSeen: now,    // Example if defined in schema
      });
      return userId;
    } catch (error: any) { // Type error
      console.error("Error in storeUser:", error);
      throw new Error(`Failed to store user: ${error.message || 'Unknown error'}`);
    }
  },
});

// Mutation to update only the user's online/offline/away status and lastSeen
export const updateUserStatus = mutation({
  args: {
    // Requires the internal Convex user ID
    userId: v.id("users"),
    status: v.union(v.literal("online"), v.literal("offline"), v.literal("away")),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    // Only update if user exists and status is different
    if (!user || user.status === args.status) {
      return false;
    }
    await ctx.db.patch(args.userId, {
      status: args.status,
      lastSeen: Date.now(), // Update lastSeen timestamp
      updatedAt: Date.now(),
    });
    return true;
  },
});

// General mutation to update various optional user profile fields
export const updateUser = mutation({
  args: {
    // Requires the internal Convex user ID
    userId: v.id("users"),
    // --- Include all optional, updatable fields from your 'users' schema ---
    statusMessage: v.optional(v.string()),
    mood: v.optional(
      v.union(
        v.literal("happy"), v.literal("sad"), v.literal("excited"),
        v.literal("bored"), v.literal("busy"), v.literal("relaxed"),
        v.literal("tired"), v.literal("angry"), v.literal("sick"), v.literal("love")
      )
    ),
    language: v.optional(v.string()),
    theme: v.optional(v.string()),
    dataSavingMode: v.optional(v.boolean()),
    // Add others like 'name', 'avatar' if they can be updated here too
    name: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;

    // Optional: Add authorization check - ensure logged-in user matches userId being updated
    // const identity = await ctx.auth.getUserIdentity();
    // if (!identity) throw new Error("Not authenticated");
    // const userToUpdate = await ctx.db.get(userId);
    // if (!userToUpdate || userToUpdate.clerkId !== identity.subject) {
    //   throw new Error("Unauthorized to update this user profile.");
    // }
     const user = await ctx.db.get(userId);
     if (!user) {
       throw new Error("User not found");
     }

    // Construct object with only the fields that were actually provided
    const validUpdates: Partial<Doc<"users">> = {}; // Use Partial<Doc<"users">> for type safety
    let hasUpdates = false;
    for (const key in updates) {
      // Check if the key is a valid field in the updates object and not undefined
      if (Object.prototype.hasOwnProperty.call(updates, key) && updates[key as keyof typeof updates] !== undefined) {
        // Type assertion might be needed depending on strictness
        validUpdates[key as keyof Doc<"users">] = updates[key as keyof typeof updates];
        hasUpdates = true;
      }
    }

    // Only perform patch if there are actual fields to update
    if (hasUpdates) {
      validUpdates.updatedAt = Date.now(); // Update timestamp
      await ctx.db.patch(userId, validUpdates);
      return { success: true, updated: true };
    } else {
      // console.log("No valid fields provided to update for user:", userId);
      return { success: true, updated: false }; // Indicate success but no change made
    }
  },
});

// Query to get online users (excluding the current user)
export const getOnlineUsers = query({
    args: {
        userId: v.optional(v.id("users")), // Optional user ID to exclude
    },
    handler: async (ctx, args) => {
        try {
            // --- Recommended: Add index on 'status' in schema.ts ---
            // const onlineUsers = await ctx.db
            //    .query("users")
            //    .withIndex("by_status", q => q.eq("status", "online"))
            //    .collect();

            // Fallback filter (less efficient without index):
            const onlineUsers = await ctx.db
                .query("users")
                .filter((q) => q.eq(q.field("status"), "online"))
                .collect();

            // Exclude the current user from the list
            const currentUserId = args.userId;
            if (!currentUserId) {
                return onlineUsers;
            }
            return onlineUsers.filter(user => user._id !== currentUserId);
        } catch (error) {
            console.error("Error getting online users:", error)
            return [] // Return empty array on error
        }
    }
});

// Query to get a user's profile
export const getUserProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db.get(args.userId);
      if (!user) {
        return null;
      }

      return {
        _id: user._id,
        name: user.name,
        avatar: user.avatar,
        status: user.status,
        statusMessage: user.statusMessage,
        mood: user.mood,
        lastSeen: user.lastSeen,
        moola: user.moola,
        language: user.language,
        dataSavingMode: user.dataSavingMode,
      };
    } catch (error) {
      console.error("Error getting user profile:", error);
      return null;
    }
  },
});

// --- Placeholder for Contact List Query ---
// TODO: Implement query to fetch actual contacts based on the 'contacts' table.
// This might involve fetching IDs from the 'contacts' table for the current user
// and then fetching the corresponding user documents.
// export const getContactList = query({ ... });