// convex/users.ts
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

// Presence update frequency in milliseconds
const HEARTBEAT_INTERVAL = 30 * 1000; // 30 seconds

// Query to get a user by their ID
export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db.get(args.userId);
      if (!user) return null;
      return user;
    } catch (error) {
      console.error("Error in getUser:", error);
      return null;
    }
  },
});

// Query to get a user by their Clerk ID
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .first();
      return user;
    } catch (error) {
      console.error("Error in getUserByClerkId:", error);
      return null; // Return null on error for queries
    }
  },
});

// Query to get the currently authenticated user based on Convex identity
export const getCurrentUser = query({
  args: {}, // No arguments needed, uses context
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      return null; // Not logged in
    }

    try {
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject))
        .unique();

      return user; // Return the user document or null if not found
    } catch (error) {
      console.error("Error fetching current user by identity:", error);
      return null; // Return null on error
    }
  },
});

// Mutation to store or update user data from Clerk webhook or initial sign-in
export const storeUser = mutation({
  args: {
    clerkId: v.string(),
    name: v.string(),
    avatar: v.string(),
    email: v.optional(v.string()),
    theme: v.optional(v.string()),
    language: v.optional(v.string()),
    dataSavingMode: v.optional(v.boolean()),
    referralCode: v.optional(v.string()),
  },

  handler: async (ctx, args) => {
    try {
      const now = Date.now();
      // Check if user already exists
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .first();

      const isNewUser = !existingUser;

      if (existingUser) {
        // Update existing user with any new fields
        return await ctx.db.patch(existingUser._id, {
          email: args.email,
          name: args.name,
          avatar: args.avatar || existingUser.avatar,
          status: existingUser.status || "offline",
          theme: args.theme || existingUser.theme,
          language: args.language || existingUser.language,
          dataSavingMode: args.dataSavingMode ?? existingUser.dataSavingMode,
          updatedAt: now,
        }); 
      } else {
        // Check if this is a referred user
        const isReferred = !!args.referralCode;
        const referrerId = args.referralCode as Id<"users"> | undefined;
        
        // Create new user with proper typing
        const newUser: Omit<Doc<"users">, '_id' | '_creationTime'> = {
          clerkId: args.clerkId,
          email: args.email,
          name: args.name,
          avatar: args.avatar || "/default-avatar.png",
          status: "offline",
          theme: args.theme || "system",
          language: args.language || "en",
          dataSavingMode: args.dataSavingMode ?? false,
          
          // Moola economy fields
          moola: isReferred ? 100 : 0, // Start with 100 moola if referred, 0 otherwise
          referredBy: isReferred ? referrerId : undefined,
          referralCount: 0,
          loginStreak: 0,
          lastLoginReward: undefined,
          achievements: [],
          
          // Timestamps
          createdAt: now,
          updatedAt: now,
          lastSeen: now,
          
          // Optional fields
          statusMessage: undefined,
          mood: undefined
        };

        // Create the new user and return its ID
        return await ctx.db.insert("users", newUser); 
      }
    } catch (error) {
      console.error("Error in storeUser mutation:", error);
      throw error;
    }
  }
})

// Mutation to update only the user's online/offline/away status and lastSeen
// Update presence when user comes online, goes offline, or is active
export const updatePresence = mutation({
  args: {
    status: v.union(v.literal("online"), v.literal("away"), v.literal("offline")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get the user by Clerk ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const now = Date.now();
    const updates: any = {
      status: args.status,
      lastSeen: now,
      updatedAt: now,
    };

    // Only update lastActive if user is coming online
    if (args.status === 'online' && user.status !== 'online') {
      updates.lastActive = now;
    }

    await ctx.db.patch(user._id, updates);

    return { success: true };
  },
});

// Get presence for multiple users
export const getUsersPresence = query({
  args: {
    userIds: v.array(v.id("users")),
  },
  handler: async (ctx, args) => {
    const users = await Promise.all(
      args.userIds.map((userId) => ctx.db.get(userId))
    );

    return users
      .filter((user): user is NonNullable<typeof user> => user !== null)
      .map((user) => ({
        userId: user._id,
        status: user.status || 'offline',
        lastSeen: user.lastSeen || 0,
        lastActive: user.lastActive || 0,
      }));
  },
});

export const updateUserStatus = mutation({
  args: {
    // Requires the internal Convex user ID
    userId: v.id("users"),
    status: v.union(v.literal("online"), v.literal("offline"), v.literal("away")),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    // Only update if user exists and status is different
    if (!user || user.status === args.status) {
      return false;
    }
    await ctx.db.patch(args.userId, {
      status: args.status,
      lastSeen: Date.now(), // Update lastSeen timestamp
      updatedAt: Date.now(),
    });
    return true;
  },
});

// General mutation to update various optional user profile fields
export const updateUser = mutation({
  args: {
    // Requires the internal Convex user ID
    userId: v.id("users"),
    // --- Include all optional, updatable fields from your 'users' schema ---
    statusMessage: v.optional(v.string()),
    mood: v.optional(
      v.union(
        v.literal("happy"),
        v.literal("sad"),
        v.literal("excited"),
        v.literal("bored"),
        v.literal("busy"),
        v.literal("relaxed")
      )
    ),
    language: v.optional(v.string()),
    theme: v.optional(v.string()),
    dataSavingMode: v.optional(v.boolean()),
    // Add others like 'name', 'avatar' if they can be updated here too
    name: v.optional(v.string()),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;

    // Optional: Add authorization check - ensure logged-in user matches userId being updated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    const userToUpdate = await ctx.db.get(userId);
    if (!userToUpdate || userToUpdate.clerkId !== identity.subject) {
      throw new Error("Unauthorized to update this user profile.");
    }
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Construct object with only the fields that were actually provided
    const validUpdates: Partial<Doc<"users">> = {};
    let hasUpdates = false;
    
    // Explicitly check and assign each possible field
    if (updates.statusMessage !== undefined) {
      validUpdates.statusMessage = updates.statusMessage;
      hasUpdates = true;
    }
    if (updates.mood !== undefined) {
      validUpdates.mood = updates.mood;
      hasUpdates = true;
    }
    if (updates.language !== undefined) {
      validUpdates.language = updates.language;
      hasUpdates = true;
    }
    if (updates.theme !== undefined) {
      validUpdates.theme = updates.theme;
      hasUpdates = true;
    }
    if (updates.dataSavingMode !== undefined) {
      validUpdates.dataSavingMode = updates.dataSavingMode;
      hasUpdates = true;
    }
    if (updates.name !== undefined) {
      validUpdates.name = updates.name;
      hasUpdates = true;
    }
    if (updates.avatar !== undefined) {
      validUpdates.avatar = updates.avatar;
      hasUpdates = true;
    }

    // Only perform patch if there are actual fields to update
    if (hasUpdates) {
      validUpdates.updatedAt = Date.now(); // Update timestamp
      await ctx.db.patch(userId, validUpdates);
      return { success: true, updated: true };
    } else {
      // console.log("No valid fields provided to update for user:", userId);
      return { success: true, updated: false }; // Indicate success but no change made
    }
  },
});

// Query to get user's presence status
export const getUserPresence = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return null;
    
    // Consider user offline if no activity in last 2 minutes
    const isOnline = user.status === "online" || 
                    (user.status !== "offline" && user.lastSeen && 
                     Date.now() - user.lastSeen < 2 * 60 * 1000);
                     
    return {
      status: isOnline ? "online" : "offline",
      lastSeen: user.lastSeen || null,
    };
  },
});

// Query to get online users (excluding the current user)
export const getOnlineUsers = query({
    args: {
        userId: v.optional(v.id("users")), // Optional user ID to exclude
    },
    handler: async (ctx, args) => {
        try {
            // --- Recommended: Add index on 'status' in schema.ts ---
            // const onlineUsers = await ctx.db
            //    .query("users")
            //    .withIndex("by_status", q => q.eq("status", "online"))
            //    .collect();

            // Fallback filter (less efficient without index):
            const onlineUsers = await ctx.db
                .query("users")
                .filter((q) => q.eq(q.field("status"), "online"))
                .collect();

            // Exclude the current user from the list
            const currentUserId = args.userId;
            if (!currentUserId) {
                return onlineUsers;
            }
            return onlineUsers.filter(user => user._id !== currentUserId);
        } catch (error) {
            console.error("Error getting online users:", error)
            return [] // Return empty array on error
        }
    }
});

// Query to get a user's profile
export const getUserProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const user = await ctx.db.get(args.userId);
      if (!user) {
        return null;
      }

      return {
        _id: user._id,
        name: user.name,
        avatar: user.avatar,
        status: user.status,
        statusMessage: user.statusMessage,
        mood: user.mood,
        lastSeen: user.lastSeen,
        moola: user.moola,
        language: user.language,
        dataSavingMode: user.dataSavingMode,
      };
    } catch (error) {
      console.error("Error getting user profile:", error);
      return null;
    }
  },
});
// In convex/users.ts
export const getUserStatus = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return null;
    return {
      status: user.status,
      lastSeen: user.lastSeen,
    };
  },
});

// --- Placeholder for Contact List Query ---
// TODO: Implement query to fetch actual contacts based on the 'contacts' table.
// This might involve fetching IDs from the 'contacts' table for the current user
// and then fetching the corresponding user documents.
// export const getContactList = query({ ... });