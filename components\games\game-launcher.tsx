import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Loader2, Gamepad2, <PERSON><PERSON>1, <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { toast } from "sonner"

// Define game types for better type safety
type GameType = "tictactoe" | "rps" | "dice" | "numberguess"

interface GameLauncherProps {
  chatId: Id<"chats">
  userId: Id<"users">
  className?: string
}

export function GameLauncher({ chatId, userId, className = "" }: GameLauncherProps) {
  const [isLoading, setIsLoading] = useState<GameType | null>(null)
  const createGame = useMutation(api.games.createGame)

  const handleStartGame = async (gameType: GameType) => {
    setIsLoading(gameType)
    try {
      await createGame({
        gameType,
        chatId,
        player1Id: userId,
      })
      toast.success(`Started a new ${getGameName(gameType)} game!`)
    } catch (error) {
      console.error("Failed to create game:", error)
      toast.error("Failed to start game. Please try again.")
    } finally {
      setIsLoading(null)
    }
  }

  const getInitialGameData = (gameType: GameType) => {
    switch (gameType) {
      case "tictactoe":
        return {
          board: Array(3).fill(null).map(() => Array(3).fill(null)),
          status: "waiting" as const,
          currentPlayer: "x" as const,
          moves: []
        }
      case "rps":
        return {
          player1Choice: null,
          player2Choice: null,
          result: null,
          status: "waiting" as const,
          currentPlayer: "x" as const
        }
      case "dice":
        return {
          player1Roll: null,
          player2Roll: null,
          status: "waiting" as const,
          currentPlayer: "x" as const
        }
      case "numberguess":
        return {
          targetNumber: Math.floor(Math.random() * 100) + 1,
          guesses: [],
          status: "waiting" as const,
          currentPlayer: "x" as const
        }
      default:
        throw new Error(`Unsupported game type: ${gameType}`)
    }
  }

  const getGameName = (gameType: string) => {
    switch (gameType) {
      case "tictactoe":
        return "Tic-Tac-Toe"
      case "rps":
        return "Rock-Paper-Scissors"
      case "dice":
        return "Dice Roll"
      case "numberguess":
        return "Number Guess"
      default:
        return "Game"
    }
  }

  // Game menu items configuration
  const gameMenuItems: { type: GameType; icon: React.ReactNode; label: string; disabled?: boolean }[] = [
    {
      type: "tictactoe",
      icon: <X className="h-4 w-4" />,
      label: "Tic-Tac-Toe"
    },
    {
      type: "rps",
      icon: <Square className="h-4 w-4" />,
      label: "Rock-Paper-Scissors"
    },
    {
      type: "dice",
      icon: <Dice1 className="h-4 w-4" />,
      label: "Dice Roll"
    },
    {
      type: "numberguess",
      icon: <Hash className="h-4 w-4" />,
      label: "Number Guess"
    }
  ]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          className={`h-8 w-8 ${className} ${isLoading ? 'opacity-50' : ''}`}
          disabled={!!isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Gamepad2 className="h-4 w-4" />
          )}
          <span className="sr-only">Start a game</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {gameMenuItems.map((item) => (
          <DropdownMenuItem 
            key={item.type}
            onClick={() => !item.disabled && handleStartGame(item.type)}
            disabled={item.disabled || !!isLoading}
            className="flex items-center gap-2"
          >
            {isLoading === item.type ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              item.icon
            )}
            <span className={item.disabled ? 'opacity-50' : ''}>
              {item.label}
              {item.disabled && ' (Coming Soon)'}
            </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}