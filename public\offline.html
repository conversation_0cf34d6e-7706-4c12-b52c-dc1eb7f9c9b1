<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>XITchat - Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
      background-color: #f9fafb;
      color: #111827;
    }
    
    .container {
      max-width: 500px;
      padding: 30px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      font-weight: 600;
    }
    
    p {
      color: #6b7280;
      margin-bottom: 24px;
      line-height: 1.5;
    }
    
    .icon {
      width: 64px;
      height: 64px;
      margin-bottom: 20px;
      color: #6b7280;
    }
    
    .button {
      display: inline-flex;
      align-items: center;
      background-color: #4f46e5;
      color: white;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      text-decoration: none;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #4338ca;
    }
    
    .button-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #111827;
        color: #f9fafb;
      }
      
      .container {
        background-color: #1f2937;
      }
      
      p {
        color: #9ca3af;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m-3.536-3.536a3 3 0 010-5.656m-6.364 0a3 3 0 00-4.243 4.243m4.242-4.242L3 3m4.243 4.243L21 21"></path>
    </svg>
    <h1>You're offline</h1>
    <p>
      XITchat requires an internet connection. Please check your connection and try again.
    </p>
    <a href="/" class="button">
      <svg class="button-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
      Try Again
    </a>
  </div>
  
  <script>
    // Check if we're back online and reload the page
    window.addEventListener('online', () => {
      window.location.reload();
    });
    
    // Add click handler for the try again button
    document.querySelector('.button').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
