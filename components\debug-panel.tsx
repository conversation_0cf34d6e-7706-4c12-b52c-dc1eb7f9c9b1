// "use client"

// import { useState } from "react"
// import { useAuth } from "@/hooks/use-auth"
// import { useQuery } from "convex/react"
// import { api } from "@/convex/_generated/api"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
// import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
// import { ChevronDown, ChevronUp } from "lucide-react"

// export function DebugPanel() {
//   const [isOpen, setIsOpen] = useState(false)
//   const { userId, user, isLoading, isAuthenticated, error } = useAuth()

//   // Get some test data to verify Convex is working
//   const onlineUsers = useQuery(api.users.getOnlineContacts, userId ? { userId } : "skip")
//   const allUsers = useQuery(api.users.getContacts, userId ? { userId } : "skip")

//   return (
//     <div className="fixed bottom-16 right-4 z-50 md:bottom-4">
//       <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-80">
//         <CollapsibleTrigger asChild>
//           <Button variant="outline" size="sm" className="flex items-center gap-1">
//             Debug Panel {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
//           </Button>
//         </CollapsibleTrigger>
//         <CollapsibleContent>
//           <Card className="mt-2">
//             <CardHeader>
//               <CardTitle>Debug Information</CardTitle>
//               <CardDescription>Current state of the application</CardDescription>
//             </CardHeader>
//             <CardContent className="space-y-2 text-xs">
//               <div>
//                 <strong>Auth State:</strong>
//                 <ul className="ml-4 list-disc">
//                   <li>Loading: {isLoading ? "Yes" : "No"}</li>
//                   <li>Authenticated: {isAuthenticated ? "Yes" : "No"}</li>
//                   <li>User ID: {userId || "None"}</li>
//                   <li>Error: {error ? error.message : "None"}</li>
//                 </ul>
//               </div>

//               <div>
//                 <strong>User Data:</strong>
//                 <pre className="mt-1 bg-muted p-2 rounded text-xs overflow-auto max-h-20">
//                   {JSON.stringify(user, null, 2)}
//                 </pre>
//               </div>

//               <div>
//                 <strong>Online Users:</strong> {Array.isArray(onlineUsers) ? onlineUsers.length : "Loading..."}
//               </div>

//               <div>
//                 <strong>All Users:</strong> {Array.isArray(allUsers) ? allUsers.length : "Loading..."}
//               </div>
//             </CardContent>
//             <CardFooter>
//               <Button size="sm" onClick={() => window.location.reload()}>
//                 Refresh
//               </Button>
//             </CardFooter>
//           </Card>
//         </CollapsibleContent>
//       </Collapsible>
//     </div>
//   )
// }

