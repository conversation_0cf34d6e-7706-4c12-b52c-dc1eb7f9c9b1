import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { X, Square, Dice1, Hash, Gamepad2 } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { useAuth } from "@/hooks/use-auth"

interface GameListProps {
  chatId: Id<"chats">
  onGameClick: (gameId: Id<"games">, gameType: string) => void
}

export function GameList({ chatId, onGameClick }: GameListProps) {
  const { userId } = useAuth()
  const games = useQuery(api.games.getChatGames, { chatId }) || []

  const getGameIcon = (gameType: string) => {
    switch (gameType) {
      case "tictactoe":
        return <X className="h-4 w-4" />
      case "rps":
        return <Square className="h-4 w-4" />
      case "dice":
        return <Dice1 className="h-4 w-4" />
      case "numberguess":
        return <Hash className="h-4 w-4" />
      default:
        return <Gamepad2 className="h-4 w-4" />
    }
  }

  type GameStatus = {
    status: string;
    winner?: Id<"users"> | 'draw' | null;
    players: {
      x: Id<"users"> | null;
      o: Id<"users"> | null;
      currentPlayer: 'x' | 'o';
    };
  };

  const getGameStatus = (game: GameStatus) => {
    if (game.status === "completed") {
      if (!game.winner) return "Game ended in a draw"
      if (game.winner === userId) return "You won!"
      return `You ${game.winner === "draw" ? "drew" : "lost"}`
    }
    
    const currentPlayerId = game.players[game.players.currentPlayer]
    const isPlayerTurn = currentPlayerId === userId
    return isPlayerTurn ? "Your turn" : "Waiting for opponent"
  }

  if (games.length === 0) {
    return (
      <div className="text-center text-muted-foreground text-sm p-4">
        No active games in this chat. Start a new one!
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {games.map((game) => {
        const gameType = (game as any).gameType || 'tictactoe' // Default to tictactoe for backward compatibility
        return (
          <div
            key={game._id}
            className="flex items-center justify-between p-3 hover:bg-accent rounded-lg cursor-pointer"
            onClick={() => onGameClick(game._id, gameType)}
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-md bg-secondary">
                {getGameIcon(gameType)}
              </div>
              <div>
                <div className="font-medium capitalize">
                  {gameType.replace(/-/g, ' ')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(game._creationTime), { addSuffix: true })}
                </div>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              {getGameStatus(game as any)}
            </div>
          </div>
        )
      })}
    </div>
  )
}
