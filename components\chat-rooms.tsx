"use client"

import { useState } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Coins } from "lucide-react"

export function ChatRooms() {
  const { toast } = useToast()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const rooms = useQuery(api.chat.getChatroomsByCategory, selectedCategory ? { category: selectedCategory as any } : {})

  const user = useQuery(api.users.getCurrentUser)
  const purchaseMoola = useMutation(api.chat.purchaseMoola)

  const handlePurchaseMoola = async (amount: number) => {
    if (!user) return

    try {
      await purchaseMoola({
        amount,
        paymentMethod: "sms",
        transactionReference: `moola-purchase-${Date.now()}`,
      })

      toast({
        title: "Moola purchased!",
        description: `Successfully added ${amount} Moola to your account.`,
      })
    } catch (error) {
      toast({
        title: "Purchase failed",
        description: error instanceof Error ? error.message : "Failed to purchase Moola",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-4 p-4">
      {/* Moola Balance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5" />
            Moola Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{user?.moola || 0} Moola</div>
          <div className="mt-4 flex gap-2">
            <Button onClick={() => handlePurchaseMoola(200)}>Buy 200 Moola (R2)</Button>
            <Button onClick={() => handlePurchaseMoola(500)}>Buy 500 Moola (R5)</Button>
            <Button onClick={() => handlePurchaseMoola(1500)}>Buy 1500 Moola (R15 + 20 bonus)</Button>
          </div>
        </CardContent>
      </Card>

      {/* Chat Room Categories */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all" onClick={() => setSelectedCategory(null)}>
            All Rooms
          </TabsTrigger>
          {/* <TabsTrigger value="flirt" onClick={() => setSelectedCategory("flirt")}>
            Flirt */}
          {/* </TabsTrigger> */}
          <TabsTrigger value="teen" onClick={() => setSelectedCategory("teen")}>
            Teen
          </TabsTrigger>
          <TabsTrigger value="grown-up" onClick={() => setSelectedCategory("grown-up")}>
            Grown-up
          </TabsTrigger>
          <TabsTrigger value="kingdom" onClick={() => setSelectedCategory("kingdom")}>
            Kingdom
          </TabsTrigger>
          <TabsTrigger value="topical" onClick={() => setSelectedCategory("topical")}>
            Topical
          </TabsTrigger>
          <TabsTrigger value="geographical" onClick={() => setSelectedCategory("geographical")}>
            Geographical
          </TabsTrigger>
        </TabsList>

        <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {rooms?.map((room) => (
            <Card key={room._id}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{room.name}</span>
                  <Badge variant="secondary">{room.participantCount} online</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">{room.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{room.moolaPerMessage} Moola per message</span>
                  <Button>Join Room</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Tabs>
    </div>
  )
}

