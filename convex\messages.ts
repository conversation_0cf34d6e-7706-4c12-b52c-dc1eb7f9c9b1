import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { Id } from "./_generated/dataModel"

// Get messages for a chat
export const getMessages = query({
  args: { chatId: v.id("chats") },
  handler: async (ctx, args) => {
    try {
      // Get messages for the chat
      const messages = await ctx.db
        .query("messages")
        .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
        .order("asc")
        .collect()

      // Get sender info for each message
      const messagesWithSenders = await Promise.all(
        messages.map(async (message) => {
          if (!message.senderId) {
            return { ...message, sender: null }
          }

          const sender = await ctx.db.get(message.senderId as Id<"users">)
          return { ...message, sender }
        })
      )

      return messagesWithSenders
    } catch (error) {
      console.error("Error in getMessages:", error)
      return []
    }
  },
})

// Subscribe to new messages in a chat
export const onMessageAdded = query({
  args: { chatId: v.id("chats") },
  handler: async (ctx, args) => {
    try {
      // Get the most recent message for the chat
      const message = await ctx.db
        .query("messages")
        .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
        .order("desc")
        .first()

      if (!message) return null

      // Get sender info
      let sender = null
      if (message.senderId) {
        sender = await ctx.db.get(message.senderId as Id<"users">)
      }

      return { ...message, sender }
    } catch (error) {
      console.error("Error in onMessageAdded:", error)
      return null
    }
  },
})

// Send a message
export const sendMessage = mutation({
  args: {
    chatId: v.id("chats"),
    senderId: v.id("users"),
    content: v.string(),
    isNudge: v.optional(v.boolean()),
    isMultimix: v.optional(v.boolean()),
    multimixStyle: v.optional(v.string()),
    isVoiceNote: v.optional(v.boolean()),
    voiceNoteUrl: v.optional(v.string()),
    voiceNoteDuration: v.optional(v.number()),
    replyTo: v.optional(v.id("messages")),
    dataMode: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Validate chat exists
      const chat = await ctx.db.get(args.chatId)
      if (!chat) {
        throw new Error(`Chat ${args.chatId} not found`)
      }

      // Validate sender exists
      const sender = await ctx.db.get(args.senderId)
      if (!sender) {
        throw new Error(`User ${args.senderId} not found`)
      }

      // Check if user is a participant in the chat
      const participation = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat_and_user", (q) => q.eq("chatId", args.chatId).eq("userId", args.senderId))
        .first()

      if (!participation) {
        throw new Error(`User ${args.senderId} is not a participant in chat ${args.chatId}`)
      }

      // Handle Moola cost if applicable
      let moolaCost = 0
      if (args.dataMode === "moola") {
        // Calculate Moola cost based on message content and chat settings
        moolaCost = 0.1 // Default cost

        // Add additional cost for longer messages
        if (args.content.length > 100) {
          moolaCost += Math.floor(args.content.length / 100) * 0.05
        }

        // Add cost for attachments
        if (args.isVoiceNote || args.isMultimix) {
          moolaCost += 0.2
        }

        // Check if user has enough Moola
        if (sender.moola < moolaCost) {
          throw new Error("Insufficient Moola balance")
        }

        // Deduct Moola
        await ctx.db.patch(args.senderId, {
          moola: sender.moola - moolaCost,
          updatedAt: Date.now(),
        })

        // Record transaction
        await ctx.db.insert("moolaTransactions", {
          userId: args.senderId,
          amount: -moolaCost,
          type: "message_cost",
          description: `Message sent in ${chat.name || `Chat ${chat._id}`}`,
          timestamp: Date.now(),
        })
      }

      // Create message
      const messageId = await ctx.db.insert("messages", {
        chatId: args.chatId,
        senderId: args.senderId,
        content: args.content,
        timestamp: Date.now(),
        isNudge: args.isNudge || false,
        isVoiceNote: args.isVoiceNote || false,
        voiceNoteUrl: args.voiceNoteUrl,
        voiceNoteDuration: args.voiceNoteDuration,
        isMultimix: args.isMultimix || false,
        multimixStyle: args.multimixStyle,
        isDeleted: false,
        isSystemMessage: false,
        isRead: false,
        replyTo: args.replyTo,
        reactions: [],
        moolaCost: moolaCost > 0 ? moolaCost : undefined,
      })

      // Update chat's last message and timestamp
      await ctx.db.patch(args.chatId, {
        lastMessageId: messageId,
        updatedAt: Date.now(),
      })

      // Increment unread count for other participants
      const otherParticipants = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
        .filter((q) => q.neq(q.field("userId"), args.senderId))
        .collect()

      for (const participant of otherParticipants) {
        await ctx.db.patch(participant._id, {
          unreadCount: (participant.unreadCount || 0) + 1,
        })
      }

      return messageId
    } catch (error) {
      console.error("Error in sendMessage:", error)
      throw error
    }
  },
})

// Mark messages as read
export const markMessagesAsRead = mutation({
  args: {
    chatId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the chat participation for this user
      const participation = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat_and_user", (q) => q.eq("chatId", args.chatId).eq("userId", args.userId))
        .first()

      if (!participation) {
        throw new Error(`User ${args.userId} is not a participant in chat ${args.chatId}`)
      }

      // Update the unread count to 0
      await ctx.db.patch(participation._id, {
        unreadCount: 0,
      })

      return true
    } catch (error) {
      console.error("Error in markMessagesAsRead:", error)
      throw error
    }
  },
})

// Get total unread message count for a user
export const getUnreadMessageCount = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Get all chat participations for this user
      const participations = await ctx.db
        .query("chatParticipants")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .collect()

      // Sum up all unread counts
      const totalUnreadCount = participations.reduce((total, participation) => {
        return total + (participation.unreadCount || 0)
      }, 0)

      return totalUnreadCount
    } catch (error) {
      console.error("Error in getUnreadMessageCount:", error)
      return 0
    }
  },
})

// Delete a message (soft delete)
export const deleteMessage = mutation({
  args: {
    messageId: v.id("messages"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the message
      const message = await ctx.db.get(args.messageId)
      if (!message) {
        throw new Error(`Message ${args.messageId} not found`)
      }

      // Check if the user is the sender of the message
      if (message.senderId !== args.userId) {
        throw new Error(`User ${args.userId} is not authorized to delete message ${args.messageId}`)
      }

      // Soft delete the message by marking it as deleted
      await ctx.db.patch(args.messageId, {
        isDeleted: true,
        content: "This message was deleted",
        // Clear any attachments or special content
        voiceNoteUrl: undefined,
        multimixStyle: undefined,
      })

      return true
    } catch (error) {
      console.error("Error in deleteMessage:", error)
      throw error
    }
  },
})