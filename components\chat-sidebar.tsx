"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { useChats } from "@/hooks/use-chats"
import { formatDistanceToNow } from "date-fns"
import { Search } from "lucide-react"
import { LoadingSpinner } from "@/components/loading-spinner"
import { useAuth } from "@/hooks/use-auth"

export function ChatSidebar() {
  const params = useParams()
  const router = useRouter()
  const { userId } = useAuth()
  const [search, setSearch] = useState("")

  const currentChatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId

  const { chats, isLoading } = useChats()

  const filteredChats = chats?.filter((chat) => {
    if (!chat) return false

    // For direct chats, search by the other person's name
    if (chat.type === "direct") {
      const otherUser = chat.participants?.find((p: any) => p.id !== userId)
      return otherUser?.name.toLowerCase().includes(search.toLowerCase())
    }

    // For group chats and chatrooms, search by the chat name
    return chat.name?.toLowerCase().includes(search.toLowerCase())
  })

  // Sort chats by last message time
  const sortedChats = [...(filteredChats || [])].sort((a, b) => {
    // Fallback to 0 if no timestamp is available
    const aTime = a.lastMessage?.timestamp
      ? new Date(a.lastMessage.timestamp).getTime()
      : a.updatedAt ? new Date(a.updatedAt).getTime() : 0
    const bTime = b.lastMessage?.timestamp
      ? new Date(b.lastMessage.timestamp).getTime()
      : b.updatedAt ? new Date(b.updatedAt).getTime() : 0
    return bTime - aTime
  })

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <h2 className="text-xl font-bold mb-4">Messages</h2>
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contacts..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="space-y-1 p-2">
          {sortedChats?.map((chat) => {
            const isActive = chat.id === currentChatId
            const otherUser = chat.type === "direct" ? chat.participants?.find((p: any) => p.id !== userId) : null

            const chatName = chat.type === "direct" ? otherUser?.name : chat.name

            const lastMessageTime = chat.lastMessage?.timestamp
              ? formatDistanceToNow(new Date(chat.lastMessage.timestamp), { addSuffix: false })
              : ""

            const isOnline = chat.type === "direct" && otherUser?.status === "online"

            return (
              <Button
                key={chat.id}
                variant="ghost"
                className={`w-full justify-start px-2 py-3 h-auto ${isActive ? "bg-muted" : ""}`}
                onClick={() => router.push(`/chat/${chat.id}`)}
              >
                <div className="flex items-start gap-3 w-full">
                  <div className="relative">
                    <Avatar>
                      <AvatarImage
                        src={
                          otherUser?.avatar || `/placeholder.svg?height=40&width=40&text=${chatName?.substring(0, 2)}`
                        }
                      />
                      <AvatarFallback>{chatName?.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    {isOnline && (
                      <span className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-background"></span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-center">
                      <p className="font-medium truncate">{chatName}</p>
                      <span className="text-xs text-muted-foreground whitespace-nowrap">{lastMessageTime}</span>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {chat.lastMessage?.content || "No messages yet"}
                    </p>
                  </div>
                  {(chat.unreadCount ?? 0) > 0 && (
                    <Badge className="ml-auto shrink-0">{chat.unreadCount}</Badge>
                  )}
                </div>
              </Button>
            )
          })}
        </div>
      </ScrollArea>
    </div>
  )
}

