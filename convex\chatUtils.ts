import { mutation } from "./_generated/server";
import { v } from "convex/values";

// List of valid chat categories
export const CHAT_CATEGORIES = [
  "kasi-vibes",
  "campus",
  "sports",
  "music",
  "flirt",
  "local-news",
  "anonymous",
  "teen",
  "grown-up",
  "topical",
  // Temporary: Keep 'kingdom' for backward compatibility
  "kingdom",
] as const;

export type ChatCategory = (typeof CHAT_CATEGORIES)[number];

/**
 * Validates and normalizes a chat category.
 * Returns the default category if the input is invalid.
 */
export function validateChatCategory(category: string | undefined): ChatCategory {
  if (!category) return "kasi-vibes";
  
  // Special case: map 'kingdom' to 'kasi-vibes' for backward compatibility
  if (category === "kingdom") {
    console.warn("Chat category 'kingdom' is deprecated. Using 'kasi-vibes' instead.");
    return "kasi-vibes";
  }
  
  // Check if the category is valid
  if (CHAT_CATEGORIES.includes(category as ChatCategory)) {
    return category as ChatCategory;
  }
  
  console.warn(`Invalid chat category: ${category}. Defaulting to 'kasi-vibes'`);
  return "kasi-vibes";
}

// Validator for chat categories
export const chatCategoryValidator = v.union(
  ...CHAT_CATEGORIES.map((c) => v.literal(c))
);

// Helper function to validate chat data before saving
export function validateChatData(data: any) {
  if (data.category) {
    data.category = validateChatCategory(data.category);
  }
  return data;
}
