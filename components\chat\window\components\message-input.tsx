"use client";

import { useState, useRef, useEffect } from "react";
import type { Dispatch, SetStateAction } from "react";
import { useAuth } from "@/hooks/use-auth";

// Server actions will be imported here if needed
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Send, 
  Mic, 
  Image as ImageIcon, 
  Smile, 
  Type,
  Gift,
  Palette
} from "lucide-react";
import { EmojiPicker } from "@/components/chat/emoji-picker";
import { VoiceRecorder } from "@/components/chat/voice-recorder";
import { ImageUpload } from "@/components/chat/image-upload";
import { GiftMoolaDialog } from "@/components/moola/gift-moola-dialog";
import { ChatBackgroundPicker } from "@/components/chat/chat-background-picker";
import { GameLauncher } from "@/components/games/game-launcher";
import { Id } from "@/convex/_generated/dataModel";

// Base props interface with all possible props
type MessageInputBaseProps = {
  // Value and change handler (new style)
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: (e: React.FormEvent) => Promise<void>;
  
  // Message value and change handler (old style)
  message?: string;
  
  // UI state
  showEmojiPicker?: boolean;
  showVoiceRecorder?: boolean;
  showMultiMixEditor?: boolean;
  showImageUpload?: boolean;
  showGiftDialog?: boolean;
  showBackgroundPicker?: boolean;
  isMobile?: boolean;
  dataSavingMode?: boolean;
  dataMode?: "mobile" | "moola" | "emergency";
  chatId?: Id<"chats">;
  
  // Action handlers (new style)
  onToggleEmojiPicker?: () => void;
  onSendMessage?: (e: React.FormEvent) => Promise<void>;
  
  // Action handlers (old style)
  onMessageChangeAction?: (value: string) => void;
  onSendMessageAction?: (e: React.FormEvent) => Promise<void>;
  onEmojiSelectAction?: (emoji: string) => void;
  onVoiceRecordedAction?: (audioBlob: Blob) => Promise<void>;
  onImageSelectedAction?: (imageUrl: string) => Promise<void>;
  onSendNudgeAction?: () => void;
  
  // State setters (with Action suffix to indicate Server Actions)
  setShowEmojiPickerAction?: (value: boolean) => void;
  setShowVoiceRecorderAction?: (value: boolean) => void;
  setShowMultiMixEditorAction?: (value: boolean) => void;
  setShowImageUploadAction?: (value: boolean) => void;
  setShowGiftDialogAction?: (value: boolean) => void;
  setShowBackgroundPickerAction?: (value: boolean) => void;
  
  // User ID for game launcher
  userId?: Id<"users">;
}

// Make all props optional and provide defaults
type MessageInputProps = MessageInputBaseProps & {
  // Add any required props here if needed
}

// Support both old and new prop names for backward compatibility
export const MessageInput: React.FC<MessageInputProps> = ({
  // Value and change handler (with proper defaults)
  message = '',
  value: valueProp = message || '',
  onChange,
  onMessageChangeAction,
  
  // UI state (with defaults)
  showEmojiPicker = false,
  showVoiceRecorder = false,
  showMultiMixEditor = false,
  showImageUpload = false,
  showGiftDialog = false,
  showBackgroundPicker = false,
  isMobile = false,
  dataSavingMode = false,
  dataMode = "mobile",
  chatId = '' as Id<"chats">,
  userId = '' as Id<"users">,
  
  // Submit handlers
  onSendMessageAction,
  onSubmit,
  onSendMessage = onSendMessageAction || onSubmit,
  
  // Other action handlers (with safe defaults)
  onEmojiSelectAction = () => {},
  onVoiceRecordedAction = async () => {},
  onImageSelectedAction = async () => {},
  onSendNudgeAction = () => {},
  
  // State setters (with safe defaults) - renamed to end with Action to indicate they are Server Actions
  setShowEmojiPickerAction = () => {},
  setShowVoiceRecorderAction = () => {},
  setShowMultiMixEditorAction = () => {},
  setShowImageUploadAction = () => {},
  setShowGiftDialogAction = () => {},
  setShowBackgroundPickerAction = () => {},
  
  // Toggle handlers
  onToggleEmojiPicker = () => setShowEmojiPickerAction(!showEmojiPicker),
}) => {
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Use the new onSubmit or onSendMessage props if provided, otherwise fall back to onSendMessageAction
    if (onSubmit) {
      await onSubmit(e);
    } else if (onSendMessage) {
      await onSendMessage(e);
    } else if (onSendMessageAction) {
      await onSendMessageAction(e);
    }
    if (message.trim()) {
      setShowEmojiPickerAction(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    onEmojiSelectAction(emoji);
    setShowEmojiPickerAction(false);
    inputRef.current?.focus();
  };

  const handleImageSelected = async (imageUrl: string) => {
    if (onImageSelectedAction) {
      await onImageSelectedAction(imageUrl);
    }
    setShowImageUploadAction(false);
  };

  const handleVoiceRecordingComplete = async (audioBlob: Blob) => {
    await onVoiceRecordedAction(audioBlob);
    setShowVoiceRecorderAction(false);
    setTimeout(() => {
      setShowVoiceRecorderAction(false);
      inputRef.current?.focus();
    }, 100);
  };

  // Gift sending is now handled directly in the GiftMoolaDialog component

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value || ''; // Ensure we never pass undefined
    // Use the new onChange prop if provided, otherwise fall back to onMessageChangeAction
    if (onChange) {
      onChange(newValue);
    } else if (onMessageChangeAction) {
      onMessageChangeAction(newValue);
    }
  };

  // Ensure we have a controlled value at all times
  const controlledValue = valueProp || '';

  return (
    <div className="border-t p-2 bg-background">
      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute bottom-16 right-4 z-10">
          <EmojiPicker onEmojiSelect={handleEmojiSelect} />
        </div>
      )}

      {/* Voice Recorder */}
      {showVoiceRecorder && (
        <div className="mb-2">
          <VoiceRecorder
            onVoiceRecorded={handleVoiceRecordingComplete}
            onCancel={() => setShowVoiceRecorderAction(false)}
          />
        </div>
      )}

      {/* Image Upload */}
      <ImageUpload
        open={showImageUpload}
        onOpenChange={setShowImageUploadAction}
        onImageSelected={async (imageUrl: string) => {
          await handleImageSelected(imageUrl);
          setShowImageUploadAction(false);
        }}
      />

      {/* Gift Dialog */}
      <GiftMoolaDialog
        open={showGiftDialog}
        onOpenChange={(open) => setShowGiftDialogAction(open)}
        recipientId="" // TODO: Get recipient ID from props or context
        recipientName="" // TODO: Get recipient name from props or context
      />

      {/* Background Picker */}
      {showBackgroundPicker && (
        <ChatBackgroundPicker
          chatId={chatId}
          currentBackground="none"
          onBackgroundChange={(background: string) => {
            // Handle background selection
            console.log("Selected background:", background);
            setShowBackgroundPickerAction(false);
            // TODO: Save the selected background
          }}
        />
      )}

      {/* Input Area */}
      <div className="border-t border-gray-200 dark:border-gray-800 p-2">
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          {/* Left side buttons */}
          <div className="flex items-center">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onToggleEmojiPicker}
              className="text-muted-foreground hover:text-foreground h-9 w-9"
            >
              <Smile className="h-5 w-5" />
            </Button>
            
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setShowVoiceRecorderAction(!showVoiceRecorder)}
              className="text-muted-foreground hover:text-foreground h-9 w-9"
            >
              <Mic className="h-5 w-5" />
            </Button>
            
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setShowImageUploadAction(!showImageUpload)}
              className="text-muted-foreground hover:text-foreground h-9 w-9"
            >
              <ImageIcon className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Message input */}
          <div className="relative flex-1">
            <Input
              ref={inputRef}
              type="text"
              placeholder="Type a message..."
              className="w-full pr-10 min-h-[36px]"
              value={controlledValue}
              onChange={handleInputChange}
              onFocus={() => setIsTyping(true)}
              onBlur={() => setIsTyping(false)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            />
            
            {/* Send button - only shows when there's text */}
            {controlledValue.trim() && (
              <Button
                type="submit"
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 -translate-y-1/2 text-primary hover:bg-transparent hover:text-primary/80 h-8 w-8"
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          {/* Right side buttons */}
          <div className="flex items-center">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setShowGiftDialogAction(true)}
              className="text-muted-foreground hover:text-foreground h-9 w-9"
            >
              <Gift className="h-5 w-5" />
            </Button>
            
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setShowBackgroundPickerAction(true)}
              className="text-muted-foreground hover:text-foreground h-9 w-9"
            >
              <Palette className="h-5 w-5" />
            </Button>
          </div>
        </form>
        
        {/* Additional UI elements */}
        {showEmojiPicker && (
          <div className="mt-2">
            <EmojiPicker onEmojiSelect={handleEmojiSelect} />
          </div>
        )}
        
        {showVoiceRecorder && (
          <div className="mt-2">
            <VoiceRecorder 
              onVoiceRecorded={handleVoiceRecordingComplete}
              onCancel={() => setShowVoiceRecorderAction(false)}
            />
          </div>
        )}
        
        {showImageUpload && (
          <div className="mt-2">
            <ImageUpload 
              open={showImageUpload}
              onOpenChange={setShowImageUploadAction}
              onImageSelected={handleImageSelected}
            />
          </div>
        )}
      </div>
    </div>
  );
};
