// Service Worker for XitChat PWA

const CACHE_NAME = "xitchat-v1"

// Assets to cache on install - only include files we know exist
const STATIC_ASSETS = [
  "/",
  "/manifest.json",
  "/offline.html", // Special offline page for Safari
  "/icons/icon-72x72.svg",
  "/icons/icon-96x96.svg",
  "/icons/icon-128x128.svg",
  "/icons/icon-144x144.svg",
  "/icons/icon-152x152.svg",
  "/icons/icon-192x192.svg",
  "/icons/icon-384x384.svg",
  "/icons/icon-512x512.svg",
  "/icon.png" // Fallback icon
]

// Install event - cache static assets
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log("Opened cache")
      return cache.addAll(STATIC_ASSETS)
    }),
  )
  // Activate immediately
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.filter((cacheName) => cacheName !== CACHE_NAME).map((cacheName) => caches.delete(cacheName)),
      )
    }),
  )
  // Take control of all clients immediately
  self.clients.claim()
})

// Fetch event - serve from cache or network with Safari compatibility
self.addEventListener("fetch", (event) => {
  // Check if the request is for Clerk or authentication
  const url = event.request.url;

  // Skip all Clerk and authentication-related requests completely
  if (url.includes('clerk') ||
      url.includes('sign-in') ||
      url.includes('sign-up') ||
      url.includes('auth') ||
      url.includes('session')) {
    return; // Let the browser handle these requests normally
  }
  // Skip non-GET requests and browser extensions
  if (
    event.request.method !== "GET" ||
    event.request.url.startsWith("chrome-extension") ||
    event.request.url.includes("extension") ||
    // Skip Convex API requests
    event.request.url.includes("convex") ||
    // Skip Clerk authentication requests and related paths
    event.request.url.includes("clerk.dev") ||
    event.request.url.includes("clerk.com") ||
    // Skip any URLs with redirects (fixes Safari issue)
    event.request.url.includes("redirect") ||
    event.request.url.includes("callback") ||
    event.request.url.includes("oauth") ||
    event.request.url.includes("sso") ||
    // Skip authentication-related paths that might redirect
    event.request.url.includes("/sign-in") ||
    event.request.url.includes("/sign-up") ||
    event.request.url.includes("/sign-out") ||
    event.request.url.includes("/signin") ||
    event.request.url.includes("/signup") ||
    event.request.url.includes("/signout") ||
    event.request.url.includes("/login") ||
    event.request.url.includes("/logout") ||
    event.request.url.includes("/register") ||
    event.request.url.includes("/auth") ||
    event.request.url.includes("/session") ||
    event.request.url.includes("/user") ||
    event.request.url.includes("/verify") ||
    event.request.url.includes("/reset-password") ||
    event.request.url.includes("/forgot-password")
  ) {
    return
  }

  // Special handling for navigation requests
  if (event.request.mode === "navigate") {
    event.respondWith(
      (async () => {
        try {
          // Try to use the navigation preload response if it's supported
          const preloadResponse = await event.preloadResponse;
          if (preloadResponse) {
            return preloadResponse;
          }

          // Otherwise, try to get the resource from the network
          const networkResponse = await fetch(event.request);
          return networkResponse;
        } catch (error) {
          // If both preload and network fail, fallback to the offline page
          // Use the special offline page for Safari
          const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent || '');
          const offlinePage = isSafari ? '/offline.html' : '/';
          const cachedResponse = await caches.match(offlinePage);
          return cachedResponse;
        }
      })()
    );
    return;
  }

  // For non-navigation requests, use the standard cache-first strategy
  event.respondWith(
    caches.match(event.request).then((response) => {
      // Return cached response if found
      if (response) {
        return response
      }

      // Clone the request
      const fetchRequest = event.request.clone()

      // Try to fetch from network
      return fetch(fetchRequest)
        .then((response) => {
          // Don't cache redirects or error responses for Safari compatibility
          if (!response || !response.ok || response.type === "opaqueredirect") {
            return response
          }

          // Clone the response
          const responseToCache = response.clone()

          // Cache the response
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache)
          })

          return response
        })
        .catch(() => {
          // For non-navigation requests that fail, return an empty response
          return new Response("", {
            status: 408,
            headers: { "Content-Type": "text/plain" },
          })
        })
    }),
  )
})

