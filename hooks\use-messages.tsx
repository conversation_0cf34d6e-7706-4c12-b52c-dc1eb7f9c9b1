"use client"

import { useState, useEffect, useCallback } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/components/ui/use-toast"
import type { Id, Doc } from "@/convex/_generated/dataModel" // Import Doc type

// Define or import your Message type (example structure)
type MessageWithSender = Doc<"messages"> & {
    sender: Doc<"users"> | null;
};

// Define offline message type
type OfflineMessage = {
  id: string;
  chatId: string;
  content: string;
  timestamp: number;
  options: any;
  moolaCost: number;
  isNudge?: boolean;
  isMultimix?: boolean;
  multimixStyle?: string;
  isVoiceNote?: boolean;
  voiceNoteUrl?: string;
  voiceNoteDuration?: number;
  replyTo?: string;
};

export function useMessages(chatId: string | null, onNewMessage?: () => void) {
  const { userId, user, isLoading: isUserLoading } = useAuth(); // Get current user ID and user data
  const [messages, setMessages] = useState<MessageWithSender[]>([]); // Use specific type
  const [offlineQueue, setOfflineQueue] = useState<OfflineMessage[]>([]);
  const [localMoolaBalance, setLocalMoolaBalance] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Check if chatId is valid before passing it to the query
  // We'll be more lenient and just check if it's a non-empty string
  const isValidChatId = chatId && typeof chatId === "string" && chatId.length > 0;

  // --- Convex Queries & Mutations ---
  const fetchedMessages = useQuery(
    api.messages.getMessages,
    isValidChatId ? { chatId: chatId as Id<"chats"> } : "skip"
  );
  const sendMessageMutation = useMutation(api.messages.sendMessage);
  const markAsReadMutation = useMutation(api.messages.markMessagesAsRead);
  const newMessage = useQuery(
    api.messages.onMessageAdded,
    isValidChatId ? { chatId: chatId as Id<"chats"> } : "skip"
  );

  // Add debug logging
  useEffect(() => {
    console.log(`useMessages hook - chatId: ${chatId}, isValidChatId: ${isValidChatId}`);

    // Log a warning if chatId is not valid
    if (chatId && !isValidChatId) {
      console.warn(`Invalid chatId format: ${chatId}. Expected a non-empty string.`);
    }
  }, [chatId, isValidChatId]);

  // --- Effects ---

  // Import toast for notifications
  const { toast } = useToast();

  // Effect to load offline queue and local Moola balance from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Load offline queue
      const savedQueue = localStorage.getItem('xitchat-offline-queue');
      if (savedQueue) {
        try {
          const parsedQueue = JSON.parse(savedQueue);
          setOfflineQueue(parsedQueue);
        } catch (e) {
          console.error('Failed to parse offline queue:', e);
          localStorage.removeItem('xitchat-offline-queue');
        }
      }

      // Load local Moola balance
      const savedBalance = localStorage.getItem('xitchat-moola-balance');
      if (savedBalance) {
        try {
          const parsedBalance = Number(savedBalance);
          setLocalMoolaBalance(parsedBalance);
        } catch (e) {
          console.error('Failed to parse local Moola balance:', e);
          localStorage.removeItem('xitchat-moola-balance');
        }
      } else if (user?.moola !== undefined) {
        // Initialize from user data if available
        setLocalMoolaBalance(user.moola);
        localStorage.setItem('xitchat-moola-balance', String(user.moola));
      }
    }
  }, [user?.moola]);

  // Effect to sync offline messages when coming back online
  useEffect(() => {
    if (!userId) return; // Skip if user is not authenticated

    const handleOnline = async () => {
      if (offlineQueue.length > 0) {
        toast({
          title: "Syncing messages",
          description: `Sending ${offlineQueue.length} offline messages...`
        });

        let successCount = 0;
        let failCount = 0;

        for (const message of offlineQueue) {
          try {
            await sendMessageMutation({
              chatId: message.chatId as Id<"chats">,
              senderId: userId as Id<"users">,
              content: message.content,
              isNudge: message.isNudge,
              isMultimix: message.isMultimix,
              multimixStyle: message.multimixStyle,
              isVoiceNote: message.isVoiceNote,
              voiceNoteUrl: message.voiceNoteUrl,
              voiceNoteDuration: message.voiceNoteDuration,
              replyTo: message.replyTo as Id<"messages"> | undefined
              // Note: offlineTimestamp is not in the API, so we don't include it
            });
            successCount++;
          } catch (error) {
            console.error("Failed to sync offline message:", error);
            failCount++;
          }
        }

        // Clear queue after sync
        setOfflineQueue([]);
        localStorage.removeItem('xitchat-offline-queue');

        // Update user about sync results
        toast({
          title: "Sync complete",
          description: `${successCount} messages sent${failCount > 0 ? `, ${failCount} failed` : ''}`
        });

        // Refresh local Moola balance from server
        if (user?.moola !== undefined) {
          setLocalMoolaBalance(user.moola);
          localStorage.setItem('xitchat-moola-balance', String(user.moola));
        }
      }
    };

    // Add event listener for online events
    window.addEventListener('online', handleOnline);

    // Check if we're already online and have queued messages
    if (navigator.onLine && offlineQueue.length > 0) {
      handleOnline();
    }

    return () => window.removeEventListener('online', handleOnline);
  }, [offlineQueue, userId, sendMessageMutation, toast, user?.moola]);

  // Effect to handle initial loading state based on query status
  useEffect(() => {
    if (!isValidChatId) {
      // console.log("useMessages: Invalid chat ID, clearing state.", chatId);
      setMessages([]);
      setIsLoading(false);
      setError(null);
    } else {
      // Reset loading state when chatId changes and is valid
      setIsLoading(true);
      setError(null); // Clear previous errors
    }
  }, [chatId, isValidChatId]);


  // Effect to handle fetched messages and initial mark as read
  useEffect(() => {
    // console.log("useMessages: fetchedMessages effect", { fetchedMessages, isValidChatId, userId });

    // Only proceed if the query didn't skip and has returned a value (even an empty array)
    if (isValidChatId && fetchedMessages !== undefined) {
      setMessages(fetchedMessages as MessageWithSender[]); // Update messages
      setIsLoading(false); // Mark loading as complete

      // Mark messages as read for the *current* user when messages are loaded/updated
      if (userId && fetchedMessages.length > 0) { // Only mark if there are messages
         // console.log(`useMessages: Attempting initial markAsRead for user ${userId} in chat ${chatId}`);
         markAsReadMutation({
            chatId: chatId as Id<"chats">,
            userId: userId as Id<"users">, // CRITICAL: Ensure this userId is correct
          }).catch(err => {
             console.error("useMessages: Failed initial markAsRead:", err)
             // Optionally set error state: setError(err);
          });
      }
    } else if (!isValidChatId) {
        // If chat ID becomes invalid while query was running, ensure loading is off
        setIsLoading(false);
    }
  }, [fetchedMessages, userId, chatId, markAsReadMutation, isValidChatId]); // Dependencies

  // Effect to handle newly added messages
  useEffect(() => {
    // console.log("useMessages: newMessage effect", { newMessage, messagesCount: messages.length, isValidChatId, userId });

    if (newMessage && !messages.some((m) => m._id === newMessage._id)) {
      // Enrich new message with sender info if needed (or handle it in the component)
      // Assuming newMessage from onMessageAdded might need sender enrichment if not done on backend
      const enrichedNewMessage = newMessage as MessageWithSender; // Adjust if enrichment needed

      setMessages((prevMessages) => [...prevMessages, enrichedNewMessage]);
      onNewMessage?.(); // Trigger scroll or other UI updates

      // Mark as read if the new message is from *another* user
      if (newMessage.senderId !== userId && userId && isValidChatId) {
        // console.log(`useMessages: Attempting markAsRead for new message from other user ${newMessage.senderId} by user ${userId} in chat ${chatId}`);
        markAsReadMutation({
            chatId: chatId as Id<"chats">,
            userId: userId as Id<"users">, // CRITICAL: Ensure this userId is correct
          }).catch(err => {
             console.error("useMessages: Failed markAsRead on new message:", err);
             // Optionally set error state: setError(err);
          });
      }
    }
  }, [newMessage, messages, userId, chatId, markAsReadMutation, onNewMessage, isValidChatId]); // Dependencies

  // --- Actions ---

  // Function to estimate Moola cost for a message
  const estimateMessageCost = (content: string, hasAttachments: boolean = false) => {
    // Get rates from lib/moola-services.ts
    const BASE_COST = 0.1; // Base cost per message
    const ATTACHMENT_COST = 0.5; // Additional cost for attachments
    const LONG_MESSAGE_COST = 0.05; // Additional cost per 500 chars over 500

    let cost = BASE_COST;

    // Add cost for longer messages
    if (content.length > 500) {
      cost += Math.floor((content.length - 500) / 500) * LONG_MESSAGE_COST;
    }

    // Add cost for attachments
    if (hasAttachments) {
      cost += ATTACHMENT_COST;
    }

    // Round to 1 decimal place
    return Math.ceil(cost * 10) / 10;
  };

  const sendMessage = useCallback(
    async (content: string, options: any = {}) => {
      // Guard clause with detailed error information
      if (!isValidChatId || !userId) {
         const errorMsg = `Cannot send message: ${!isValidChatId ? 'invalid chatId' : 'missing userId'}`;
         console.error(errorMsg, {
           chatId,
           userId,
           isValidChatId,
           chatIdType: chatId ? typeof chatId : 'null',
           chatIdLength: chatId ? chatId.length : 0
         });
         throw new Error(errorMsg);
      }

      // Check if we're offline
      if (!navigator.onLine) {
        // Estimate Moola cost
        const hasAttachments = options.isVoiceNote || (options.voiceNoteUrl !== undefined);
        const estimatedCost = estimateMessageCost(content, hasAttachments);

        // Create offline message
        const offlineMessage: OfflineMessage = {
          id: crypto.randomUUID(),
          chatId: chatId as string,
          content,
          timestamp: Date.now(),
          options,
          moolaCost: estimatedCost,
          isNudge: options.isNudge,
          isMultimix: options.isMultimix,
          multimixStyle: options.multimixStyle,
          isVoiceNote: options.isVoiceNote,
          voiceNoteUrl: options.voiceNoteUrl,
          voiceNoteDuration: options.voiceNoteDuration,
          replyTo: options.replyTo
        };

        // Add to queue
        const newQueue = [...offlineQueue, offlineMessage];
        setOfflineQueue(newQueue);
        localStorage.setItem('xitchat-offline-queue', JSON.stringify(newQueue));

        // Update local Moola balance
        const newBalance = localMoolaBalance - estimatedCost;
        setLocalMoolaBalance(newBalance);
        localStorage.setItem('xitchat-moola-balance', String(newBalance));

        // Add temporary message to UI - use any type to bypass type checking for offline messages
        const tempMessage: any = {
          _id: offlineMessage.id, // Temporary ID
          _creationTime: offlineMessage.timestamp,
          chatId: chatId,
          senderId: userId,
          content: offlineMessage.content,
          timestamp: offlineMessage.timestamp,
          isNudge: offlineMessage.isNudge || false,
          isVoiceNote: offlineMessage.isVoiceNote || false,
          voiceNoteUrl: offlineMessage.voiceNoteUrl,
          voiceNoteDuration: offlineMessage.voiceNoteDuration,
          isMultimix: offlineMessage.isMultimix || false,
          multimixStyle: offlineMessage.multimixStyle,
          isRead: true,
          isSystemMessage: false,
          isDeleted: false,
          reactions: [],
          // Simplified sender info
          sender: {
            _id: userId,
            name: user?.name || 'You',
            avatar: user?.avatar || ''
          },
          _offline: true // Mark as offline message
        };

        setMessages(prev => [...prev, tempMessage]);
        onNewMessage?.(); // Trigger scroll or other UI updates

        toast({
          title: "Offline Mode",
          description: "Message saved and will be sent when you're back online"
        });

        return offlineMessage.id; // Return temporary ID
      }

      try {
        // Get the data mode from options or use mobile as default
        const dataMode = options.dataMode || "mobile";

        // If using Moola data mode, we need to handle potential errors
        // like insufficient balance or emergency Moola depletion
        if (dataMode === "moola" || dataMode === "emergency") {
          try {
            // If we have useDataMode hook available, we should use it to record the message
            // This is just a fallback in case it's not available
            if (typeof window !== 'undefined' && (window as any).recordMessageSent) {
              // Call the global recordMessageSent function if available
              await (window as any).recordMessageSent(content.length, !!options.isVoiceNote || !!options.isMultimix);
            }
          } catch (moolaError: any) {
            // If there's an error with Moola (like insufficient balance),
            // we can either throw the error or fallback to mobile data
            console.warn("Moola error, falling back to mobile data:", moolaError);
            options.dataMode = "mobile"; // Fallback to mobile data
          }
        }

        // Send the message using the Convex mutation
        const result = await sendMessageMutation({
          chatId: chatId as Id<"chats">,
          senderId: userId as Id<"users">,
          content,
          isNudge: options.isNudge || false,
          isMultimix: options.isMultimix || false,
          multimixStyle: options.multimixStyle,
          isVoiceNote: options.isVoiceNote || false,
          voiceNoteUrl: options.voiceNoteUrl,
          voiceNoteDuration: options.voiceNoteDuration,
          replyTo: options.replyTo ? options.replyTo as Id<"messages"> : undefined,
          dataMode: options.dataMode || "mobile", // Use the potentially updated dataMode
        });
        return result;
      } catch (err: any) {
        console.error("useMessages: Error sending message:", err);
        setError(err); // Optionally expose error via state
        throw err; // Re-throw for component-level handling
      }
    },
    [isValidChatId, chatId, userId, sendMessageMutation] // Dependencies
  );

  const sendNudge = useCallback(async (dataMode: string = "mobile") => {
    if (!isValidChatId || !userId) {
        const errorMsg = `Cannot send nudge: ${!isValidChatId ? 'invalid chatId' : 'missing userId'}`;
        console.error(errorMsg, { chatId, userId });
        throw new Error(errorMsg);
    }

    try {
      // Define the type for dataMode
      type DataMode = "mobile" | "moola" | "emergency";
      let currentDataMode: DataMode = dataMode as DataMode || "mobile";

      // Check if we're in Moola mode and have enough balance
      if (currentDataMode === "moola") {
        try {
          // @ts-ignore - This is a global function injected by the Moola service worker
          if (typeof window !== 'undefined' && window.recordMessageSent) {
            // Record the nudge in Moola (assuming it costs 50 units)
            await (window as any).recordMessageSent(50, false);
          }
        } catch (moolaError: any) {
          console.warn("Moola error when sending nudge, falling back to mobile data:", moolaError);
          currentDataMode = "mobile"; // Fallback to mobile data
        }
      }

      await sendMessageMutation({
        chatId: chatId as Id<"chats">,
        senderId: userId as Id<"users">,
        content: "sent a nudge", // Standard nudge content
        isNudge: true,
        isMultimix: false, // Nudges aren't multimix
        dataMode: currentDataMode, // Use the typed dataMode
      });
    } catch (err: any) {
      console.error("useMessages: Error sending nudge:", err);
      setError(err);
      throw err;
    }
  }, [isValidChatId, chatId, userId, sendMessageMutation]); // Dependencies

  // Exported function to manually trigger mark as read if needed elsewhere
  const triggerMarkMessagesAsRead = useCallback(async () => {
      if (!isValidChatId || !userId) {
          console.warn("Cannot trigger markMessagesAsRead: Invalid chatId or missing userId", { chatId, userId });
          return;
      }
      try {
        // console.log(`useMessages: Manually triggering markAsRead for user ${userId} in chat ${chatId}`);
        await markAsReadMutation({
          chatId: chatId as Id<"chats">,
          userId: userId as Id<"users">, // CRITICAL: Ensure this userId is correct
        });
      } catch (err: any) {
        console.error("useMessages: Error in triggerMarkMessagesAsRead:", err);
        setError(err);
      }
    },
    [isValidChatId, chatId, userId, markAsReadMutation] // Dependencies
  );


  // --- Return Value ---
  return {
    messages,
    // Loading is true if the query should run AND (either the initial fetch is happening OR user data is still loading)
    isLoading: isValidChatId && (isLoading || isUserLoading),
    error,
    sendMessage,
    sendNudge,
    markMessagesAsRead: triggerMarkMessagesAsRead, // Rename exported function for clarity
  };
}