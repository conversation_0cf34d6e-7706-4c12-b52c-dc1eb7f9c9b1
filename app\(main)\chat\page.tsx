"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { ChatSidebar } from "@/components/chat/chat-sidebar"
import { EmptyChat } from "@/components/chat/empty-chat"

export default function ChatPage() {
  const router = useRouter()

  useEffect(() => {
    // Check if we're on a small screen
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      router.push("/chat/mobile")
    }
  }, [router])

  return (
    <div className="flex h-full">
      <div className="w-80 border-r h-full md:block hidden">
        <ChatSidebar />
      </div>
      <div className="flex-1">
        <EmptyChat />
      </div>
    </div>
  )
}

