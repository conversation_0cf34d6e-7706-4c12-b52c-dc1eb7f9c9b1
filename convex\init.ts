"use client"

import { Id } from "./_generated/dataModel"
import { mutation } from "./_generated/server"

const CHAT_ROOMS = {
  flirt: [
    { name: "<PERSON>C<PERSON>", description: "Connect and flirt" },
    { name: "Bo<PERSON><PERSON>", description: "Casual flirting" },
    { name: "Fantasy Island", description: "Where dreams come true" },
  ],
  teen: [
    { name: "Teen Zone", description: "For teens 13-19" },
    { name: "The Island", description: "Teen hangout spot" },
    { name: "Planet Teen", description: "Out of this world conversations" },
  ],
  "grown-up": [
    { name: "30 Something", description: "For the 30+ crowd" },
    { name: "Single Parents", description: "Connect with other parents" },
    { name: "Lisa's Lounge", description: "Relaxed adult conversations" },
  ],
  kingdom: [
    { name: "The Castle", description: "Royal conversations" },
    { name: "Dark Mountain", description: "Mysterious and exciting" },
    { name: "Greggori's Inn", description: "A cozy place to chat" },
  ],
  topical: [
    { name: "Cars", description: "For car enthusiasts" },
    { name: "Technology", description: "Tech talk" },
    { name: "Fashion", description: "Style and trends" },
  ],
  geographical: [
    { name: "Cape Town", description: "Cape Town locals" },
    { name: "Durban", description: "Durban chat" },
    { name: "Gauteng", description: "Gauteng community" },
  ],
} as const

export const initializeChatRooms = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now()

    // Check if rooms already exist
    const existingRooms = await ctx.db.query("chats").collect()
    if (existingRooms.length > 0) {
      // console.log("Chat rooms already initialized:", existingRooms.length)
      return { status: "Rooms already initialized", count: existingRooms.length }
    }

    // console.log("Initializing chat rooms...")
    let count = 0

    // Create all chat rooms
    for (const [category, rooms] of Object.entries(CHAT_ROOMS)) {
      for (const room of rooms) {
        await ctx.db.insert("chats", {
          type: "chatroom",
          name: room.name,
          description: room.description,
          category: category as any,
          isPublic: true,
          isPrivate: false,
          creatorId: "system" as Id<"users">, // This will need to be updated with a real user ID later
          moolaPerMessage: 2, // 2 Moola per message
          maxParticipants: 100,
          createdAt: now,
          updatedAt: now,
        })
        count++
      }
    }

    // console.log(`Successfully initialized ${count} chat rooms`)
    return { status: "Successfully initialized chat rooms", count }
  },
})

