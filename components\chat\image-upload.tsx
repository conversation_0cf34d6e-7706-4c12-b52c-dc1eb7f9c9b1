"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Image, X, Upload } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { MoolaIcon } from "@/components/moola-icon"
import { useDataMode } from "@/hooks/use-data-mode"

interface ImageUploadProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImageSelected: (imageUrl: string) => void
}

export function ImageUpload({ open, onOpenChange, onImageSelected }: ImageUploadProps) {
  const { toast } = useToast()
  const [imageUrl, setImageUrl] = useState<string>("")
  const [isUploading, setIsUploading] = useState(false)
  const { dataMode, moolaBalance } = useDataMode()
  
  // In a real app, you would implement file upload to a storage service
  // For now, we'll simulate it with a placeholder
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB",
        variant: "destructive",
      })
      return
    }
    
    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file",
        variant: "destructive",
      })
      return
    }
    
    setIsUploading(true)
    
    // Simulate upload delay
    setTimeout(() => {
      // In a real app, you would upload the file to a storage service
      // and get back a URL to the uploaded file
      const reader = new FileReader()
      reader.onload = (event) => {
        if (event.target?.result) {
          setImageUrl(event.target.result as string)
        }
      }
      reader.readAsDataURL(file)
      setIsUploading(false)
    }, 1000)
  }
  
  const handleSubmit = () => {
    if (!imageUrl) {
      toast({
        title: "No image selected",
        description: "Please select an image to send",
        variant: "destructive",
      })
      return
    }
    
    // Check if using Moola and has enough balance
    if (dataMode === "moola" && moolaBalance < 1) {
      toast({
        title: "Insufficient Moola",
        description: "You need at least 1 Moola to send an image",
        variant: "destructive",
      })
      return
    }
    
    onImageSelected(imageUrl)
    setImageUrl("")
    onOpenChange(false)
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Send Image</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {dataMode === "moola" && (
            <div className="flex items-center justify-between px-2 py-1 bg-yellow-100/10 rounded-md">
              <span className="text-sm">Cost:</span>
              <div className="flex items-center">
                <MoolaIcon className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-sm">1.0</span>
              </div>
            </div>
          )}
          
          {!imageUrl ? (
            <div 
              className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={() => document.getElementById("image-upload")?.click()}
            >
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
              />
              <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                <Image className="h-6 w-6 text-muted-foreground" />
              </div>
              <p className="mt-2 text-sm text-muted-foreground">
                Click to select an image, or drag and drop
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Maximum size: 5MB
              </p>
            </div>
          ) : (
            <div className="relative">
              <img 
                src={imageUrl} 
                alt="Preview" 
                className="max-h-[300px] rounded-md mx-auto object-contain"
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8 rounded-full"
                onClick={() => setImageUrl("")}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!imageUrl || isUploading}
            className="gap-2"
          >
            {isUploading ? "Uploading..." : (
              <>
                <Upload className="h-4 w-4" />
                Send Image
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
