import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { Id } from "./_generated/dataModel"

// Get all chats for a user
export const getChats = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    try {
      // Validate userId
      if (!args.userId) {
        return []
      }

      // Get all chats where user is a participant
      const participations = await ctx.db
        .query("chatParticipants")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .collect()

      if (!participations.length) {
        return []
      }

      const chatIds = participations.map((p) => p.chatId)

      // Use a Set to track processed chat IDs to avoid duplicates
      const processedChatIds = new Set()

      const chats = await Promise.all(
        chatIds.map(async (chatId) => {
          try {
            // Skip if we've already processed this chat
            if (processedChatIds.has(chatId.toString())) {
              return null
            }

            processedChatIds.add(chatId.toString())

            const chat = await ctx.db.get(chatId)
            if (!chat) return null

            // Get participants
            const chatParticipants = await ctx.db
              .query("chatParticipants")
              .withIndex("by_chat", (q) => q.eq("chatId", chatId))
              .collect()

            const participants = await Promise.all(
              chatParticipants.map(async (p) => {
                const user = await ctx.db.get(p.userId)
                return user
              }),
            )

            // Get last message
            let lastMessage = null
            if (chat.lastMessageId) {
              const message = await ctx.db.get(chat.lastMessageId)
              if (message) {
                const sender = await ctx.db.get(message.senderId as Id<"users">)
                lastMessage = { ...message, sender }
              }
            }

            // Get unread count for current user
            const participation = chatParticipants.find((p) => p.userId === args.userId)

            return {
              ...chat,
              participants: participants.filter(Boolean),
              lastMessage,
              unreadCount: participation?.unreadCount ?? 0,
            }
          } catch (error) {
            console.error(`Error processing chat ${chatId}:`, error)
            return null
          }
        }),
      )

      // Filter out null values and sort by last message time
      return chats
        .filter(Boolean)
        .sort((a, b) => (b?.lastMessage?.timestamp ?? b?.updatedAt ?? 0) - (a?.lastMessage?.timestamp ?? a?.updatedAt ?? 0))
    } catch (error) {
      console.error("Error in getChats:", error)
      return []
    }
  },
})

// Get a specific chat by ID
export const getChatById = query({
  args: {
    chatId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the chat
      const chat = await ctx.db.get(args.chatId)
      if (!chat) return null

      // Get participants
      const chatParticipants = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
        .collect()

      const participants = await Promise.all(
        chatParticipants.map(async (p) => {
          const user = await ctx.db.get(p.userId)
          return user
        }),
      )

      // Get last message
      let lastMessage = null
      if (chat.lastMessageId) {
        const message = await ctx.db.get(chat.lastMessageId)
        if (message) {
          const sender = await ctx.db.get(message.senderId as Id<"users">)
          lastMessage = { ...message, sender }
        }
      }

      // Get unread count for current user
      const participation = chatParticipants.find((p) => p.userId === args.userId)

      return {
        ...chat,
        participants: participants.filter(Boolean),
        lastMessage,
        unreadCount: participation?.unreadCount ?? 0,
      }
    } catch (error) {
      console.error("Error in getChatById:", error)
      return null
    }
  },
})

// Create a direct chat with another user
export const createDirectChat = mutation({
  args: {
    userId: v.id("users"),
    otherUserId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Log the user IDs to understand their format
      console.log(`Creating direct chat between users: ${args.userId} and ${args.otherUserId}`)

      // Check if direct chat already exists between these two specific users
      // First, check if there's a chat where both users are participants
      const chatParticipations = await ctx.db
        .query("chatParticipants")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .collect()

      const chatIds = chatParticipations.map((p) => p.chatId)

      // For each chat where the first user is a participant, check if the second user is also a participant
      for (const chatId of chatIds) {
        const otherParticipation = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat_and_user", (q) => q.eq("chatId", chatId).eq("userId", args.otherUserId))
          .first()

        if (otherParticipation) {
          // Found a chat where both users are participants
          const chat = await ctx.db.get(chatId)
          if (chat && chat.type === "direct") {
            console.log(`Found existing direct chat: ${chatId} between ${args.userId} and ${args.otherUserId}`)
            return chatId
          }
        }
      }

      // Create new chat with a timestamp to ensure uniqueness
      const now = Date.now()
      const chatId = await ctx.db.insert("chats", {
        type: "direct",
        isPublic: false,
        creatorId: args.userId,
        createdAt: now,
        updatedAt: now,
        background: undefined,
        name: "",
        description: undefined,
        lastMessageId: undefined,
        participants: [args.userId, args.otherUserId],
      })

      console.log(`Created new direct chat: ${chatId} between ${args.userId} and ${args.otherUserId}`)

      // Add participants
      await Promise.all([
        ctx.db.insert("chatParticipants", {
          chatId,
          userId: args.userId,
          unreadCount: 0,
          joinedAt: now,
          role: "member",
        }),
        ctx.db.insert("chatParticipants", {
          chatId,
          userId: args.otherUserId,
          unreadCount: 0,
          joinedAt: now,
          role: "member",
        }),
      ])

      return chatId
    } catch (error) {
      console.error("Error in createDirectChat:", error)
      throw error
    }
  },
})

// Create a group chat
export const createGroupChat = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    creatorId: v.id("users"),
    participantIds: v.array(v.id("users")),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    try {
      // Create new chat
      const now = Date.now()
      const chatId = await ctx.db.insert("chats", {
        type: "group",
        name: args.name,
        description: args.description,
        isPublic: args.isPublic ?? false,
        creatorId: args.creatorId,
        createdAt: now,
        updatedAt: now,
        category: "topical", // Added required category field
        participants: args.participantIds,
      })

      // Add all participants including creator
      const allParticipantIds = [...new Set([...args.participantIds, args.creatorId])]

      await Promise.all(
        allParticipantIds.map((userId) =>
          ctx.db.insert("chatParticipants", {
            chatId,
            userId,
            unreadCount: 0,
            joinedAt: now,
            role: userId === args.creatorId ? "admin" : "member",
          }),
        ),
      )

      return chatId
    } catch (error) {
      console.error("Error in createGroupChat:", error)
      throw error
    }
  },
})

// Get all public chat rooms
export const getPublicChatrooms = query({
  handler: async (ctx) => {
    try {
      const chatrooms = await ctx.db
        .query("chats")
        .filter((q) => q.eq(q.field("type"), "chatroom"))
        .filter((q) => q.eq(q.field("isPublic"), true))
        .collect()

      return Promise.all(
        chatrooms.map(async (room) => {
          const participants = await ctx.db
            .query("chatParticipants")
            .withIndex("by_chat", (q) => q.eq("chatId", room._id))
            .collect()

          const creator = await ctx.db.get(room.creatorId)

          return {
            ...room,
            creator,
            participants: participants.length,
          }
        }),
      )
    } catch (error) {
      console.error("Error getting public chatrooms:", error)
      return []
    }
  },
})

// Get all chatrooms (public and joined private ones)
export const getChatrooms = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Get all public chatrooms
      const chatrooms = await ctx.db
        .query("chats")
        .withIndex("by_type_and_category", (q) => q.eq("type", "chatroom"))
        .filter((q) => q.eq(q.field("isPublic"), true))
        .collect()

      // Get the user's joined chatrooms
      const participations = await ctx.db
        .query("chatParticipants")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .collect()

      const joinedChatroomIds = participations
        .map((p) => p.chatId)

      // Get private chatrooms the user is part of
      const privateChatrooms = await ctx.db
        .query("chats")
        .withIndex("by_type_and_category", (q) => q.eq("type", "chatroom"))
        .filter((q) => q.eq(q.field("isPublic"), false))
        .collect()

      const accessiblePrivateChatrooms = privateChatrooms.filter((chatroom) =>
        joinedChatroomIds.includes(chatroom._id)
      )

      // Combine public and accessible private chatrooms
      const allChatrooms = [...chatrooms, ...accessiblePrivateChatrooms]

      // Add a flag to indicate if the user has joined each chatroom
      return allChatrooms.map((chatroom) => ({
        ...chatroom,
        isJoined: joinedChatroomIds.includes(chatroom._id),
      }))
    } catch (error) {
      console.error("Error in getChatrooms:", error)
      return []
    }
  },
})

// Join a chat room
export const joinChatroom = mutation({
  args: {
    chatroomId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Check if user is already a participant
      const existing = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat_and_user", (q) => q.eq("chatId", args.chatroomId).eq("userId", args.userId))
        .first()

      if (existing) return args.chatroomId

      // Add user as participant
      await ctx.db.insert("chatParticipants", {
        chatId: args.chatroomId,
        userId: args.userId,
        unreadCount: 0,
        joinedAt: Date.now(),
        role: "member",
      })

      return args.chatroomId
    } catch (error) {
      console.error("Error joining chatroom:", error)
      throw error
    }
  },
})

// Get chat rooms by category
export const getChatroomsByCategory = query({
  args: {
    category: v.optional(
      v.union(
        v.literal("flirt"),
        v.literal("teen"),
        v.literal("grown-up"),
        v.literal("kingdom"),
        v.literal("topical"),
        v.literal("geographical"),
      ),
    ),
  },
  handler: async (ctx, args) => {
    let q = ctx.db.query("chats").filter((q) => q.eq(q.field("type"), "chatroom"))

    if (args.category) {
      q = q.filter((q) => q.eq(q.field("category"), args.category))
    }

    const rooms = await q.collect()

    // Get participant count for each room
    const roomsWithStats = await Promise.all(
      rooms.map(async (room) => {
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", room._id))
          .collect()

        return {
          ...room,
          participantCount: participants.length,
        }
      }),
    )

    return roomsWithStats
  },
})

// Add this function to the existing chats.ts file

export const setChatBackground = mutation({
  args: {
    userId: v.id("users"),
    chatId: v.id("chats"),
    backgroundUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const { userId, chatId, backgroundUrl } = args

    // Check if user is a participant in the chat
    const participant = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) => q.eq("chatId", chatId).eq("userId", userId))
      .first()

    if (!participant) {
      throw new Error("User is not a participant in this chat")
    }

    // Check if there's an existing background for this chat and user
    const existingBackground = await ctx.db
      .query("chatBackgrounds")
      .withIndex("by_user_and_chat", (q) => q.eq("userId", userId).eq("chatId", chatId))
      .first()

    const now = Date.now()

    if (existingBackground) {
      // Update existing background
      await ctx.db.patch(existingBackground._id, {
        backgroundUrl,
        isActive: true,
      })
    } else {
      // Create new background
      await ctx.db.insert("chatBackgrounds", {
        userId,
        chatId,
        backgroundUrl,
        isActive: true,
        createdAt: now,
      })
    }

    // If background is "none", update the chat's background to undefined
    // Otherwise, update the chat's background
    if (backgroundUrl === "none") {
      await ctx.db.patch(chatId, {
        background: undefined,
        updatedAt: now,
      })
    } else {
      await ctx.db.patch(chatId, {
        background: backgroundUrl,
        updatedAt: now,
      })
    }

    return { success: true }
  },
})

// Add this function to the chats.ts file
export const updateChatSettings = mutation({
  args: {
    chatId: v.id("chats"),
    userId: v.id("users"),
    settings: v.object({
      moolaPerMessage: v.number(),
      enableTipping: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    const { chatId, userId, settings } = args

    // Check if user is the creator or admin
    const participant = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) => q.eq("chatId", chatId).eq("userId", userId))
      .first()

    if (!participant || participant.role !== "admin") {
      throw new Error("Only the creator or admins can update chat settings")
    }

    // Update chat settings
    await ctx.db.patch(chatId, {
      moolaPerMessage: settings.moolaPerMessage,
      monetizationSettings: {
        enableTipping: settings.enableTipping,
        enableSubscription: false,
        subscriptionAmount: 0,
      },
      updatedAt: Date.now(),
    })

    return { success: true }
  },
})

// Mark messages as read in a chat
export const markMessagesAsRead = mutation({
  args: {
    chatId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Validate that chatId is a proper Convex ID
    if (typeof args.chatId !== 'string' || !args.chatId.match(/^[a-zA-Z0-9]+$/)) {
      throw new Error(`Invalid chat ID format in markMessagesAsRead: ${args.chatId}`);
    }

    const userId = args.userId;


    // Get the chat participation for this user
    const participation = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) => q.eq("chatId", args.chatId).eq("userId", userId))
      .collect();

    if (!participation.length) {
      throw new Error(`User is not a participant in chat ${args.chatId}`);
    }

    // Update the unread count to 0
    await ctx.db.patch(participation[0]._id, {
      ...participation[0],
      unreadCount: 0,
    });

    return true;
  },
})