"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, Search, Users, X } from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { Id } from "@/convex/_generated/dataModel"
import { ScrollArea } from "@/components/ui/scroll-area"
import { PullToRefresh } from "@/components/mobile/pull-to-refresh"

export function MobileChatList() {
  const router = useRouter()
  const { userId } = useAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const [showSearch, setShowSearch] = useState(false)

  // Get chats
  const chats = useQuery(
    api.chats.getChats,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  // Refresh function for pull-to-refresh
  const handleRefresh = async () => {
    // Add a small delay to show the refresh animation
    await new Promise(resolve => setTimeout(resolve, 1000))
    // The query will automatically refetch due to Convex's real-time nature
  }
  
  // Filter chats based on search query
  const filteredChats = chats?.filter((chat) => {
    if (!searchQuery) return true
    
    const otherUser = chat.participants?.find((p) => p._id !== userId)
    const chatName = chat.name || otherUser?.name || ""
    
    return chatName.toLowerCase().includes(searchQuery.toLowerCase())
  })
  
  return (
    <div className="h-full flex flex-col">
      {/* Search Bar */}
      <div className="p-2 border-b bg-background">
        {showSearch ? (
          <div className="flex items-center gap-2">
            <Input
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
              autoFocus
            />
            <Button variant="ghost" size="icon" onClick={() => setShowSearch(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        ) : (
          <Button 
            variant="outline" 
            className="w-full flex justify-start text-muted-foreground"
            onClick={() => setShowSearch(true)}
          >
            <Search className="h-4 w-4 mr-2" />
            Search chats...
          </Button>
        )}
      </div>
      
      {/* Chat List with Pull-to-Refresh */}
      <PullToRefresh onRefresh={handleRefresh} className="flex-1">
        <div className="space-y-1 p-2">
          {filteredChats ? (
            filteredChats.length > 0 ? (
              filteredChats.map((chat) => {
                const otherUser = chat.participants?.find((p) => p._id !== userId)
                const chatName = chat.name || otherUser?.name || "Chat"
                const lastMessage = chat.lastMessage?.content || "No messages yet"
                const unreadCount = chat.unreadCount || 0
                
                return (
                  <Link
                    key={chat._id}
                    href={`/chat/${chat._id}`}
                    className="flex items-center gap-3 p-3 rounded-md hover:bg-muted"
                  >
                    {chat.type === "chatroom" ? (
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <Users className="h-6 w-6 text-primary" />
                      </div>
                    ) : (
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={otherUser?.avatar} alt={chatName} />
                        <AvatarFallback>{chatName.substring(0, 2).toUpperCase()}</AvatarFallback>
                      </Avatar>
                    )}
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="font-medium truncate">{chatName}</p>
                        {chat.lastMessage && (
                          <p className="text-xs text-muted-foreground">
                            {formatDistanceToNow(chat.lastMessage.timestamp, { addSuffix: true })}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-muted-foreground truncate">
                          {chat.lastMessage?.isDeleted 
                            ? "This message was deleted" 
                            : lastMessage}
                        </p>
                        {unreadCount > 0 && (
                          <Badge variant="destructive" className="ml-2">
                            {unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </Link>
                )
              })
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <MessageSquare className="h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No chats yet</h3>
                <p className="text-muted-foreground text-center mt-1">
                  Start a conversation with your contacts
                </p>
              </div>
            )
          ) : (
            // Loading state
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center gap-3 p-3">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                    <Skeleton className="h-3 w-full" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </PullToRefresh>
    </div>
  )
}
