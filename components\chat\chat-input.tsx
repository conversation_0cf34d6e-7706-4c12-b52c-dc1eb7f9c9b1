"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Send, Smile, Mic } from "lucide-react"
import { EmojiPicker } from "@/components/chat/emoji-picker"
import { EmoticonPicker } from "@/components/chat/emoticon-picker"
import { MoolaUsageIndicator } from "@/components/chat/moola-usage-indicator"

interface ChatInputProps {
  onSendMessage: (message: string) => void
  onStartRecording?: () => void
  disabled?: boolean
}

export function ChatInput({ onSendMessage, onStartRecording, disabled = false }: ChatInputProps) {
  const [message, setMessage] = useState("")
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showMXitEmoticons, setShowMXitEmoticons] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Attempting to send message:", message)

    if (message.trim()) {
      try {
        onSendMessage(message.trim())
        setMessage("")
        console.log("Message sent and input cleared")
      } catch (error) {
        console.error("Error in ChatInput when sending message:", error)
      }
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    setMessage((prev) => prev + emoji)
    setShowEmojiPicker(false)
    setShowMXitEmoticons(false)
    // Focus the input after selecting an emoji
    setTimeout(() => {
      inputRef.current?.focus()
    }, 0)
  }

  return (
    <div className="space-y-2">
      <form onSubmit={handleSendMessage} className="flex gap-2 items-center">
        <div className="relative flex-1">
          <Input
            ref={inputRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type a message..."
            className="pr-20 bg-gray-800 border-gray-700 text-white"
            disabled={disabled}
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground"
              onClick={() => {
                setShowEmojiPicker(false)
                setShowMXitEmoticons(!showMXitEmoticons)
              }}
              disabled={disabled}
            >
              <span className="text-lg">:-)</span>
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground"
              onClick={() => {
                setShowMXitEmoticons(false)
                setShowEmojiPicker(!showEmojiPicker)
              }}
              disabled={disabled}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {showEmojiPicker && (
            <div className="absolute bottom-full right-0 mb-2 z-10">
              <EmojiPicker onEmojiSelect={handleEmojiSelect} />
            </div>
          )}

          {showMXitEmoticons && (
            <div className="absolute bottom-full right-0 mb-2 z-10">
              <EmoticonPicker onEmojiSelect={handleEmojiSelect} />
            </div>
          )}
        </div>

        {onStartRecording && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onStartRecording}
            disabled={disabled}
            className="text-muted-foreground"
          >
            <Mic className="h-5 w-5" />
          </Button>
        )}

        <Button
          type="submit"
          size="icon"
          disabled={!message.trim() || disabled}
          className="rounded-full bg-primary h-10 w-10"
        >
          <Send className="h-5 w-5" />
        </Button>
      </form>

      {/* Add the Moola usage indicator */}
      <div className="flex justify-end">
        <MoolaUsageIndicator messageLength={message.length} hasAttachments={false} />
      </div>
    </div>
  )
}

