"use client";

import { useState, useR<PERSON>, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { useChat } from "@/hooks/use-chat";
import { useMessages } from "@/hooks/use-messages";
import { useDataSaving } from "@/hooks/use-data-saving";
import { useDataMode } from "@/hooks/use-data-mode";
import { useToast } from "@/components/ui/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";

// Components
import { ChatHeader } from "./components/chat-header";
import { MessageList } from "./components/message-list";
import { MessageInput } from "./components/message-input";
import { VideoCall } from "@/components/chat/video-call";
import { VoiceCall } from "@/components/chat/voice-call";

// Types
import { Message, MessageWithSender } from "./types";

// Custom hook for responsive design
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};

export function ChatWindow() {
  // Hooks
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { userId, user, isLoading: isUserLoading } = useAuth();
  const { dataSavingMode } = useDataSaving();
  const { dataMode } = useDataMode();
  const isMobile = useIsMobile();
  
  // Get chatId from URL parameters
  const chatId = useMemo(() => {
    return Array.isArray(params?.chatId)
      ? params.chatId[0]
      : typeof params?.chatId === 'string'
        ? params.chatId
        : null;
  }, [params?.chatId]);
  
  // Chat data and messages
  const { chat, isLoading: isChatLoading } = useChat(chatId || null);
  const {
    messages = [],
    sendMessage: sendMessageHook,
    sendNudge,
    isLoading: isMessagesLoading,
  } = useMessages(chatId || null);
  
  // Mutations
  const deleteMessage = useMutation(api.messages.deleteMessage);
  const { recordMessageSent } = useDataMode();
  
  // Local state
  const [newMessage, setNewMessage] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [showGiftDialog, setShowGiftDialog] = useState(false);
  const [showBackgroundPicker, setShowBackgroundPicker] = useState(false);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [showVoiceCall, setShowVoiceCall] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const [newMessageCount, setNewMessageCount] = useState(0);
  const [selectedMessageId, setSelectedMessageId] = useState<Id<"messages"> | null>(null);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Scroll to bottom effect
  useEffect(() => {
    if (isScrolledToBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isScrolledToBottom]);
  
  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 100;
    setIsScrolledToBottom(isAtBottom);
    
    // Reset new message count if scrolled to bottom
    if (isAtBottom) {
      setNewMessageCount(0);
    }
  }, []);
  
  // Handle sending a message
  const handleSendMessage = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !chatId || !userId) {
      toast({
        title: "Cannot send message",
        description: "Please enter a message",
        variant: "destructive",
      });
      return;
    }
    
    const messageToSend = newMessage;
    setNewMessage("");
    setShowEmojiPicker(false);
    
    try {
      await sendMessageHook(messageToSend);
      await recordMessageSent?.(messageToSend.length);
    } catch (error) {
      console.error("Error sending message:", error);
      setNewMessage(messageToSend); // Revert on error
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  }, [newMessage, chatId, userId, sendMessageHook, toast, recordMessageSent]);
  
  // Handle deleting a message
  const handleDeleteMessage = useCallback(async (messageId: Id<"messages">) => {
    if (!userId) return;
    
    try {
      await deleteMessage({ messageId, userId: userId as Id<"users"> });
      toast({
        title: "Message deleted",
        description: "Your message has been deleted",
      });
    } catch (error) {
      console.error("Error deleting message:", error);
      toast({
        title: "Error",
        description: "Failed to delete message",
        variant: "destructive",
      });
    }
  }, [userId, deleteMessage, toast]);
  
  // Derived state
  const isLoading = isChatLoading || isUserLoading || isMessagesLoading;
  const isError = !isLoading && !chat;
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  // Render error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Could not load the chat. Please try again.
          </AlertDescription>
        </Alert>
        <Button className="mt-4" onClick={() => router.back()}>
          Go Back
        </Button>
      </div>
    );
  }

  // Main chat UI
  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      {chat && (
        <ChatHeader
          chat={chat}
          onVideoCall={() => setShowVideoCall(true)}
          onVoiceCall={() => setShowVoiceCall(true)}
          isMobile={isMobile}
        />
      )}

      {/* Messages List */}
      <div 
        className="flex-1 overflow-y-auto p-4" 
        onScroll={handleScroll}
      >
        <MessageList 
          messages={messages}
          userId={userId}
          onDeleteAction={handleDeleteMessage}
          selectedMessageId={selectedMessageId}
          setSelectedMessageId={setSelectedMessageId}
          chatBackground=""
          onScrollToBottom={() => {}}
          isScrolledToBottom={isScrolledToBottom}
          newMessageCount={newMessageCount}
          currentUser={{
            name: user?.name,
            avatar: user?.avatar
          }}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t p-4">
        <MessageInput
          value={newMessage}
          onChange={setNewMessage}
          onSubmit={handleSendMessage}
          showEmojiPicker={showEmojiPicker}
          onToggleEmojiPicker={() => setShowEmojiPicker(!showEmojiPicker)}
          onSendMessage={handleSendMessage}
          
        />
      </div>

      {/* Voice Call Modal */}
      <VoiceCall
        open={showVoiceCall}
        onOpenChange={setShowVoiceCall}
        recipientId={chat?.participants?.find(p => p?._id !== userId)?._id || ""}
        recipientName={chat?.participants?.find(p => p?._id !== userId)?.name || "User"}
        isIncoming={false}
      />
    </div>
  );

}
