"use client"

/**
 * Utility function to handle service worker registration for authentication pages
 * This ensures that the service worker doesn't interfere with Clerk authentication
 */
export function registerAuthServiceWorker() {
  if (typeof window === 'undefined' || !("serviceWorker" in navigator)) {
    return;
  }

  // Wait for the page to load
  window.addEventListener("load", async () => {
    try {
      // Check if we're on iOS Safari
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      
      // Get all registered service workers
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      // Unregister all existing service workers to prevent interference
      for (const registration of registrations) {
        await registration.unregister();
        console.log("Unregistered service worker:", registration.scope);
      }
      
      // For iOS Safari, we don't register any service worker during auth
      if (isSafari && isIOS) {
        console.log("iOS Safari detected, skipping service worker registration during auth");
        return;
      }
      
      // For other browsers, register the minimal auth-safe service worker
      await navigator.serviceWorker.register("/auth-sw.js", {
        scope: "/",
        updateViaCache: "none"
      });
      
      console.log("Auth-safe service worker registered");
    } catch (error) {
      console.error("Auth service worker handling failed:", error);
    }
  });
}
