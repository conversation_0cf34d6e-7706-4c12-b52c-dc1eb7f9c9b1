"use client"

import { useState, useEffect, useRef, useMemo } from "react"
// Removed react-window dependency for simpler implementation
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Users } from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { cn } from "@/lib/utils"

interface ChatItem {
  _id: string
  name?: string
  type: "direct" | "group" | "chatroom"
  participants?: Array<{ _id: string; name: string; avatar?: string }>
  lastMessage?: {
    content: string
    timestamp: number
    isDeleted?: boolean
  }
  unreadCount?: number
}

interface VirtualChatListProps {
  chats: ChatItem[]
  userId: string
  isLoading?: boolean
  onChatSelect?: (chatId: string) => void
}

interface ChatRowProps {
  index: number
  style: React.CSSProperties
  data: {
    chats: ChatItem[]
    userId: string
    onChatSelect?: (chatId: string) => void
  }
}

// Individual chat row component for virtualization
function ChatRow({ index, style, data }: ChatRowProps) {
  const { chats, userId, onChatSelect } = data
  const chat = chats[index]

  if (!chat) {
    return (
      <div style={style} className="p-3">
        <div className="flex items-center gap-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2 flex-1">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-16" />
            </div>
            <Skeleton className="h-3 w-full" />
          </div>
        </div>
      </div>
    )
  }

  const otherUser = chat.participants?.find((p) => p._id !== userId)
  const chatName = chat.name || otherUser?.name || "Chat"
  const lastMessage = chat.lastMessage?.content || "No messages yet"
  const unreadCount = chat.unreadCount || 0

  return (
    <div style={style}>
      <Link
        href={`/chat/${chat._id}`}
        className="flex items-center gap-3 p-3 hover:bg-muted transition-colors"
        onClick={() => onChatSelect?.(chat._id)}
      >
        {chat.type === "chatroom" ? (
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
            <Users className="h-6 w-6 text-primary" />
          </div>
        ) : (
          <Avatar className="h-12 w-12 flex-shrink-0">
            <AvatarImage src={otherUser?.avatar} alt={chatName} />
            <AvatarFallback>{chatName.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
        )}
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <p className="font-medium truncate">{chatName}</p>
            {chat.lastMessage && (
              <p className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                {formatDistanceToNow(chat.lastMessage.timestamp, { addSuffix: true })}
              </p>
            )}
          </div>
          <div className="flex items-center justify-between mt-1">
            <p className="text-sm text-muted-foreground truncate">
              {chat.lastMessage?.isDeleted 
                ? "This message was deleted" 
                : lastMessage}
            </p>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2 flex-shrink-0">
                {unreadCount > 99 ? "99+" : unreadCount}
              </Badge>
            )}
          </div>
        </div>
      </Link>
    </div>
  )
}

export function VirtualChatList({ 
  chats, 
  userId, 
  isLoading = false, 
  onChatSelect 
}: VirtualChatListProps) {
  const [containerHeight, setContainerHeight] = useState(400)
  const containerRef = useRef<HTMLDivElement>(null)

  // Update container height on resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        const availableHeight = window.innerHeight - rect.top - 100 // Leave some space
        setContainerHeight(Math.max(200, availableHeight))
      }
    }

    updateHeight()
    window.addEventListener('resize', updateHeight)
    return () => window.removeEventListener('resize', updateHeight)
  }, [])

  // Memoize the data object to prevent unnecessary re-renders
  const listData = useMemo(() => ({
    chats,
    userId,
    onChatSelect
  }), [chats, userId, onChatSelect])

  if (isLoading) {
    return (
      <div ref={containerRef} className="space-y-1">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-3">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2 flex-1">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16" />
              </div>
              <Skeleton className="h-3 w-full" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (chats.length === 0) {
    return (
      <div ref={containerRef} className="flex flex-col items-center justify-center py-12">
        <Users className="h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-medium">No chats yet</h3>
        <p className="text-muted-foreground text-center mt-1">
          Start a conversation with your contacts
        </p>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="w-full">
      <List
        height={containerHeight}
        itemCount={chats.length}
        itemSize={80} // Height of each chat item
        itemData={listData}
        overscanCount={5} // Render 5 extra items outside viewport for smooth scrolling
        className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
      >
        {ChatRow}
      </List>
    </div>
  )
}
