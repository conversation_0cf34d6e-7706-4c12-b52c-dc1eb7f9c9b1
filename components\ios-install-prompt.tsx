"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"

export function IOSInstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if it's iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
    setIsIOS(iOS)

    // Check if already installed
    const checkIfInstalled = () => {
      const standalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone;
      setIsStandalone(standalone);
      return standalone;
    };

    // Initial check
    const isAppInstalled = checkIfInstalled();

    // Show prompt after 3 seconds if it's iOS and not already installed
    if (iOS && !isAppInstalled) {
      const timer = setTimeout(() => {
        // Check if user has already dismissed the prompt in the last 24 hours
        const dismissed = localStorage.getItem('ios-install-prompt-dismissed');
        const dismissedTime = dismissed ? parseInt(dismissed, 10) : 0;
        const now = Date.now();
        const oneDayMs = 24 * 60 * 60 * 1000;

        if (!dismissed || (now - dismissedTime > oneDayMs)) {
          setShowPrompt(true);
        }
      }, 3000);

      // Also check when the page becomes visible again
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          checkIfInstalled();
        }
      });

      return () => {
        clearTimeout(timer);
        document.removeEventListener('visibilitychange', checkIfInstalled);
      };
    }
  }, [])

  const dismissPrompt = () => {
    setShowPrompt(false)
    // Remember that user dismissed the prompt with timestamp
    localStorage.setItem('ios-install-prompt-dismissed', Date.now().toString())
  }

  if (!showPrompt || !isIOS || isStandalone) {
    return null
  }

  return (
    <div className="fixed inset-x-0 bottom-0 z-50 p-4 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 shadow-lg">
      <div className="relative max-w-md mx-auto">
        <button
          onClick={dismissPrompt}
          className="absolute top-0 right-0 p-1"
          aria-label="Close"
        >
          <X className="h-4 w-4 text-gray-500" />
        </button>

        <h3 className="font-bold text-lg mb-2">Install XITchat App</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          For the best experience, add XITchat to your home screen:
        </p>

        <ol className="text-sm text-gray-600 dark:text-gray-400 list-decimal pl-5 mb-3 space-y-1">
          <li>Tap the share button <span className="inline-block w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-md text-center">↑</span></li>
          <li>Scroll down and tap <strong>Add to Home Screen</strong></li>
          <li>Tap <strong>Add</strong> in the top right</li>
        </ol>

        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={dismissPrompt}
          >
            Maybe Later
          </Button>
        </div>
      </div>
    </div>
  )
}
