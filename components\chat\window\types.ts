import { Id } from "@/convex/_generated/dataModel";

export interface Message {
  _id: Id<"messages">;
  content: string;
  senderId: Id<"users"> | null; // Can be null for system messages
  senderName?: string;
  senderAvatar?: string;
  timestamp: number;
  isNudge?: boolean;
  isVoiceNote?: boolean;
  voiceNoteUrl?: string;
  voiceNoteDuration?: number;
  isMultimix?: boolean;
  multimixStyle?: string;
  dataMode?: "mobile" | "desktop";
}

export interface Chat {
  avatar: string | undefined;
  _id: Id<"chats">;
  name?: string;
  participants: Array<{
    _id: Id<"users">;
    _creationTime?: number;
    name?: string;
    avatar?: string;
    status?: "online" | "offline" | "away";
    email?: string;
    dataSavingMode?: boolean;
    lastSeen?: number;
    // Add any other fields that might be present in your data
  } | null>;
  type: "direct" | "group" | "chatroom";
  lastMessage?: {
    _id: Id<"messages">;
    content: string;
    timestamp: number;
    sender: {
      _id: Id<"users">;
      name?: string;
      avatar?: string;
    } | null;
    isNudge?: boolean;
    isVoiceNote?: boolean;
    voiceNoteUrl?: string;
    voiceNoteDuration?: number;
    isMultimix?: boolean;
    multimixStyle?: string;
    dataMode?: "mobile" | "desktop";
    reactions?: Array<{
      emoji: string;
      userId: Id<"users">;
      userName: string;
    }>;
  } | null;
  unreadCount?: number;
  background?: string;
  createdAt: number;
  updatedAt: number;
}

export interface ChatWindowProps {
  chatId: string;
  userId: Id<"users"> | null;
  user: {
    _id: Id<"users">;
    name?: string;
    avatar?: string;
  } | null;
  isMobile: boolean;
  dataSavingMode?: boolean;
  dataMode?: "mobile" | "desktop";
}

// Extend the base Message type with sender info
export interface MessageWithSender extends Omit<Message, 'senderId'> {
  senderId: Id<"users"> | null;
  senderName?: string;
  senderAvatar?: string;
}

export interface MessageListProps {
  messages: Array<Message | MessageWithSender>;
  userId: Id<"users"> | null;
  onDeleteMessage: (messageId: Id<"messages">) => Promise<void>;
  selectedMessageId: Id<"messages"> | null;
  setSelectedMessageId: (id: Id<"messages"> | null) => void;
  chatBackground: string;
  onScrollToBottom: () => void;
  isScrolledToBottom: boolean;
  newMessageCount: number;
}

export interface MessageItemProps {
  message: Message;
  isCurrentUser: boolean;
  onDelete: (messageId: Id<"messages">) => Promise<void>;
  isSelected: boolean;
  onSelect: (id: Id<"messages"> | null) => void;
}

export interface MessageInputProps {
  message: string;
  onMessageChange: (value: string) => void;
  onSendMessage: (e: React.FormEvent) => Promise<void>;
  onEmojiSelect: (emoji: string) => void;
  onVoiceRecorded: (audioBlob: Blob) => Promise<void>;
  onSendNudge: () => void;
  onImageSelected: (imageUrl: string) => Promise<void>;
  showEmojiPicker: boolean;
  setShowEmojiPicker: (show: boolean) => void;
  showVoiceRecorder: boolean;
  setShowVoiceRecorder: (show: boolean) => void;
  showMultiMixEditor: boolean;
  setShowMultiMixEditor: (show: boolean) => void;
  showImageUpload: boolean;
  setShowImageUpload: (show: boolean) => void;
  showGiftDialog: boolean;
  setShowGiftDialog: (show: boolean) => void;
  showBackgroundPicker: boolean;
  setShowBackgroundPicker: (show: boolean) => void;
  isMobile: boolean;
  dataSavingMode?: boolean;
  dataMode?: "mobile" | "desktop";
}

export interface ChatHeaderProps {
  chat: Chat | null;
  onVideoCall: () => void;
  onVoiceCall: () => void;
  onBack: () => void;
  isMobile: boolean;
}

export interface ScrollToBottomButtonProps {
  visible: boolean;
  count: number;
  onClick: () => void;
}
