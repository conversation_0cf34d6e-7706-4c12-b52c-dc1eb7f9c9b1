"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import type { Id } from "@/convex/_generated/dataModel"

/**
 * A custom React hook for managing chat data.
 * It handles resolving a chat ID from a user ID (for direct chats)
 * or fetching an existing chat by its ID.
 *
 * @param {string | null} chatId - Can be either:
 *   1. null: No chat ID provided.
 *   2. A user ID prefixed with "users_": e.g., "users_jh75b989b4tv6qfc1tsdyb8rz57cytee"
 *      (This will trigger creation/fetching of a direct chat)
 *   3. A standard Convex chat ID
 */
export function useChat(chatId: string | null) {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [error, setError] = useState<Error | null>(null)
  const [resolvedChatId, setResolvedChatId] = useState<string | null>(null)
  const [isResolving, setIsResolving] = useState(false)

  // Create direct chat mutation
  const createDirectChat = useMutation(api.chats.createDirectChat)
  
  // Check if the provided ID is a user ID (starts with 'users_')
  const isUserId = chatId?.startsWith('users_')
  
  // Only try to get chat if we have a valid chatId that's not a user ID
  const shouldFetchChat = chatId && userId && !isUserId
  
  // Get chat data if we have a valid chat ID
  const chatData = useQuery(
    api.chats.getChatById, 
    shouldFetchChat ? { 
      chatId: chatId as Id<"chats">, 
      userId: userId as Id<"users"> 
    } : "skip"
  )

  // Handle user ID resolution to chat ID
  useEffect(() => {
    if (!chatId || !userId || !isUserId) {
      setIsResolving(false)
      return
    }

    const resolveChatId = async () => {
      setIsResolving(true)
      setError(null)
      
      try {
        const otherUserId = chatId as Id<"users">
        const chatIdResolved = await createDirectChat({
          userId: userId as Id<"users">,
          otherUserId
        })
        setResolvedChatId(chatIdResolved)
      } catch (error) {
        console.error("Error resolving chat ID:", error)
        setError(error instanceof Error ? error : new Error("Failed to resolve chat ID"))
      } finally {
        setIsResolving(false)
      }
    }

    resolveChatId()
  }, [chatId, userId, isUserId, createDirectChat])

  // Get the chat data using the resolved chat ID (for user IDs)
  const resolvedChatData = useQuery(
    api.chats.getChatById, 
    (isUserId && resolvedChatId && userId) ? { 
      chatId: resolvedChatId as Id<"chats">, 
      userId: userId as Id<"users"> 
    } : "skip"
  )

  // Determine loading state
  const isLoading = isAuthLoading || 
                  (isUserId ? isResolving || resolvedChatData === undefined : chatData === undefined)

  // Set error if chat wasn't found
  useEffect(() => {
    if (!isLoading) {
      const currentChat = isUserId ? resolvedChatData : chatData
      if (currentChat === null) {
        setError(new Error("Chat not found"))
      }
    }
  }, [isLoading, isUserId, resolvedChatData, chatData])

  return {
    chat: isUserId ? resolvedChatData : chatData,
    isLoading,
    error,
    chatId: isUserId ? resolvedChatId : chatId
  }
}
