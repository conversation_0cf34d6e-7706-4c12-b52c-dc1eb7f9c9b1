"use client"

import { useState } from "react"
import { formatDistanceToNow } from "date-fns"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Reply, MoreVertical, Heart, ThumbsUp, Smile } from "lucide-react"
import { MoolaIcon } from "@/components/moola-icon"
import type { Message, User } from "@/lib/types"

interface MobileMessageBubbleProps {
  message: Message
  sender: User
  isOwn: boolean
  showAvatar?: boolean
  onReply?: (message: Message) => void
  onReact?: (messageId: string, emoji: string) => void
  onDelete?: (messageId: string) => void
}

export function MobileMessageBubble({
  message,
  sender,
  isOwn,
  showAvatar = true,
  onReply,
  onReact,
  onDelete
}: MobileMessageBubbleProps) {
  const [showActions, setShowActions] = useState(false)
  const [showReactions, setShowReactions] = useState(false)

  const quickReactions = ["❤️", "👍", "😂", "😮", "😢", "😡"]

  const handleLongPress = () => {
    setShowActions(true)
    // Add haptic feedback if available
    if (navigator.vibrate) {
      navigator.vibrate(50)
    }
  }

  const handleReaction = (emoji: string) => {
    onReact?.(message._id, emoji)
    setShowReactions(false)
  }

  return (
    <div className={cn(
      "flex gap-2 mb-3 px-3",
      isOwn ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar - only show for other users and when specified */}
      {!isOwn && showAvatar && (
        <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
          <AvatarImage src={sender.avatar} alt={sender.name} />
          <AvatarFallback className="text-xs">
            {sender.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}

      {/* Message Container */}
      <div className={cn(
        "flex flex-col max-w-[85%]",
        isOwn ? "items-end" : "items-start"
      )}>
        {/* Sender name for group chats */}
        {!isOwn && showAvatar && (
          <span className="text-xs text-muted-foreground mb-1 px-2">
            {sender.name}
          </span>
        )}

        {/* Message Bubble */}
        <div
          className={cn(
            "relative rounded-2xl px-3 py-2 max-w-full break-words",
            "shadow-sm border",
            isOwn 
              ? "bg-primary text-primary-foreground rounded-br-md" 
              : "bg-background border-border rounded-bl-md",
            message.isNudge && "animate-pulse border-yellow-400 bg-yellow-50",
            message.isMultimix && "bg-gradient-to-r from-purple-100 to-pink-100"
          )}
          onTouchStart={() => {
            // Start long press timer
            const timer = setTimeout(handleLongPress, 500)
            const cleanup = () => clearTimeout(timer)
            
            const handleTouchEnd = () => {
              cleanup()
              document.removeEventListener('touchend', handleTouchEnd)
              document.removeEventListener('touchcancel', handleTouchEnd)
            }
            
            document.addEventListener('touchend', handleTouchEnd)
            document.addEventListener('touchcancel', handleTouchEnd)
          }}
        >
          {/* Special message types */}
          {message.isNudge && (
            <div className="flex items-center gap-2 text-yellow-600 font-medium text-sm">
              <span className="animate-bounce">👋</span>
              Nudge!
            </div>
          )}

          {message.isMultimix && (
            <div className="text-xs text-purple-600 font-medium mb-1">
              ✨ MultiMix
            </div>
          )}

          {/* Message content */}
          {!message.isNudge && (
            <div className={cn(
              "text-sm leading-relaxed",
              message.isMultimix && `font-${message.multimixStyle || 'normal'}`
            )}>
              {message.content}
            </div>
          )}

          {/* Voice note indicator */}
          {message.isVoiceNote && (
            <div className="flex items-center gap-2 mt-1">
              <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
              <span className="text-xs opacity-75">
                {message.voiceNoteDuration || "0:00"}
              </span>
            </div>
          )}

          {/* Moola cost indicator */}
          {message.moolaCost && message.moolaCost > 0 && (
            <div className="flex items-center gap-1 mt-1 opacity-75">
              <MoolaIcon className="h-3 w-3" />
              <span className="text-xs">{message.moolaCost}</span>
            </div>
          )}

          {/* Message reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {message.reactions.map((reaction, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs px-1 py-0 h-5"
                >
                  {reaction.emoji}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Message metadata */}
        <div className={cn(
          "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
          isOwn ? "flex-row-reverse" : "flex-row"
        )}>
          <span>
            {formatDistanceToNow(message.timestamp, { addSuffix: true })}
          </span>
          
          {/* Read status for own messages */}
          {isOwn && (
            <span className={cn(
              "text-xs",
              message.isRead ? "text-blue-500" : "text-gray-400"
            )}>
              {message.isRead ? "✓✓" : "✓"}
            </span>
          )}
        </div>

        {/* Quick Actions */}
        {showActions && (
          <div className="flex items-center gap-2 mt-2 p-2 bg-background border rounded-lg shadow-lg">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setShowReactions(true)
                setShowActions(false)
              }}
            >
              <Smile className="h-4 w-4" />
            </Button>
            
            {onReply && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  onReply(message)
                  setShowActions(false)
                }}
              >
                <Reply className="h-4 w-4" />
              </Button>
            )}

            {isOwn && onDelete && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  onDelete(message._id)
                  setShowActions(false)
                }}
              >
                Delete
              </Button>
            )}

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowActions(false)}
            >
              ✕
            </Button>
          </div>
        )}

        {/* Quick Reactions */}
        {showReactions && (
          <div className="flex items-center gap-1 mt-2 p-2 bg-background border rounded-lg shadow-lg">
            {quickReactions.map((emoji) => (
              <Button
                key={emoji}
                size="sm"
                variant="ghost"
                className="text-lg p-1 h-8 w-8"
                onClick={() => handleReaction(emoji)}
              >
                {emoji}
              </Button>
            ))}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowReactions(false)}
            >
              ✕
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
