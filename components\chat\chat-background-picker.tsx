"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Palette, Check } from "lucide-react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { Id } from "@/convex/_generated/dataModel"

// Predefined backgrounds
const BACKGROUNDS = {
  colors: [
    { id: "default", value: "none", label: "Default" },
    { id: "blue", value: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)", label: "Blue" },
    { id: "green", value: "linear-gradient(135deg, #10b981 0%, #059669 100%)", label: "Green" },
    { id: "purple", value: "linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)", label: "Purple" },
    { id: "pink", value: "linear-gradient(135deg, #ec4899 0%, #db2777 100%)", label: "Pink" },
    { id: "orange", value: "linear-gradient(135deg, #f97316 0%, #ea580c 100%)", label: "Orange" },
  ],
  patterns: [
    { id: "dots", value: "url('/patterns/dots.png')", label: "Dots" },
    { id: "waves", value: "url('/patterns/waves.png')", label: "Waves" },
    { id: "grid", value: "url('/patterns/grid.png')", label: "Grid" },
    { id: "confetti", value: "url('/patterns/confetti.png')", label: "Confetti" },
    { id: "hearts", value: "url('/patterns/hearts.png')", label: "Hearts" },
    { id: "stars", value: "url('/patterns/stars.png')", label: "Stars" },
  ],
  images: [
    { id: "beach", value: "url('/backgrounds/beach.jpg')", label: "Beach" },
    { id: "mountains", value: "url('/backgrounds/mountains.jpg')", label: "Mountains" },
    { id: "city", value: "url('/backgrounds/city.jpg')", label: "City" },
    { id: "forest", value: "url('/backgrounds/forest.jpg')", label: "Forest" },
    { id: "space", value: "url('/backgrounds/space.jpg')", label: "Space" },
    { id: "abstract", value: "url('/backgrounds/abstract.jpg')", label: "Abstract" },
  ],
}

interface ChatBackgroundPickerProps {
  chatId: string
  currentBackground?: string
  onBackgroundChange: (background: string) => void
}

export function ChatBackgroundPicker({
  chatId,
  currentBackground = "none",
  onBackgroundChange,
}: ChatBackgroundPickerProps) {
  const { toast } = useToast()
  const { userId } = useAuth()
  const [selectedBackground, setSelectedBackground] = useState(currentBackground)
  const [isOpen, setIsOpen] = useState(false)

  const setChatBackground = useMutation(api.chats.setChatBackground)

  const handleSelectBackground = (background: string) => {
    setSelectedBackground(background)
  }

  const handleSaveBackground = async () => {
    if (!userId || !chatId) return

    try {
      await setChatBackground({
        userId: userId as Id<"users">,
        chatId: chatId as Id<"chats">,
        backgroundUrl: selectedBackground,
      })

      onBackgroundChange(selectedBackground)
      toast({
        title: "Background Updated",
        description: "Chat background has been updated successfully",
      })

      setIsOpen(false)
    } catch (error) {
      console.error("Error setting chat background:", error)
      toast({
        title: "Error",
        description: "Failed to update chat background",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon">
          <Palette className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Change Chat Background</DialogTitle>
          <DialogDescription>Choose a background for this chat.</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="colors">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="colors">Colors</TabsTrigger>
            <TabsTrigger value="patterns">Patterns</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
          </TabsList>

          <TabsContent value="colors" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              {BACKGROUNDS.colors.map((bg) => (
                <div
                  key={bg.id}
                  className="relative h-20 rounded-md cursor-pointer overflow-hidden"
                  style={{ background: bg.value === "none" ? "#f9fafb" : bg.value }}
                  onClick={() => handleSelectBackground(bg.value)}
                >
                  {selectedBackground === bg.value && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                      <Check className="h-6 w-6 text-white" />
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/40 p-1">
                    <p className="text-xs text-white text-center">{bg.label}</p>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="patterns" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              {BACKGROUNDS.patterns.map((bg) => (
                <div
                  key={bg.id}
                  className="relative h-20 rounded-md cursor-pointer overflow-hidden"
                  style={{ background: bg.value }}
                  onClick={() => handleSelectBackground(bg.value)}
                >
                  {selectedBackground === bg.value && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                      <Check className="h-6 w-6 text-white" />
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/40 p-1">
                    <p className="text-xs text-white text-center">{bg.label}</p>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="images" className="mt-4">
            <div className="grid grid-cols-3 gap-4">
              {BACKGROUNDS.images.map((bg) => (
                <div
                  key={bg.id}
                  className="relative h-20 rounded-md cursor-pointer overflow-hidden"
                  style={{
                    background: bg.value,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                  }}
                  onClick={() => handleSelectBackground(bg.value)}
                >
                  {selectedBackground === bg.value && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                      <Check className="h-6 w-6 text-white" />
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 bg-black/40 p-1">
                    <p className="text-xs text-white text-center">{bg.label}</p>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSaveBackground}>Apply Background</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

