import { v } from "convex/values"
import { mutation, query } from "./_generated/server"

// Get all emoticons, optionally filtered by category
export const getEmoticons = query({
  args: {
    category: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let q = ctx.db.query("emoticons")

    if (args.category) {
      q = q.withIndex("by_category", (q) => q.eq("category", args.category))
    }

    return await q.collect()
  },
})

// Get emoticons that a user has purchased
export const getUserEmoticons = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const userEmoticons = await ctx.db
      .query("userEmoticons")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect()

    const emoticonIds = userEmoticons.map((ue) => ue.emoticonId)

    if (emoticonIds.length === 0) {
      return []
    }

    // Get the actual emoticon data
    const emoticons = await Promise.all(emoticonIds.map((id) => ctx.db.get(id)))

    return emoticons.filter(Boolean)
  },
})

// Purchase an emoticon with Moola
export const purchaseEmoticon = mutation({
  args: {
    userId: v.id("users"),
    emoticonId: v.id("emoticons"),
  },
  handler: async (ctx, args) => {
    const { userId, emoticonId } = args

    // Check if user already owns this emoticon
    const existing = await ctx.db
      .query("userEmoticons")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("emoticonId"), emoticonId))
      .first()

    if (existing) {
      return { success: true, alreadyOwned: true }
    }

    // Get the emoticon to check its cost
    const emoticon = await ctx.db.get(emoticonId)
    if (!emoticon) {
      throw new Error("Emoticon not found")
    }

    // Get the user to check their Moola balance
    const user = await ctx.db.get(userId)
    if (!user) {
      throw new Error("User not found")
    }

    // Check if the emoticon is premium and costs Moola
    if (emoticon.isPremium && emoticon.moolaCost) {
      if (user.moola < emoticon.moolaCost) {
        throw new Error("Insufficient Moola balance")
      }

      // Deduct Moola from user
      await ctx.db.patch(userId, {
        moola: user.moola - emoticon.moolaCost,
      })

      // Record the transaction
      await ctx.db.insert("moolaTransactions", {
        userId,
        amount: -emoticon.moolaCost,
        type: "purchase",
        description: `Purchased emoticon: ${emoticon.name}`,
        timestamp: Date.now(),
      })
    }

    // Add the emoticon to the user's collection
    await ctx.db.insert("userEmoticons", {
      userId,
      emoticonId,
      purchasedAt: Date.now(),
    })

    return { success: true, alreadyOwned: false }
  },
})

// Initialize default emoticons (run this once to populate the database)
export const initializeDefaultEmoticons = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now()

    // Check if emoticons already exist
    const existingCount = await ctx.db
      .query("emoticons")
      .collect()
      .then((e) => e.length)
    if (existingCount > 0) {
      return { success: true, message: "Emoticons already initialized" }
    }

    // Define the classic MXit emoticons
    const defaultEmoticons = [
      // Basic emoticons (free)
      { name: "Smile", code: ":-)", imageUrl: "/emoticons/smile.png", category: "basic", isPremium: false },
      { name: "Sad", code: ":-(", imageUrl: "/emoticons/sad.png", category: "basic", isPremium: false },
      { name: "Wink", code: ";-)", imageUrl: "/emoticons/wink.png", category: "basic", isPremium: false },
      { name: "Laugh", code: ":-D", imageUrl: "/emoticons/laugh.png", category: "basic", isPremium: false },
      { name: "Tongue", code: ":-P", imageUrl: "/emoticons/tongue.png", category: "basic", isPremium: false },
      { name: "Kiss", code: ":-*", imageUrl: "/emoticons/kiss.png", category: "basic", isPremium: false },
      { name: "Surprised", code: ":-O", imageUrl: "/emoticons/surprised.png", category: "basic", isPremium: false },
      { name: "Neutral", code: ":-|", imageUrl: "/emoticons/neutral.png", category: "basic", isPremium: false },
      { name: "Confused", code: ":-S", imageUrl: "/emoticons/confused.png", category: "basic", isPremium: false },
      { name: "Embarrassed", code: ":-$", imageUrl: "/emoticons/embarrassed.png", category: "basic", isPremium: false },
      { name: "Cool", code: "B-)", imageUrl: "/emoticons/cool.png", category: "basic", isPremium: false },
      { name: "Angry", code: ">:(", imageUrl: "/emoticons/angry.png", category: "basic", isPremium: false },
      { name: "Crying", code: ":'(", imageUrl: "/emoticons/crying.png", category: "basic", isPremium: false },
      { name: "Sleepy", code: "|-)", imageUrl: "/emoticons/sleepy.png", category: "basic", isPremium: false },
      { name: "Sick", code: ":-&", imageUrl: "/emoticons/sick.png", category: "basic", isPremium: false },
      { name: "Yell", code: ":-@", imageUrl: "/emoticons/yell.png", category: "basic", isPremium: false },
      { name: "Nerd", code: "8-)", imageUrl: "/emoticons/nerd.png", category: "basic", isPremium: false },
      { name: "Heart", code: "<3", imageUrl: "/emoticons/heart.png", category: "basic", isPremium: false },
      { name: "Broken Heart", code: "</3", imageUrl: "/emoticons/broken-heart.png", category: "basic", isPremium: false },
      { name: "Thumbs Up", code: "(y)", imageUrl: "/emoticons/thumbs-up.png", category: "basic", isPremium: false },
      { name: "Thumbs Down", code: "(n)", imageUrl: "/emoticons/thumbs-down.png", category: "basic", isPremium: false },

      // Premium emoticons
      {
        name: "Angel",
        code: "O:-)",
        imageUrl: "/emoticons/angel.png",
        category: "premium",
        isPremium: true,
        moolaCost: 50,
      },
      {
        name: "Devil",
        code: ">:-)",
        imageUrl: "/emoticons/devil.png",
        category: "premium",
        isPremium: true,
        moolaCost: 50,
      },
      {
        name: "Cool",
        code: "8-)",
        imageUrl: "/emoticons/cool.png",
        category: "premium",
        isPremium: true,
        moolaCost: 50,
      },
      {
        name: "Heart",
        code: "<3",
        imageUrl: "/emoticons/heart.png",
        category: "premium",
        isPremium: true,
        moolaCost: 75,
      },
      {
        name: "Broken Heart",
        code: "</3",
        imageUrl: "/emoticons/broken-heart.png",
        category: "premium",
        isPremium: true,
        moolaCost: 75,
      },

      // XITchat exclusive premium emoticons
      {
        name: "Party",
        code: "<:o)",
        imageUrl: "/emoticons/party.png",
        category: "premium",
        isPremium: true,
        moolaCost: 100,
      },
      {
        name: "Ninja",
        code: ":ninja:",
        imageUrl: "/emoticons/ninja.png",
        category: "premium",
        isPremium: true,
        moolaCost: 150,
      },
      {
        name: "Robot",
        code: ":robot:",
        imageUrl: "/emoticons/robot.png",
        category: "premium",
        isPremium: true,
        moolaCost: 150,
      },
      {
        name: "Zombie",
        code: ":zombie:",
        imageUrl: "/emoticons/zombie.png",
        category: "premium",
        isPremium: true,
        moolaCost: 200,
      },
      {
        name: "South Africa",
        code: ":za:",
        imageUrl: "/emoticons/south-africa.png",
        category: "premium",
        isPremium: true,
        moolaCost: 250,
      },

      // Special MXit emoticons
      {
        name: "MXit",
        code: ":-M",
        imageUrl: "/emoticons/mxit.png",
        category: "special",
        isPremium: true,
        moolaCost: 100,
      },
      {
        name: "Moola",
        code: ":-$$$",
        imageUrl: "/emoticons/moola.png",
        category: "special",
        isPremium: true,
        moolaCost: 100,
      },
    ]

    // Insert all emoticons
    for (const emoticon of defaultEmoticons) {
      await ctx.db.insert("emoticons", {
        ...emoticon,
        createdAt: now,
      })
    }

    return { success: true, message: "Default emoticons initialized", count: defaultEmoticons.length }
  },
})

