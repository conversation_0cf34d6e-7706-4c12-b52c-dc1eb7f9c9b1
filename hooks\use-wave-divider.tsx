"use client"

import { useState, useEffect } from "react"

export function useWaveDivider() {
  const [showWaveDivider, setShowWaveDivider] = useState(true)

  useEffect(() => {
    // Load preference from localStorage
    const savedPreference = localStorage.getItem("xitchat-wave-divider")
    if (savedPreference !== null) {
      setShowWaveDivider(savedPreference === "true")
    }
  }, [])

  const toggleWaveDivider = () => {
    const newValue = !showWaveDivider
    setShowWaveDivider(newValue)
    localStorage.setItem("xitchat-wave-divider", String(newValue))
  }

  return {
    showWaveDivider,
    toggleWaveDivider,
  }
}

