"use client"
import { cn } from "@/lib/utils"

interface WaveDividerProps {
  className?: string
  isVisible?: boolean
  onToggle?: () => void
}

export function WaveDivider({ className, isVisible = true, onToggle }: WaveDividerProps) {
  return (
    <div
      className={cn(
        "relative w-full transition-opacity duration-300",
        isVisible ? "opacity-100" : "opacity-0",
        className,
      )}
    >
      {/* Toggle button */}
      {onToggle && (
        <button
          onClick={onToggle}
          className="absolute top-1/2 right-4 -translate-y-1/2 z-10 w-6 h-6 rounded-full bg-white dark:bg-gray-700 shadow-md flex items-center justify-center"
          aria-label={isVisible ? "Hide wave divider" : "Show wave divider"}
        >
          <span className="text-xs text-gray-600 dark:text-gray-300">{isVisible ? "−" : "+"}</span>
        </button>
      )}

      {/* Wave SVG */}
      <svg
        viewBox="0 0 1440 120"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-12"
        preserveAspectRatio="none"
      >
        <path
          d="M0 0L48 8.875C96 17.75 192 35.5 288 44.375C384 53.25 480 53.25 576 44.375C672 35.5 768 17.75 864 26.625C960 35.5 1056 71 1152 79.875C1248 88.75 1344 71 1392 62.125L1440 53.25V120H1392C1344 120 1248 120 1152 120C1056 120 960 120 864 120C768 120 672 120 576 120C480 120 384 120 288 120C192 120 96 120 48 120H0V0Z"
          fill="white"
        />
      </svg>
    </div>
  )
}

