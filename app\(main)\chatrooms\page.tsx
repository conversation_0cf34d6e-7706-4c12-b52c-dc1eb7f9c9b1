"use client"

import { useEffect, useState } from "react"
import { useMutation, useQuery } from "convex/react"; // useAction is not used, removed it
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { useConvexUser } from "@/hooks/use-convex-auth"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useQueryClient } from "@tanstack/react-query";
import { ChatroomCategories } from "@/components/chatrooms/chatroom-categories"
import CreateChatroomDialog from "@/components/chatrooms/create-chatroom-dialog"
import { SubmitNewsDialog } from "@/components/news/submit-news-dailog"
import { Button } from "@/components/ui/button"
import { Trash2, Megaphone } from "lucide-react"
import { NewsItem } from "@/components/news/news-item"
import { CHAT_CATEGORIES, validateChatCategory } from "@/convex/lib/chatUtils" // Static import

// Define a type for your local news, consistent with Convex schema
interface LocalNewsItem {
  _id: Id<"localNews">;
  _creationTime: number;
  title: string;
  description: string;
  url?: string;
  userId: Id<"users">;
  userName: string;
  userProfileImage?: string;
}

// Define a more specific type for your chatrooms, extending Convex's document properties
interface Chatroom {
  _id: Id<"chats">;
  _creationTime: number;
  name: string;
  description?: string;
  isPublic: boolean;
  location?: string;
  tags?: string[];
  creatorId: Id<"users">;
  category?: (typeof CHAT_CATEGORIES)[number] | 'local-news';
  memberCount: number;
  creator: { name: string; avatar: string } | null;
  participants: Id<"users">[];
  createdAt?: number;
  updatedAt?: number;
  isPlaceholder?: boolean;
  // Optional fields that might be present in the data
  background?: string;
  type?: string;
}

// Extract emoji from chatroom name if present
const extractEmoji = (name: string) => {
  const emojiRegex = /(\p{Emoji_Presentation}|\p{Extended_Pictographic})/gu;
  const matches = [...name.matchAll(emojiRegex)];
  return matches.length > 0 ? matches[0][0] : null;
};

// Extract text content without emoji
const extractText = (name: string) => {
  return name.replace(/\p{Emoji_Presentation}|\p{Extended_Pictographic}/gu, '').trim();
};

// Generate a color based on string
const stringToColor = (str: string) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = Math.abs(hash % 360);
  return `hsl(${hue}, 70%, 60%)`;
};

// Local component for listing chatrooms with avatars + member counts
function ChatroomList({
  chatrooms,
  userChatrooms,
  onJoinChatroom,
  onLeaveChatroom,
  onDeleteChatroom,
  currentUserId,
}: {
  chatrooms: Chatroom[]; // Updated to use Chatroom interface
  userChatrooms: string[];
  onJoinChatroom: (chatroomId: string) => void;
  onLeaveChatroom: (chatroomId: string) => void;
  onDeleteChatroom: (chatroomId: string) => Promise<void>;
  currentUserId?: string;
}) {
  return (
    <div className="space-y-3 p-4">
      {chatrooms.map((room) => {
        const isJoined = userChatrooms.includes(room._id);
        const emoji = extractEmoji(room.name);
        const displayName = extractText(room.name);
        const memberCount = room.memberCount || 0;
        const bgColor = stringToColor(room._id);

        return (
          <div
            key={room._id}
            className="flex items-center justify-between p-4 rounded-xl bg-white shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100"
          >
            {/* Avatar + Info */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Avatar with emoji or first letter */}
              <div
                className="h-12 w-12 flex-shrink-0 rounded-xl flex items-center justify-center text-2xl"
                style={{ backgroundColor: bgColor + '20', color: bgColor }}
              >
                {emoji || room.name?.charAt(0).toUpperCase()}
              </div>

              {/* Chatroom info */}
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-gray-900 truncate">
                    {displayName}
                  </h3>
                  {emoji && (
                    <span className="text-lg flex-shrink-0">{emoji}</span>
                  )}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-block h-2 w-2 rounded-full ${memberCount > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                  <p className="text-sm text-gray-500">
                    {memberCount} {memberCount === 1 ? 'person' : 'people'} online
                  </p>
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-2 pl-2">
              {isJoined ? (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onLeaveChatroom(room._id);
                    }}
                    className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium bg-red-50 text-red-600 hover:bg-red-100 transition-colors"
                    title="Leave chatroom"
                  >
                    <span className="text-red-500">❌</span>
                    <span className="hidden sm:inline">Leave</span>
                  </button>
                  {room.creatorId === currentUserId && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (confirm('Are you sure you want to delete this chatroom? This cannot be undone.')) {
                          onDeleteChatroom(room._id);
                        }
                      }}
                      className="p-1.5 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                      title="Delete chatroom"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </>
              ) : (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onJoinChatroom(room._id);
                  }}
                  className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium bg-green-50 text-green-700 hover:bg-green-100 transition-colors"
                  title="Join chatroom"
                >
                  <span className="text-green-500">🔗</span>
                  <span className="hidden sm:inline">Join</span>
                </button>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default function ChatroomsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isLoading: isUserLoading } = useConvexUser();
  const userId = user?._id;
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isSubmitNewsDialogOpen, setIsSubmitNewsDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Fetch all chatrooms (with category filter if a category is selected)
  const chatroomsData = useQuery(
    api.chatrooms.getAllChatrooms,
    selectedCategory ? { category: selectedCategory as any } : {} // 'any' for category is a workaround if CHAT_CATEGORIES type doesn't perfectly align with api types, consider refining Convex function arg types
  ) || [];

  // NEW: Fetch recent local news submissions
  const recentLocalNews = useQuery(api.localNews.getRecentLocalNews, {});

  // State to store the local news chatroom ID
  const [localNewsChatroomId, setLocalNewsChatroomId] = useState<Id<"chats"> | null>(null);
  const ensureLocalNews = useMutation(api.chatrooms.ensureLocalNewsChatroom);

  // Ensure the local news chatroom exists on component mount
  useEffect(() => {
    const ensureChatroom = async () => {
      try {
        console.log('Ensuring local news chatroom exists...');
        const chatroomId = await ensureLocalNews({});
        console.log('Local news chatroom ID:', chatroomId);
        setLocalNewsChatroomId(chatroomId);
      } catch (error) {
        console.error("Error ensuring local news chatroom:", error);
      }
    };
    if (userId) { // Only ensure if a user is logged in
      ensureChatroom();
    }
  }, [ensureLocalNews, userId]);

  // Get the local news chatroom details from the existing data
  const localNewsChatroomFromData = chatroomsData.find((cr)=> cr.name === '📰 Local News');

  // Prepare chatrooms array, ensuring local news is present and updated
  let chatrooms: Chatroom[] = (chatroomsData || []).map(cr => ({
    ...cr,
    name: cr.name || 'Unnamed Chatroom'
  }));

  if (localNewsChatroomId) {
    // If local news chatroom exists in fetched data, update its description
    if (localNewsChatroomFromData) {
      chatrooms = chatrooms.map((cr: Chatroom) =>
        cr._id === localNewsChatroomFromData._id
          ? {
              ...cr,
              description: recentLocalNews?.[0]?.title
                ? `Latest: ${recentLocalNews[0].title}`
                : cr.description || 'Stay updated with the latest local news and announcements'
            }
          : cr
      );
    } else {
      // If the chatroom ID exists but the chatroom isn't in the fetched data (e.g., due to filter or initial load),
      // add a placeholder for it. This placeholder might be replaced by actual data on subsequent fetches.
      // This helps ensure the "Submit News" button is enabled early.
      const defaultLocalNewsChatroom: Chatroom = {
        _id: localNewsChatroomId,
        _creationTime: Date.now(),
        name: '📰 Local News',
        description: recentLocalNews?.[0]?.title
          ? `Latest: ${recentLocalNews[0].title}`
          : 'Stay updated with the latest local news and announcements',
        memberCount: 0,
        isPublic: true,
        category: 'local-news',
        creatorId: 'system' as Id<'users'>, // Assuming 'system' is a valid Id<'users'> or you have a specific system user ID
        isPlaceholder: true,
        creator: null,
        participants: []
      };
      chatrooms = [defaultLocalNewsChatroom, ...chatrooms];
    }
  }

  // Filter out any null or undefined chatrooms that might have snuck in (though types should prevent this now)
  chatrooms = chatrooms.filter(Boolean);

  // Fetch user's joined chatrooms (only if user is authenticated)
  const userChatrooms = useQuery(
    api.chatrooms.getUserChatrooms,
    userId ? { userId: userId as Id<"users"> } : 'skip'
  ) || [];

  // Mutations
  const createChatroom = useMutation(api.chatrooms.createChatroom);
  const joinChatroom = useMutation(api.chatrooms.joinChatroom);
  const leaveChatroom = useMutation(api.chatrooms.leaveChatroom);
  const deleteChatroom = useMutation(api.chatrooms.deleteChatroom);

  // Create new chatroom
  const handleCreateChatroom = async (data: {
    name: string
    description?: string
    isPrivate: boolean
    location?: string
    tags?: string[]
    category?: string
  }) => {
    // Ensure category is valid and properly formatted
    const formattedData = {
      ...data,
      category: validateChatCategory(data.category) // Using statically imported function
    };
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to create a chatroom",
        variant: "destructive",
      })
      return
    }

    try {
      const chatroomId = await createChatroom({
        name: formattedData.name,
        description: formattedData.description,
        isPrivate: formattedData.isPrivate,
        location: formattedData.location,
        tags: formattedData.tags || [],
        creatorId: userId as Id<"users">,
        category: formattedData.category as (typeof CHAT_CATEGORIES)[number], // Ensure category type matches Convex
      })

      toast({
        title: "Success",
        description: "Chatroom created successfully",
      })

      router.push(`/chat/${chatroomId}`)
    } catch (error) {
      console.error("Error creating chatroom:", error)
      toast({
        title: "Error",
        description: "Failed to create chatroom",
        variant: "destructive",
      })
    }
  }

  // Join
  const handleJoinChatroom = async (chatroomId: string) => {
    if (!userId) {
      console.log('Cannot join: User not authenticated');
      return;
    }

    try {
      console.log('Attempting to join chatroom:', chatroomId);
      await joinChatroom({
        chatroomId: chatroomId as Id<"chats">,
        userId: userId as Id<"users">,
        userName: user?.name || 'Anonymous',
        userProfileImage: user?.avatar || undefined
      });

      // Invalidate queries to refresh the UI
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [api.chatrooms.getAllChatrooms] }), // More specific invalidation for Convex
        queryClient.invalidateQueries({ queryKey: [api.chatrooms.getUserChatrooms, { userId: userId as Id<"users"> }] }) // More specific invalidation for Convex
      ]);

      toast({
        title: "Success",
        description: "You have joined the chatroom",
      });

      router.push(`/chat/${chatroomId}`);
    } catch (error) {
      console.error("Error joining chatroom:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to join chatroom",
        variant: "destructive",
      });
    }
  };

  // Leave chatroom
  const handleLeaveChatroom = async (chatroomId: string) => {
    if (!userId) {
      console.log('Cannot leave: User not authenticated');
      return;
    }

    try {
      console.log('Attempting to leave chatroom:', chatroomId);
      await leaveChatroom({
        chatroomId: chatroomId as Id<"chats">,
        userId: userId as Id<"users">
      });

      // Invalidate queries to refresh the UI
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [api.chatrooms.getAllChatrooms] }),
        queryClient.invalidateQueries({ queryKey: [api.chatrooms.getUserChatrooms, { userId: userId as Id<"users"> }] })
      ]);

      toast({
        title: "Success",
        description: "You have left the chatroom",
      });
    } catch (error) {
      console.error("Error leaving chatroom:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to leave chatroom",
        variant: "destructive",
      });
    }
  };

  const handleDeleteChatroom = async (chatroomId: string) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to delete a chatroom",
        variant: "destructive",
      });
      return;
    }

    try {
      await deleteChatroom({
        chatroomId: chatroomId as Id<"chats">,
        userId: userId as Id<"users">
      });

      queryClient.invalidateQueries({ queryKey: [api.chatrooms.getAllChatrooms] }); // Invalidate after delete

      toast({
        title: "Chatroom deleted",
        description: "The chatroom has been deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting chatroom:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete chatroom",
        variant: "destructive",
      });
    }
  }

  // Unified loading state check
  const isLoading = isUserLoading || chatroomsData === undefined || (userId && userChatrooms === undefined) || recentLocalNews === undefined;

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!userId) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8 text-center">
        <h2 className="text-2xl font-bold mb-2">Sign In Required</h2>
        <p className="text-muted-foreground mb-6">Please sign in to view and join chatrooms</p>
        <Button onClick={() => router.push('/sign-in')}>
          Sign In
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Chatrooms</h1>
          <div className="flex gap-2">
            <Button
              onClick={() => setIsSubmitNewsDialogOpen(true)}
              variant="outline"
              size="sm"
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
              disabled={!localNewsChatroomId}
              title={!localNewsChatroomId ? "The 'Local News' chatroom must exist to submit news." : "Submit local news"}
            >
              <Megaphone className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="hidden xs:inline">Submit</span> News
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              Create Chatroom
            </Button>
          </div>
        </div>
        <ChatroomCategories
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
        />
      </div>

      {/* NEW: Display Recent Local News section */}
      {recentLocalNews && recentLocalNews.length > 0 && (
        <div className="p-4 bg-gray-50/50 border-b border-gray-100">
          <h2 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
            <span className="text-xl">🌟</span> Recently Submitted Local News
          </h2>
          <div className="space-y-3">
            {recentLocalNews.map((article: LocalNewsItem) => (
              <NewsItem
                key={article._id}
                title={article.title}
                content={article.description} // Matches the prop expected by NewsItem based on user's code
                url={article.url || ''}
                source={`Submitted by ${article.userName || 'Unknown User'}`}
                imageUrl={article.userProfileImage || undefined} // Explicitly pass undefined if null/empty string
              />
            ))}
          </div>
        </div>
      )}

      <div className="flex-1 overflow-auto">
        <ChatroomList
          chatrooms={chatrooms}
          userChatrooms={userChatrooms}
          onJoinChatroom={handleJoinChatroom}
          onLeaveChatroom={handleLeaveChatroom}
          onDeleteChatroom={handleDeleteChatroom}
          currentUserId={userId}
        />
      </div>

      <CreateChatroomDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateChatroom}
      />

      {/* News Submission Dialog - Only render when we have a valid chatroom ID */}
      {localNewsChatroomId && (
        <SubmitNewsDialog
          open={isSubmitNewsDialogOpen}
          onOpenChange={setIsSubmitNewsDialogOpen}
          localNewsChatroomId={localNewsChatroomId}
        />
      )}
    </div>
  )
}