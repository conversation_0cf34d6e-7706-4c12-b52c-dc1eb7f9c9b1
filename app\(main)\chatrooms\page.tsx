"use client"

import { useState } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useConvexUser } from "@/hooks/use-convex-auth"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/chatrooms/chatroom-list"
import CreateChatroomDialog from "@/components/chatrooms/create-chatroom-dialog"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ChatroomCategories } from "@/components/chatrooms/chatroom-categories"
import { Id } from "@/convex/_generated/dataModel"

export default function ChatroomsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { userId, isLoading: isUserLoading } = useConvexUser()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Fetch all chatrooms
  const chatrooms = useQuery(
    api.chatrooms.getAllChatrooms,
    { category: selectedCategory || "topical" },
  )

  // Fetch user's chatrooms
  const userChatrooms = useQuery(api.chatrooms.getUserChatrooms, userId ? { userId: userId as Id<"users"> } : "skip") || []

  // Mutations
  const createChatroom = useMutation(api.chatrooms.createChatroom)
  const joinChatroom = useMutation(api.chatrooms.joinChatroom)

  const handleCreateChatroom = async (data: {
    name: string
    description?: string
    isPrivate: boolean
    location?: string
    tags?: string[]
    category?: string
  }) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to create a chatroom",
        variant: "destructive",
      })
      return
    }

    try {
      const chatroomId = await createChatroom({
        name: data.name,
        description: data.description,
        isPrivate: data.isPrivate,
        isPublic: !data.isPrivate,
        location: data.location,
        tags: data.tags,
        creatorId: userId as Id<"users">,
        category: (data.category as any) || "topical", // Default category
      })

      toast({
        title: "Success",
        description: "Chatroom created successfully",
      })

      // Navigate to the new chatroom
      router.push(`/chat/${chatroomId}`)
    } catch (error) {
      console.error("Error creating chatroom:", error)
      toast({
        title: "Error",
        description: "Failed to create chatroom",
        variant: "destructive",
      })
    }
  }

  const handleJoinChatroom = async (chatroomId: string) => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to join a chatroom",
        variant: "destructive",
      })
      return
    }

    try {
      await joinChatroom({
        chatroomId: chatroomId as Id<"chats">,
        userId: userId as Id<"users">,
      })

      toast({
        title: "Success",
        description: "Joined chatroom successfully",
      })

      // Navigate to the chatroom
      router.push(`/chat/${chatroomId}`)
    } catch (error) {
      console.error("Error joining chatroom:", error)
      toast({
        title: "Error",
        description: "Failed to join chatroom",
        variant: "destructive",
      })
    }
  }

  if (isUserLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="h-full flex flex-col">
      <ChatroomCategories selectedCategory={selectedCategory} onSelectCategory={setSelectedCategory} />
      <div className="flex-1 overflow-auto">
        <ChatroomList
          chatrooms={chatrooms || []}
          userChatrooms={userChatrooms}
          onJoinChatroom={handleJoinChatroom}
          onCreateChatroom={() => setIsCreateDialogOpen(true)}
        />
      </div>

      <CreateChatroomDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateChatroom}
      />
    </div>
  )
}

