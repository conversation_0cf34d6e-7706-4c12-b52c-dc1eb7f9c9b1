"use client"

import { useState, useEffect } from "react"
import { useUser } from "@clerk/nextjs"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Coins, User, Settings, Bell, Palette, Globe, Wifi } from "lucide-react"
import { useConvexUser } from "@/hooks/use-convex-auth"
import { useToast } from "@/components/ui/use-toast"
import { MoolaIcon } from "@/components/moola-icon"
import { MoodSelector } from "@/components/mood-selector"
import { ThemeSelector } from "@/components/theme-selector"
import { ScrollArea } from "@/components/ui/scroll-area"
import { MoolaUsageStats } from "@/components/moola/moola-usage-stats"
import { DataModeIndicator } from "@/components/data-mode-indicator"
import { useDataMode } from "@/hooks/use-data-mode"
import type { UserMood } from "@/lib/types"
import { useMediaQuery } from "@/hooks/use-media-query"
import { StatusUpdater } from "@/components/profile/status-updater"

export default function ProfilePage() {
  const { user: clerkUser } = useUser()
  const { userId } = useConvexUser()
  const { toast } = useToast()
  const isMobile = useMediaQuery("(max-width: 768px)")
  const { dataMode, toggleDataMode } = useDataMode()

  // Track if component is mounted to avoid hydration mismatch
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const [statusMessage, setStatusMessage] = useState("")
  const [mood, setMood] = useState("")
  const [language, setLanguage] = useState("en")
  const [activeTab, setActiveTab] = useState("account")

  const user = useQuery(api.users.getUserByClerkId, clerkUser?.id ? { clerkId: clerkUser.id } : "skip")
  const updateUser = useMutation(api.users.updateUser)
  const purchaseMoola = useMutation(api.moola.purchaseMoola)
  const transactions = useQuery(api.moola.getTransactions, user?._id ? { userId: user._id } : "skip")

  // Set initial values from user data when it loads
  useEffect(() => {
    if (user) {
      setStatusMessage(user.statusMessage || "")
      setMood(user.mood || "")
      setLanguage(user.language || "en")
    }
  }, [user])

  const handleUpdateProfile = async () => {
    if (!clerkUser?.id || !user) return

    try {
      await updateUser({
        userId: user._id,
        statusMessage,
        mood: mood as UserMood,
        language,
      })

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      })
    }
  }

  const handlePurchaseMoola = async (amount: number) => {
    if (!clerkUser?.id || !user) return

    try {
      await purchaseMoola({
        userId: user._id,
        amount,
        paymentMethod: "card",
      })

      toast({
        title: "Moola purchased",
        description: `You have successfully purchased ${amount} Moola`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to purchase Moola",
        variant: "destructive",
      })
    }
  }

  // Render transaction as a card for mobile view
  const renderTransactionCard = (transaction: any) => (
    <Card key={transaction._id} className="mb-2">
      <CardContent className="p-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm font-medium">{transaction.description}</p>
            <p className="text-xs text-muted-foreground">{new Date(transaction.timestamp).toLocaleDateString()}</p>
          </div>
          <p className={`font-medium ${transaction.amount > 0 ? "text-green-600" : "text-red-600"}`}>
            {transaction.amount > 0 ? "+" : ""}
            {transaction.amount}
          </p>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <ScrollArea className="h-[calc(100vh-7rem)] md:h-[calc(100vh-4rem)] w-full safe-bottom">
      <div className="container max-w-5xl mx-auto py-4 md:py-6 space-y-4 md:space-y-8 px-4 md:px-6 pb-20 md:pb-6">
        <Tabs defaultValue="account" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 mb-4 md:mb-8">
            <TabsTrigger value="account" className="text-sm md:text-base">Account</TabsTrigger>
            <TabsTrigger value="moola" className="text-sm md:text-base">Moola</TabsTrigger>
            <TabsTrigger value="settings" className="text-sm md:text-base">Settings</TabsTrigger>
          </TabsList>

          {/* Account Tab */}
          <TabsContent value="account" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader className="px-4 md:px-6">
                <CardTitle className="text-lg md:text-xl">Profile Information</CardTitle>
                <CardDescription className="text-sm">Update your personal details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 px-4 md:px-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <Avatar className="h-16 w-16 md:h-24 md:w-24 mx-auto md:mx-0">
                    <AvatarImage src={clerkUser?.imageUrl} />
                    <AvatarFallback>
                      {clerkUser?.firstName?.charAt(0)}
                      {clerkUser?.lastName?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-1 text-center md:text-left">
                    <h3 className="text-lg font-medium">{clerkUser?.fullName}</h3>
                    <p className="text-sm text-muted-foreground">{clerkUser?.primaryEmailAddress?.emailAddress}</p>
                  </div>
                </div>
                <StatusUpdater
                  initialStatus={statusMessage}
                  onStatusChange={setStatusMessage}
                />
                <MoodSelector
                  selectedMood={mood as UserMood}
                  onMoodSelect={setMood}
                />
              </CardContent>
              <CardFooter className="px-4 md:px-6">
                <Button onClick={handleUpdateProfile} className="w-full md:w-auto">Save Changes</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Moola Tab */}
          <TabsContent value="moola" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Moola Balance</CardTitle>
                <CardDescription>Your current Moola balance and transactions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <MoolaIcon className="h-6 w-6 text-yellow-500" />
                    <span className="text-xl font-bold">{user?.moola || 0}</span>
                  </div>
                  <Button onClick={() => handlePurchaseMoola(100)}>Buy Moola</Button>
                </div>
                {user?._id && <MoolaUsageStats userId={user._id} key={user._id} />}
              </CardContent>
            </Card>

            {/* Transactions List */}
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>Recent Moola transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {transactions?.map((transaction) => 
                    renderTransactionCard(transaction)
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Data Settings</CardTitle>
                <CardDescription>Manage how you use data and Moola</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium">Current Data Mode</h3>
                    <p className="text-sm text-muted-foreground">
                      {dataMode === "mobile"
                        ? "Using your mobile data plan"
                        : "Using Moola for data"}
                    </p>
                  </div>
                  <DataModeIndicator />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>Customize your experience</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger id="language">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <ThemeSelector />
              </CardContent>
              <CardFooter>
                <Button onClick={handleUpdateProfile}>Save Preferences</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  )
}














