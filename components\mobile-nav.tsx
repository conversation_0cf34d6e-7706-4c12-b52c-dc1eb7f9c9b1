"use client"

import { usePathname, useRouter } from "next/navigation"
import {
  MessageSquare,
  Users,
  Home,
  UserPlus,
  Activity,
  ShoppingCart,
  Bell
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/use-auth"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"

export function MobileNav() {
  const pathname = usePathname()
  const router = useRouter()
  const { userId } = useAuth()

  // Get unread message count
  const unreadCount = useQuery(
    api.messages.getUnreadMessageCount,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  // Get notification count
  const notificationCount = useQuery(
    api.notifications.getUnreadNotificationCount,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )

  const routes = [
    {
      href: "/chat/mobile",
      label: "Chats",
      icon: MessageSquare,
      active: pathname === "/chat/mobile" || pathname?.startsWith("/chat/"),
      badge: unreadCount && unreadCount > 0 ? unreadCount : null
    },
    {
      href: "/contacts",
      label: "Contacts",
      icon: UserPlus,
      active: pathname === "/contacts" || pathname === "/chat/mobile/contacts",
    },
    {
      href: "/chatrooms",
      label: "Rooms",
      icon: Users,
      active: pathname === "/chatrooms",
    },
    {
      href: "/tradepost",
      label: "Trade",
      icon: ShoppingCart,
      active: pathname === "/tradepost" || pathname?.startsWith("/tradepost/"),
    }
  ]

  // Filter out routes if needed based on some condition, otherwise render all
  const visibleRoutes = routes; // .filter(route => /* some condition */)

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 flex h-14 items-center justify-around bg-background border-t md:hidden">
      {visibleRoutes.map((route) => (
        <button
          key={route.href}
          aria-label={route.label}
          className={cn(
            "flex h-full flex-1 flex-col items-center justify-center space-y-1 px-1",
            route.active
              ? "text-primary"
              : "text-muted-foreground hover:text-foreground",
            "transition-colors duration-150 ease-in-out"
          )}
          onClick={() => router.push(route.href)}
        >
          <div className="relative">
            <route.icon className="h-5 w-5 flex-shrink-0" />
            {route.badge && (
              <Badge
                variant="destructive"
                className="absolute -top-2 -right-2 h-4 min-w-4 px-1 text-[10px] flex items-center justify-center"
              >
                {route.badge > 99 ? "99+" : route.badge}
              </Badge>
            )}
          </div>
          <span className="text-xs font-medium truncate w-full text-center">{route.label}</span>
        </button>
      ))}

      {/* Moola display (Keep commented out unless you reposition it appropriately for mobile bottom nav) */}
      {/* It might look better in the top header (MainNav) even on mobile */}
      {/* <div className="absolute ..."> ... </div> */}
    </div>
  )
}