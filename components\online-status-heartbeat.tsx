"use client"

import { useEffect, useRef } from "react"
import { useAuth } from "@/hooks/use-auth"

export function OnlineStatusHeartbeat() {
  const { userId, updateUserStatus } = useAuth()
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Only set up heartbeat if we have a userId
    if (!userId) return

    // Set initial status
    updateUserStatus("online").catch(console.error)

    // Set up heartbeat to maintain online status
    heartbeatIntervalRef.current = setInterval(() => {
      if (document.visibilityState === "visible") {
        updateUserStatus("online").catch(console.error)
      }
    }, 60000) // Send heartbeat every minute

    // Set up beforeunload handler to set offline status when user closes the page
    const handleBeforeUnload = () => {
      // Use synchronous approach for beforeunload
      navigator.sendBeacon("/api/set-offline", JSON.stringify({ userId }))
    }

    window.addEventListener("beforeunload", handleBeforeUnload)

    return () => {
      // Clean up interval and event listener
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
      }
      window.removeEventListener("beforeunload", handleBeforeUnload)

      // Don't set offline here - that's handled by the beforeunload handler
    }
  }, [userId, updateUserStatus])

  return null
}

