"use client"

import { useEffect } from "react"
import { useOnlineUsers } from "@/hooks/use-online-users"

/**
 * This component maintains the user's online status by sending periodic heartbeats.
 * It's a simple component that uses the useOnlineUsers hook to keep the user's
 * presence status up to date.
 */
export function OnlineStatusHeartbeat() {
  const { updatePresence } = useOnlineUsers()

  useEffect(() => {
    // Initial presence update
    updatePresence().catch(console.error)
    
    // Set up visibility change handler to update presence when the tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updatePresence().catch(console.error)
      }
    }
    
    // Set up beforeunload handler to clean up when the user leaves
    const handleBeforeUnload = () => {
      // Use sendBeacon as it's more reliable for sending data during page unload
      navigator.sendBeacon('/api/set-offline', JSON.stringify({ 
        timestamp: Date.now() 
      }))
    }
    
    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('beforeunload', handleBeforeUnload)
    
    // Clean up event listeners on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [updatePresence])

  // This component doesn't render anything
  return null
}

