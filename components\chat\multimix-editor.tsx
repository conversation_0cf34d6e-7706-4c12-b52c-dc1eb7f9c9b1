"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, Type, Palette } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { TextAlign } from "sharp"

interface MultiMixEditorProps {
  value: string
  onChange: (value: string) => void
  onStyleChange: (style: string) => void
}

export function MultiMixEditor({ value, onChange, onStyleChange }: MultiMixEditorProps) {
  const [fontFamily, setFontFamily] = useState("sans")
  const [fontSize, setFontSize] = useState("medium")
  const [textAlign, setTextAlign] = useState("left")
  const [isBold, setIsBold] = useState(false)
  const [isItalic, setIsItalic] = useState(false)
  const [isUnderline, setIsUnderline] = useState(false)
  const [textColor, setTextColor] = useState("#000000")
  const [bgColor, setBgColor] = useState("transparent")

  const updateStyle = () => {
    const style = {
      fontFamily:
        fontFamily === "sans"
          ? "sans-serif"
          : fontFamily === "serif"
            ? "serif"
            : fontFamily === "mono"
              ? "monospace"
              : "cursive",
      fontSize:
        fontSize === "small"
          ? "0.875rem"
          : fontSize === "medium"
            ? "1rem"
            : fontSize === "large"
              ? "1.25rem"
              : "1.5rem",
      textAlign,
      fontWeight: isBold ? "bold" : "normal",
      fontStyle: isItalic ? "italic" : "normal",
      textDecoration: isUnderline ? "underline" : "none",
      color: textColor,
      backgroundColor: bgColor === "transparent" ? "transparent" : bgColor,
    }

    onStyleChange(JSON.stringify(style))
  }

  const handleFontFamilyChange = (value: string) => {
    setFontFamily(value)
    updateStyle()
  }

  const handleFontSizeChange = (value: string) => {
    setFontSize(value)
    updateStyle()
  }

  const handleTextAlignChange = (value: string) => {
    setTextAlign(value)
    updateStyle()
  }

  const toggleBold = () => {
    setIsBold(!isBold)
    updateStyle()
  }

  const toggleItalic = () => {
    setIsItalic(!isItalic)
    updateStyle()
  }

  const toggleUnderline = () => {
    setIsUnderline(!isUnderline)
    updateStyle()
  }

  const handleTextColorChange = (value: string) => {
    setTextColor(value)
    updateStyle()
  }

  const handleBgColorChange = (value: string) => {
    setBgColor(value)
    updateStyle()
  }

  const previewStyle = {
    fontFamily:
      fontFamily === "sans"
        ? "sans-serif"
        : fontFamily === "serif"
          ? "serif"
          : fontFamily === "mono"
            ? "monospace"
            : "cursive",
    fontSize:
      fontSize === "small" ? "0.875rem" : fontSize === "medium" ? "1rem" : fontSize === "large" ? "1.25rem" : "1.5rem",
    textAlign,
    fontWeight: isBold ? "bold" : "normal",
    fontStyle: isItalic ? "italic" : "normal",
    textDecoration: isUnderline ? "underline" : "none",
    color: textColor,
    backgroundColor: bgColor,
  }

  return (
    <div className="space-y-4 p-2 border rounded-lg bg-muted/30">
      <div className="flex flex-wrap gap-2">
        <Select value={fontFamily} onValueChange={handleFontFamilyChange}>
          <SelectTrigger className="w-[120px]">
            <Type className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="sans">Sans-serif</SelectItem>
            <SelectItem value="serif">Serif</SelectItem>
            <SelectItem value="mono">Monospace</SelectItem>
            <SelectItem value="cursive">Cursive</SelectItem>
          </SelectContent>
        </Select>

        <Select value={fontSize} onValueChange={handleFontSizeChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="small">Small</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="large">Large</SelectItem>
            <SelectItem value="xlarge">X-Large</SelectItem>
          </SelectContent>
        </Select>

        <div className="flex border rounded-md">
          <Button variant="ghost" size="icon" className={isBold ? "bg-muted" : ""} onClick={toggleBold}>
            <Bold className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className={isItalic ? "bg-muted" : ""} onClick={toggleItalic}>
            <Italic className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className={isUnderline ? "bg-muted" : ""} onClick={toggleUnderline}>
            <Underline className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex border rounded-md">
          <Button
            variant="ghost"
            size="icon"
            className={textAlign === "left" ? "bg-muted" : ""}
            onClick={() => handleTextAlignChange("left")}
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={textAlign === "center" ? "bg-muted" : ""}
            onClick={() => handleTextAlignChange("center")}
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={textAlign === "right" ? "bg-muted" : ""}
            onClick={() => handleTextAlignChange("right")}
          >
            <AlignRight className="h-4 w-4" />
          </Button>
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="w-[120px]">
              <Palette className="h-4 w-4 mr-2" />
              Colors
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[240px]">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Text Color</h4>
                <div className="grid grid-cols-5 gap-2">
                  {[
                    "#000000",
                    "#FF0000",
                    "#00FF00",
                    "#0000FF",
                    "#FFFF00",
                    "#FF00FF",
                    "#00FFFF",
                    "#FF9900",
                    "#9900FF",
                    "#FFFFFF",
                  ].map((color) => (
                    <div
                      key={color}
                      className={`h-6 w-6 rounded-md cursor-pointer border ${textColor === color ? "ring-2 ring-primary" : ""}`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleTextColorChange(color)}
                    />
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Background Color</h4>
                <div className="grid grid-cols-5 gap-2">
                  {[
                    "transparent",
                    "#FFFFFF",
                    "#FFDDDD",
                    "#DDFFDD",
                    "#DDDDFF",
                    "#FFFFDD",
                    "#FFDDFF",
                    "#DDFFFF",
                    "#FFEECC",
                    "#EECCFF",
                  ].map((color) => (
                    <div
                      key={color}
                      className={`h-6 w-6 rounded-md cursor-pointer border ${bgColor === color ? "ring-2 ring-primary" : ""}`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleBgColorChange(color)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="min-h-[100px] p-2 border rounded-md bg-white" style={{ ...previewStyle, textAlign: textAlign as TextAlign }}>
        <textarea
          className="w-full h-full min-h-[80px] bg-transparent border-none focus:outline-none resize-none"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Type your MultiMix message..."
          style={{ ...previewStyle, textAlign: textAlign as TextAlign }}
        />
      </div>
    </div>
  )
}

