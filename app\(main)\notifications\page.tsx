"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bell, Check, Trash2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"
import { Id } from "@/convex/_generated/dataModel"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"

export default function NotificationsPage() {
  const { userId } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  
  // Get notifications
  const notificationsResult = useQuery(
    api.notifications.getNotifications,
    userId ? { userId: userId as Id<"users"> } : "skip"
  )
  
  // Mutations
  const markAsRead = useMutation(api.notifications.markNotificationAsRead)
  const markAllAsRead = useMutation(api.notifications.markAllNotificationsAsRead)
  const deleteNotification = useMutation(api.notifications.deleteNotification)
  
  // Handle mark as read
  const handleMarkAsRead = async (notificationId: Id<"notifications">) => {
    if (!userId) return
    
    try {
      await markAsRead({
        notificationId,
        userId: userId as Id<"users">,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to mark notification as read",
        variant: "destructive",
      })
    }
  }
  
  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (!userId) return
    
    try {
      await markAllAsRead({
        userId: userId as Id<"users">,
      })
      
      toast({
        title: "Success",
        description: "All notifications marked as read",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to mark all notifications as read",
        variant: "destructive",
      })
    }
  }
  
  // Handle delete notification
  const handleDeleteNotification = async (notificationId: Id<"notifications">) => {
    if (!userId) return
    
    try {
      await deleteNotification({
        notificationId,
        userId: userId as Id<"users">,
      })
      
      toast({
        title: "Success",
        description: "Notification deleted",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete notification",
        variant: "destructive",
      })
    }
  }
  
  // Handle notification click
  const handleNotificationClick = async (notification: any) => {
    // Mark as read
    if (!notification.isRead) {
      await handleMarkAsRead(notification._id)
    }
    
    // Navigate to link if available
    if (notification.linkUrl) {
      router.push(notification.linkUrl)
    }
  }
  
  // Filter notifications based on active tab
  const filteredNotifications = notificationsResult?.notifications?.filter((notification) => {
    if (activeTab === "all") return true
    if (activeTab === "unread") return !notification.isRead
    return notification.type === activeTab
  })
  
  // Get notification types for tabs
  const notificationTypes = notificationsResult?.notifications
    ? Array.from(new Set(notificationsResult.notifications.map((n) => n.type)))
    : []
  
  return (
    <div className="container max-w-2xl mx-auto py-4 px-4 pb-20">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">Notifications</h1>
        <Button variant="outline" size="sm" onClick={handleMarkAllAsRead}>
          <Check className="h-4 w-4 mr-2" />
          Mark all as read
        </Button>
      </div>
      
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4 w-full overflow-x-auto flex whitespace-nowrap">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="unread">Unread</TabsTrigger>
          {notificationTypes.map((type) => (
            <TabsTrigger key={type} value={type}>
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value={activeTab}>
          {!notificationsResult ? (
            // Loading state
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-full" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredNotifications?.length === 0 ? (
            // Empty state
            <div className="text-center py-12">
              <Bell className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No notifications</h3>
              <p className="text-muted-foreground">
                You don't have any {activeTab !== "all" ? activeTab + " " : ""}notifications yet
              </p>
            </div>
          ) : (
            // Notifications list
            <div className="space-y-4">
              {filteredNotifications?.map((notification) => (
                <Card 
                  key={notification._id} 
                  className={`transition-colors ${!notification.isRead ? "bg-primary/5 border-primary/20" : ""}`}
                >
                  <CardContent className="p-4">
                    <div 
                      className="flex gap-4 cursor-pointer" 
                      onClick={() => handleNotificationClick(notification)}
                    >
                      {notification.imageUrl ? (
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={notification.imageUrl} alt={notification.title} />
                          <AvatarFallback>
                            {notification.title.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                          <Bell className="h-6 w-6 text-primary" />
                        </div>
                      )}
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <h3 className="font-medium">{notification.title}</h3>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-muted-foreground">
                              {formatDistanceToNow(notification.createdAt, { addSuffix: true })}
                            </span>
                            {!notification.isRead && (
                              <div className="h-2 w-2 rounded-full bg-primary" />
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                      </div>
                    </div>
                    
                    <div className="flex justify-end mt-2">
                      {!notification.isRead && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleMarkAsRead(notification._id)}
                        >
                          <Check className="h-4 w-4 mr-2" />
                          Mark as read
                        </Button>
                      )}
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDeleteNotification(notification._id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
