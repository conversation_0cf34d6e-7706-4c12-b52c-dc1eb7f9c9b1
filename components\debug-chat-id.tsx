// "use client"

// import { useState } from "react"
// import { useParams } from "next/navigation"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { useAuth } from "@/hooks/use-auth"
// import { useQuery, useMutation } from "convex/react"
// import { api } from "@/convex/_generated/api"

// export function DebugChatId() {
//   const [isOpen, setIsOpen] = useState(false)
//   const params = useParams()
//   const { userId } = useAuth()

//   const chatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId

//   // Get detailed chat info
//   const chatDetails = useQuery(api.debug.getChatDetails, chatId ? { chatId } : "skip")

//   // Clear all direct chats (for testing)
//   const clearChats = useMutation(api.debug.clearAllDirectChats)

//   const handleClearChats = async () => {
//     try {
//       const result = await clearChats()
//       alert(`Cleared ${result.deletedCount} chats. Please refresh the page.`)
//     } catch (error) {
//       console.error("Error clearing chats:", error)
//       alert("Failed to clear chats")
//     }
//   }

//   if (!isOpen) {
//     return (
//       <Button
//         className="fixed bottom-20 left-4 z-50 opacity-50"
//         size="sm"
//         variant="outline"
//         onClick={() => setIsOpen(true)}
//       >
//         Debug ID
//       </Button>
//     )
//   }

//   return (
//     <Card className="fixed bottom-20 left-4 z-50 w-80 max-h-[80vh] overflow-auto">
//       <CardHeader className="py-2">
//         <CardTitle className="text-sm flex justify-between">
//           <span>Chat ID Debug</span>
//           <Button size="sm" variant="ghost" onClick={() => setIsOpen(false)}>
//             Close
//           </Button>
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="text-xs space-y-2">
//         <div>
//           <p>
//             <strong>Current Chat ID:</strong> {chatId || "None"}
//           </p>
//           <p>
//             <strong>User ID:</strong> {userId || "None"}
//           </p>
//           <p>
//             <strong>Browser:</strong> {navigator.userAgent}
//           </p>
//         </div>

//         {chatDetails && (
//           <div>
//             <p>
//               <strong>Chat Type:</strong> {chatDetails.chat?.type}
//             </p>
//             <p>
//               <strong>Creator ID:</strong> {chatDetails.chat?.creatorId}
//             </p>
//             <p>
//               <strong>Participants:</strong> {chatDetails.totalParticipants}
//             </p>
//             <div>
//               {chatDetails.participants?.map((p, i) => (
//                 <div key={i} className="pl-2 border-l-2 border-gray-300 mt-1">
//                   <p>
//                     User: {p.userName} ({p.userId})
//                   </p>
//                   <p>Role: {p.role}</p>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}

//         <div className="flex flex-col gap-2 pt-2">
//           <Button size="sm" onClick={() => navigator.clipboard.writeText(chatId || "")}>
//             Copy Chat ID
//           </Button>
//           <Button size="sm" variant="destructive" onClick={handleClearChats}>
//             Clear All Direct Chats
//           </Button>
//         </div>
//       </CardContent>
//     </Card>
//   )
// }

