/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as chat from "../chat.js";
import type * as chatActions from "../chatActions.js";
import type * as chatrooms from "../chatrooms.js";
import type * as chats from "../chats.js";
import type * as contacts from "../contacts.js";
import type * as debug from "../debug.js";
import type * as emoticons from "../emoticons.js";
import type * as init from "../init.js";
import type * as messages from "../messages.js";
import type * as moola from "../moola.js";
import type * as notifications from "../notifications.js";
import type * as test from "../test.js";
import type * as tradepost from "../tradepost.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  chat: typeof chat;
  chatActions: typeof chatActions;
  chatrooms: typeof chatrooms;
  chats: typeof chats;
  contacts: typeof contacts;
  debug: typeof debug;
  emoticons: typeof emoticons;
  init: typeof init;
  messages: typeof messages;
  moola: typeof moola;
  notifications: typeof notifications;
  test: typeof test;
  tradepost: typeof tradepost;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
