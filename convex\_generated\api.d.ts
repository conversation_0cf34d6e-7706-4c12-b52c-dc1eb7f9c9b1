/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as chat from "../chat.js";
import type * as chatActions from "../chatActions.js";
import type * as chatrooms from "../chatrooms.js";
import type * as chats from "../chats.js";
import type * as chatUtils from "../chatUtils.js";
import type * as contacts from "../contacts.js";
import type * as debug from "../debug.js";
import type * as emoticons from "../emoticons.js";
import type * as gameLogic from "../gameLogic.js";
import type * as games from "../games.js";
import type * as init from "../init.js";
import type * as lib_chatUtils from "../lib/chatUtils.js";
import type * as localNews from "../localNews.js";
import type * as messages from "../messages.js";
import type * as migrations_fix_chat_categories from "../migrations/fix_chat_categories.js";
import type * as migrations_update_kingdom_categories from "../migrations/update_kingdom_categories.js";
import type * as moola from "../moola.js";
import type * as notifications from "../notifications.js";
import type * as presence from "../presence.js";
import type * as test from "../test.js";
import type * as tradepost from "../tradepost.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  chat: typeof chat;
  chatActions: typeof chatActions;
  chatrooms: typeof chatrooms;
  chats: typeof chats;
  chatUtils: typeof chatUtils;
  contacts: typeof contacts;
  debug: typeof debug;
  emoticons: typeof emoticons;
  gameLogic: typeof gameLogic;
  games: typeof games;
  init: typeof init;
  "lib/chatUtils": typeof lib_chatUtils;
  localNews: typeof localNews;
  messages: typeof messages;
  "migrations/fix_chat_categories": typeof migrations_fix_chat_categories;
  "migrations/update_kingdom_categories": typeof migrations_update_kingdom_categories;
  moola: typeof moola;
  notifications: typeof notifications;
  presence: typeof presence;
  test: typeof test;
  tradepost: typeof tradepost;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
