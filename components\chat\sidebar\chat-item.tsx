import { formatDistanceToNow } from "date-fns";
import { Trash2 } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Id } from "@/convex/_generated/dataModel";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { useOnlineUsers } from "@/hooks/use-online-users";

interface ChatItemProps {
  chat: any; // Replace with proper type
  isActive: boolean;
  currentUserId: string | null;
  contacts: any[]; // Replace with proper type
  isContactsReady: boolean;
  onChatSelect: (chatId: string) => void;
  onRemoveContact: (contactRelationshipId: string) => Promise<void>;
}

export function ChatItem({
  chat,
  isActive,
  currentUserId,
  contacts,
  isContactsReady,
  onChatSelect,
  onRemoveContact,
}: ChatItemProps) {
  const { toast } = useToast();
  
  if (!chat || !chat.id) {
    console.warn("Skipping rendering chat due to missing data:", chat);
    return null;
  }

  const otherUser = chat.type === "direct" ? chat.participants?.find((p: any) => p?._id !== currentUserId) : null;
  const chatName = chat.type === "direct" ? otherUser?.name : chat.name;
  const chatAvatar = chat.type === "direct" ? otherUser?.avatar : undefined;
  
  // Get real-time online status
  const { isUserOnline } = useOnlineUsers();
  const userStatus = otherUser?._id 
    ? (isUserOnline(otherUser._id) ? 'online' : 'offline')
    : 'offline';

  const lastMessageTime = chat.lastMessage?.timestamp
    ? formatDistanceToNow(new Date(chat.lastMessage.timestamp), { addSuffix: false })
    : chat.updatedAt 
      ? formatDistanceToNow(new Date(chat.updatedAt), { addSuffix: false }) 
      : "";

  const contactInfo = isContactsReady && otherUser?._id
    ? contacts.find((c: any) => c?._id === otherUser._id)
    : undefined;

  const canAttemptDelete = !!(chat.type === 'direct' && otherUser?._id);
  const isDeleteEnabled = canAttemptDelete && isContactsReady && !!contactInfo?.contactRelationshipId;
  
  const deleteButtonTitle = !canAttemptDelete 
    ? "Cannot identify contact"
    : !isContactsReady 
      ? "Contacts loading..."
      : !contactInfo?.contactRelationshipId 
        ? "Contact relationship not found"
        : `Remove ${otherUser?.name || 'contact'}`;

  const handleRemoveClick = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!isDeleteEnabled || !contactInfo?.contactRelationshipId) {
      console.error("Delete action attempted in invalid state.", { isDeleteEnabled, contactInfo });
      toast({ 
        title: "Cannot Remove Contact", 
        description: "Contact data may still be loading or unavailable.", 
        variant: "destructive" 
      });
      return;
    }

    const contactName = otherUser?.name || "this contact";
    if (window.confirm(`Are you sure you want to remove ${contactName} from your contacts?\nThis removes the contact relationship but does not delete the chat history.`)) {
      await onRemoveContact(contactInfo.contactRelationshipId);
    }
  };

  return (
    <div className="relative group rounded-md">
      <Button
        variant="ghost"
        className={`w-full justify-start px-2 py-3 h-auto text-left rounded-md transition-colors ${
          isActive ? "bg-muted hover:bg-muted" : "hover:bg-accent"
        }`}
        aria-label={`Go to chat with ${chatName || 'Unknown'}`}
        onClick={() => onChatSelect(chat.id)}
      >
        <div className="flex items-center gap-3 w-full">
          {/* Avatar and Online Status */}
          <div className="relative flex-shrink-0">
            <Avatar className="h-10 w-10">
              <AvatarImage
                src={chatAvatar || undefined}
                alt={chatName ? `${chatName}'s avatar` : 'Chat avatar'}
              />
              <AvatarFallback className="text-sm">
                {chatName?.substring(0, 2).toUpperCase() || '?'}
              </AvatarFallback>
            </Avatar>
            {chat.type === "direct" && otherUser?._id && (
              <span 
                className={cn(
                  "absolute bottom-0 right-0 w-2.5 h-2.5 rounded-full border-2 border-background",
                  userStatus === 'online' ? 'bg-green-500' : 'bg-gray-400',
                  "transition-colors duration-300"
                )}
                title={userStatus === 'online' ? 'Online' : 'Offline'}
              />
            )}
          </div>

          {/* Chat Name and Last Message */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <p className="font-medium truncate">{chatName || 'Unknown'}</p>
              {lastMessageTime && (
                <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                  {lastMessageTime}
                </span>
              )}
            </div>
            <p className="text-xs text-muted-foreground truncate">
              {chat.lastMessage?.senderId === currentUserId ? "You: " : ""}
              {chat.lastMessage?.content || (chat.type === 'direct' ? "Chat started" : "Group created")}
            </p>
          </div>

          {/* Unread Count Badge */}
          {chat.unreadCount && chat.unreadCount > 0 && (
            <div 
              className="ml-2 h-5 min-w-5 flex items-center justify-center p-0 text-xs rounded-full bg-primary text-primary-foreground"
            >
              {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
            </div>
          )}
        </div>
      </Button>

      {/* Delete Contact Button (only for direct chats) */}
      {canAttemptDelete && (
        <Button
          variant="ghost"
          size="icon"
          aria-label={deleteButtonTitle}
          title={deleteButtonTitle}
          className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity disabled:opacity-30 disabled:cursor-not-allowed"
          disabled={!isDeleteEnabled}
          onClick={handleRemoveClick}
        >
          <Trash2 className="h-4 w-4 text-muted-foreground group-hover:text-destructive group-disabled:hover:text-muted-foreground" />
        </Button>
      )}
    </div>
  );
}
