"use client"

import { useState, useEffect } from "react"
import { useDataMode } from "@/hooks/use-data-mode"
import { MoolaIcon } from "@/components/moola-icon"
import { formatMoolaAmount } from "@/lib/moola-services"
import { Wifi, Zap, AlertTriangle } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface MoolaUsageIndicatorProps {
  messageLength: number
  hasAttachments?: boolean
}

export function MoolaUsageIndicator({ messageLength, hasAttachments = false }: MoolaUsageIndicatorProps) {
  const { dataMode, estimatedMessageCost, messagesRemaining, isLowMoolaBalance } = useDataMode()

  const [cost, setCost] = useState(0)

  // Update cost when message length changes
  useEffect(() => {
    if (dataMode !== "mobile") {
      setCost(estimatedMessageCost(messageLength, hasAttachments))
    }
  }, [messageLength, hasAttachments, dataMode, estimatedMessageCost])

  // Don't show for mobile data mode
  if (dataMode === "mobile") {
    return (
      <div className="flex items-center text-xs text-muted-foreground">
        <Wifi className="h-3.5 w-3.5 mr-1" />
        <span>Using mobile data</span>
      </div>
    )
  }

  // Show for emergency mode
  if (dataMode === "emergency") {
    return (
      <div className="flex items-center text-xs text-yellow-500">
        <Zap className="h-3.5 w-3.5 mr-1" />
        <span>Using emergency Moola</span>
      </div>
    )
  }

  // Show for Moola data mode
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center text-xs text-yellow-500">
            <MoolaIcon className="h-3.5 w-3.5 mr-1" />
            <span>{formatMoolaAmount(cost)}</span>
            {isLowMoolaBalance && <AlertTriangle className="h-3.5 w-3.5 ml-1 text-yellow-500" />}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top">
          <div className="space-y-2 w-48">
            <p>This message will cost {formatMoolaAmount(cost)} Moola</p>
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Messages remaining:</span>
                <span>{messagesRemaining}</span>
              </div>
              <Progress value={(messagesRemaining / (messagesRemaining + 1)) * 100} />
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

