import { v } from "convex/values"
import { mutation } from "./_generated/server"

export const storeUser = mutation({
  args: {
    clerkId: v.string(),
    name: v.string(),
    avatar: v.string(),
    email: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const now = Date.now()

      // Check if user exists
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
        .first()

      if (existingUser) {
        // Update existing user
        await ctx.db.patch(existingUser._id, {
          name: args.name,
          avatar: args.avatar,
          email: args.email,
          updatedAt: now,
        })
        return existingUser._id
      }

      // Create new user
      const userId = await ctx.db.insert("users", {
        clerkId: args.clerkId,
        name: args.name,
        email: args.email,
        avatar: args.avatar,
        status: "online",
        moola: 100, // Start with 100 Moola
        createdAt: now,
        updatedAt: now,
      })

      return userId
    } catch (error) {
      console.error("Error storing user:", error)
      throw new Error("Failed to store user")
    }
  },
})

