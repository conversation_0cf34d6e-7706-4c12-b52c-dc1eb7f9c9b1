import { Id } from "./_generated/dataModel"

// Game logic functions
export async function processTicTacToeMove(game: any, playerId: string, move: { row: number; col: number }) {
  const { row, col } = move
  const playerSymbol = game.players.x === playerId ? "x" : "o"
  
  // Validate the move
  if (row < 0 || row > 2 || col < 0 || col > 2) {
    throw new Error("Invalid move: position out of bounds")
  }
  
  if (game.gameData.board[row][col] !== null) {
    throw new Error("Invalid move: position already taken")
  }
  
  // Make the move
  game.gameData.board[row][col] = playerSymbol
  game.gameData.moves.push({
    player: playerId,
    move,
    timestamp: Date.now(),
  })
  
  // Check for win condition
  if (checkTicTacToeWin(game.gameData.board, playerSymbol)) {
    game.status = "completed"
    game.winner = playerId
    return
  }
  
  // Check for draw
  if (isTicTacToeDraw(game.gameData.board)) {
    game.status = "completed"
    game.winner = "draw"
    return
  }
  
  // Switch players
  game.players.currentPlayer = game.players.currentPlayer === "x" ? "o" : "x"
}

function checkTicTacToeWin(board: string[][], symbol: string): boolean {
  // Check rows and columns
  for (let i = 0; i < 3; i++) {
    // Check row
    if (board[i][0] === symbol && board[i][1] === symbol && board[i][2] === symbol) {
      return true
    }
    // Check column
    if (board[0][i] === symbol && board[1][i] === symbol && board[2][i] === symbol) {
      return true
    }
  }
  
  // Check diagonals
  if (board[0][0] === symbol && board[1][1] === symbol && board[2][2] === symbol) {
    return true
  }
  if (board[0][2] === symbol && board[1][1] === symbol && board[2][0] === symbol) {
    return true
  }
  
  return false
}

function isTicTacToeDraw(board: string[][]): boolean {
  return board.every(row => row.every(cell => cell !== null))
}

export async function processRPSMove(game: any, playerId: string, move: { choice: string }) {
  const isPlayer1 = game.players.x === playerId
  const choiceKey = isPlayer1 ? "player1Choice" : "player2Choice"
  
  // Record the player's choice
  game.gameData[choiceKey] = move.choice
  
  // If both players have made their choices, determine the winner
  if (game.gameData.player1Choice && game.gameData.player2Choice) {
    const result = determineRPSWinner(
      game.gameData.player1Choice,
      game.gameData.player2Choice
    )
    
    game.gameData.result = result
    game.status = "completed"
    
    if (result === "player1") {
      game.winner = game.players.x
    } else if (result === "player2") {
      game.winner = game.players.o
    } else {
      game.winner = "draw"
    }
  } else {
    // Switch to the other player's turn
    game.players.currentPlayer = game.players.currentPlayer === "x" ? "o" : "x"
  }
}

function determineRPSWinner(choice1: string, choice2: string): string {
  if (choice1 === choice2) return "draw"
  
  const winConditions: Record<string, string[]> = {
    rock: ["scissors"],
    paper: ["rock"],
    scissors: ["paper"],
  }
  
  return winConditions[choice1].includes(choice2) ? "player1" : "player2"
}

export async function processDiceRoll(game: any, playerId: string) {
  const isPlayer1 = game.players.x === playerId
  const rollKey = isPlayer1 ? "player1Roll" : "player2Roll"
  
  // Roll the dice (1-6)
  const roll = Math.floor(Math.random() * 6) + 1
  game.gameData[rollKey] = roll
  
  // If both players have rolled, determine the winner
  if (game.gameData.player1Roll && game.gameData.player2Roll) {
    game.status = "completed"
    
    if (game.gameData.player1Roll > game.gameData.player2Roll) {
      game.winner = game.players.x
      game.gameData.result = "player1"
    } else if (game.gameData.player1Roll < game.gameData.player2Roll) {
      game.winner = game.players.o
      game.gameData.result = "player2"
    } else {
      game.winner = "draw"
      game.gameData.result = "draw"
    }
  } else {
    // Switch to the other player's turn
    game.players.currentPlayer = game.players.currentPlayer === "x" ? "o" : "x"
  }
}

export async function processNumberGuess(game: any, playerId: string, guess: number) {
  const { target, guesses } = game.gameData
  
  // Check the guess
  let result: "higher" | "lower" | "correct"
  if (guess === target) {
    result = "correct"
    game.status = "completed"
    game.winner = playerId
  } else {
    result = guess < target ? "higher" : "lower"
    // Switch to the other player's turn
    game.players.currentPlayer = game.players.currentPlayer === "x" ? "o" : "x"
  }
  
  // Record the guess
  game.gameData.guesses.push({
    player: playerId,
    guess,
    result,
    timestamp: Date.now(),
  })
}