// Moola service to handle data usage and marketplace transactions

// Constants for Moola usage rates
export const MOOLA_RATES = {
    // Data usage rates - these are the core of the Moola-for-Data feature
    DATA_PER_KB: 0.005, // 1 Moola = 200KB (more generous than before)
    DATA_PER_MESSAGE: 0.2, // 1 Moola = 5 text messages (making it very clear)
    DATA_PER_MINUTE_ACTIVE: 0.5, // 1 Moola = 2 minutes of active chat
    DATA_PER_MINUTE_BACKGROUND: 0.1, // 1 Moola = 10 minutes in background
  
    // Media costs
    VOICE_NOTE_PER_SECOND: 0.05, // 1 Moola = 20 seconds of voice notes
    IMAGE_UPLOAD: 1, // 1 Moola per image
  
    // TradePost rates
    TRADEPOST_FEE_PERCENT: 5, // 5% fee for marketplace transactions
  
    // Minimum balances
    MIN_BALANCE_FOR_DATA: 5, // Minimum Moola needed to use data mode
  
    // Emergency Moola
    EMERGENCY_MOOLA_AMOUNT: 10, // Amount given to new users
    EMERGENCY_MOOLA_MESSAGES: 5, // Number of messages allowed with emergency Moola
  }
  
  // Calculate data usage cost with more precision
  export function calculateDataUsage(bytes: number): number {
    const kilobytes = bytes / 1024
    return Math.ceil(kilobytes * MOOLA_RATES.DATA_PER_KB * 10) / 10 // Round to 1 decimal
  }
  
  // Calculate message cost - now more transparent
  export function calculateMessageCost(contentLength: number, hasAttachments = false): number {
    // Base cost for a message
    let cost = MOOLA_RATES.DATA_PER_MESSAGE
  
    // Add cost for longer messages (over 500 characters)
    if (contentLength > 500) {
      cost += Math.floor((contentLength - 500) / 500) * 0.1
    }
  
    // Add cost for attachments
    if (hasAttachments) {
      cost += MOOLA_RATES.IMAGE_UPLOAD
    }
  
    return Math.ceil(cost * 10) / 10 // Round to 1 decimal place
  }
  
  // Calculate voice note cost
  export function calculateVoiceNoteCost(durationSeconds: number): number {
    return Math.ceil(durationSeconds * MOOLA_RATES.VOICE_NOTE_PER_SECOND * 10) / 10
  }
  
  // Calculate active time cost
  export function calculateActiveTimeCost(minutesActive: number): number {
    return Math.ceil(minutesActive * MOOLA_RATES.DATA_PER_MINUTE_ACTIVE * 10) / 10
  }
  
  // Calculate background time cost
  export function calculateBackgroundTimeCost(minutesInBackground: number): number {
    return Math.ceil(minutesInBackground * MOOLA_RATES.DATA_PER_MINUTE_BACKGROUND * 10) / 10
  }
  
  // Calculate marketplace fee
  export function calculateMarketplaceFee(amount: number): number {
    return Math.ceil(((amount * MOOLA_RATES.TRADEPOST_FEE_PERCENT) / 100) * 10) / 10
  }
  
  // Format Moola amount for display
  export function formatMoolaAmount(amount: number): string {
    return amount.toFixed(1)
  }
  
  // Estimate how many messages can be sent with given Moola
  export function estimateMessagesWithMoola(moolaAmount: number): number {
    return Math.floor(moolaAmount / MOOLA_RATES.DATA_PER_MESSAGE)
  }
  
  // Estimate how many minutes can be active with given Moola
  export function estimateActiveTimeWithMoola(moolaAmount: number): number {
    return Math.floor(moolaAmount / MOOLA_RATES.DATA_PER_MINUTE_ACTIVE)
  }
  
  // Estimate how many KB can be transferred with given Moola
  export function estimateDataWithMoola(moolaAmount: number): number {
    return Math.floor(moolaAmount / MOOLA_RATES.DATA_PER_KB)
  }
  
  // Check if user has emergency Moola available
  export function hasEmergencyMoola(messagesUsed: number): boolean {
    return messagesUsed < MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES
  }
  
  