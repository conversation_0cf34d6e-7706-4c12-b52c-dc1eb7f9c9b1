<!DOCTYPE html>
<html>
<head>
  <title>Create Icon</title>
</head>
<body>
  <h1>Icon Generator</h1>
  <p>Click the button below to generate and download an icon:</p>
  <button id="generateBtn">Generate Icon</button>
  
  <script>
    document.getElementById('generateBtn').addEventListener('click', function() {
      const canvas = document.createElement('canvas');
      canvas.width = 192;
      canvas.height = 192;
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = '#1a1a2e';
      ctx.fillRect(0, 0, 192, 192);
      
      // Text
      ctx.fillStyle = 'white';
      ctx.font = 'bold 96px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('XC', 96, 96);
      
      // Convert to data URL
      const dataUrl = canvas.toDataURL('image/png');
      
      // Create download link
      const link = document.createElement('a');
      link.download = 'icon.png';
      link.href = dataUrl;
      link.click();
      
      // Create another for the 512 version
      canvas.width = 512;
      canvas.height = 512;
      ctx.fillStyle = '#1a1a2e';
      ctx.fillRect(0, 0, 512, 512);
      ctx.fillStyle = 'white';
      ctx.font = 'bold 256px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('XC', 256, 256);
      
      const dataUrl512 = canvas.toDataURL('image/png');
      const link512 = document.createElement('a');
      link512.download = 'icon-512.png';
      link512.href = dataUrl512;
      setTimeout(() => link512.click(), 100);
    });
  </script>
</body>
</html>

