"use client"

import { useState, useEffect } from "react"
import { useMoola } from "@/hooks/use-moola"
import { MoolaIcon } from "@/components/moola-icon"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function MoolaStatus() {
  const { balance, localBalance, serverBalance, isOffline, pendingTransactions } = useMoola()
  const [isOnline, setIsOnline] = useState(true)
  
  // Update online status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine)
    }
    
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // Initial status
    updateOnlineStatus()
    
    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
    }
  }, [])
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-1 cursor-help">
            <MoolaIcon className="h-4 w-4 text-yellow-500" />
            <span className="font-medium">{balance}</span>
            
            {!isOnline && (
              <Badge variant="outline" className="ml-1 px-1 py-0 h-4 text-xs">
                Offline
              </Badge>
            )}
            
            {pendingTransactions.length > 0 && (
              <Badge variant="secondary" className="ml-1 px-1 py-0 h-4 text-xs">
                {pendingTransactions.length} pending
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1 text-xs">
            <p>Current Moola Balance: {balance}</p>
            {!isOnline && <p>Using offline balance: {localBalance}</p>}
            {serverBalance !== localBalance && (
              <p>Server balance: {serverBalance} (will sync when online)</p>
            )}
            {pendingTransactions.length > 0 && (
              <p>{pendingTransactions.length} transactions pending sync</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
