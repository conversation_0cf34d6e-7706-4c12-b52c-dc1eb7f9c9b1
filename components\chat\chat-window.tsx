// "use client"

// import React from "react"

// import { useState, useRef, useEffect } from "react"
// import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { ScrollArea } from "@/components/ui/scroll-area"
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
// import {
//   ArrowLeft,
//   Send,
//   Video,
//   Phone,
//   MoreVertical,
//   Check,
//   Zap,
//   Mic,
//   Gift,
//   ImageIcon,
//   Type,
//   Trash2,
//   Copy,
//   ArrowDown,
//   Palette,
// } from "lucide-react"
// import { useAuth } from "@/hooks/use-auth"
// import { useMessages } from "@/hooks/use-messages"
// import { LoadingSpinner } from "@/components/ui/loading-spinner"
// import { useToast } from "@/components/ui/use-toast"
// import { format } from "date-fns"
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
// import { EmojiPicker } from "@/components/chat/emoji-picker"
// import { VoiceRecorder } from "@/components/chat/voice-recorder"
// import { MultiMixEditor } from "@/components/chat/multimix-editor"
// import { GiftMoolaDialog } from "@/components/moola/gift-moola-dialog"
// import { useDataSaving } from "@/hooks/use-data-saving"
// import { useChat } from "@/hooks/use-chat"
// import { useMutation } from "convex/react"
// import { api } from "@/convex/_generated/api"
// import { UserStatusBadge } from "@/components/user-status-badge"
// import { VideoCall } from "@/components/chat/video-call"
// import { VoiceCall } from "@/components/chat/voice-call"
// import { useDataMode } from "@/hooks/use-data-mode"
// import { MoolaIcon } from "../moola-icon"
// import type { Id } from "@/convex/_generated/dataModel"
// // import { ChatDebug } from "./chat-debug"
// import { ImageUpload } from "@/components/chat/image-upload"
// import { ChatBackgroundPicker } from "./chat-background-picker"
// import { GameDialog } from "@/components/games/game-dialog"
// import { GameLauncher } from "@/components/games/game-launcher"
// import { ChatInput } from "./chat-input"

// // Scroll to bottom function with smooth scrolling option
// const scrollToBottom = (element: HTMLDivElement | null, behavior: ScrollBehavior = 'auto') => {
//   if (element) {
//     element.scrollTo({
//       top: element.scrollHeight,
//       behavior
//     });
//   }
// }


// // Implement the missing ScrollToBottomButton component
// interface ScrollToBottomButtonProps {
//   visible: boolean;
//   onClick: () => void;
// }

// const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({ visible, onClick }) => {
//   if (!visible) return null;

//   return (
//     <Button
//       variant="secondary"
//       size="sm"
//       className="absolute bottom-16 right-4 rounded-full shadow-lg flex items-center justify-center z-10"
//       onClick={onClick}
//     >
//       <ArrowDown className="h-4 w-4 mr-1" />
//       <span className="text-xs">New messages</span>
//     </Button>
//   );
// };

// export function ChatWindow() {
//   const params = useParams()
//   const router = useRouter()
//   const { toast } = useToast()

//   // Get chatId from URL parameters
//   const chatId = Array.isArray(params?.chatId)
//     ? params.chatId[0]
//     : typeof params?.chatId === 'string'
//       ? params.chatId
//       : null

//   // Log chatId for debugging
//   console.log("ChatWindow chatId:", chatId)

//   if (!chatId) {
//     console.error("No chatId found in URL parameters")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">No chat selected</p>
//         </div>
//       </>
//     )
//   }

//   const { userId, user, isLoading: isUserLoading } = useAuth()
//   const { dataSavingMode } = useDataSaving()
//   const isMobile = typeof window !== "undefined" ? window.innerWidth < 768 : false

//   const [newMessage, setNewMessage] = useState("")
//   const [showEmojiPicker, setShowEmojiPicker] = useState(false)
//   const [showVoiceRecorder, setShowVoiceRecorder] = useState(false)
//   const [showMultiMixEditor, setShowMultiMixEditor] = useState(false)
//   const [multimixStyle, setMultimixStyle] = useState("")
//   const [showGiftDialog, setShowGiftDialog] = useState(false)
//   const [showImageUpload, setShowImageUpload] = useState(false)
//   const [showBackgroundPicker, setShowBackgroundPicker] = useState(false)
//   const [chatBackground, setChatBackground] = useState("none")
//   const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)
//   const [showVideoCall, setShowVideoCall] = useState(false)
//   const [showVoiceCall, setShowVoiceCall] = useState(false)
//   const [hasNewMessages, setHasNewMessages] = useState(false)

//   const scrollAreaRef = useRef<HTMLDivElement>(null);
//   const messagesEndRef = useRef<HTMLDivElement>(null);
//   const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
//   const [newMessageCount, setNewMessageCount] = useState(0);
//   const prevMessagesLength = useRef(0);

//   const { chat, chatId: actualChatId, isLoading: isChatLoading } = useChat(chatId || null)
//   console.log("Chat state:", { chat, actualChatId, isChatLoading })

//   // Use the actualChatId once the chat is loaded
//   // We'll trust that the useChat hook has resolved the correct chat ID
//   const validChatId = !isChatLoading && actualChatId ? actualChatId : null
//   console.log("Using chat ID for messages:", validChatId)

//   const {
//     messages,
//     sendMessage,
//     sendNudge,
//     isLoading: isMessagesLoading,
//   } = useMessages(validChatId, () => {
//     setTimeout(() => {
//       if (scrollAreaRef.current) {
//         scrollToBottom(scrollAreaRef.current)
//       }
//     }, 100)
//   })

//   console.log("Messages state:", { messagesCount: messages?.length, isMessagesLoading })

//   // Add delete message mutation
//   const deleteMessage = useMutation(api.messages.deleteMessage)
//   const { dataMode, recordMessageSent } = useDataMode()

//   // Auto-scroll when new messages arrive or chat changes
//   useEffect(() => {
//     if (messages.length === 0) return;
    
//     const isNewMessage = messages.length > prevMessagesLength.current;
//     prevMessagesLength.current = messages.length;

//     if (isScrolledToBottom) {
//       // If at bottom, scroll smoothly to bottom
//       scrollToBottom(scrollAreaRef.current, 'smooth');
//     } else if (isNewMessage) {
//       // If not at bottom and new messages arrive, increment counter
//       setNewMessageCount(prev => prev + 1);
//     }
//   }, [messages, isScrolledToBottom]);

//   // Scroll to bottom when chat changes
//   useEffect(() => {
//     if (scrollAreaRef.current) {
//       scrollToBottom(scrollAreaRef.current, 'auto');
//       setIsScrolledToBottom(true);
//       setNewMessageCount(0);
//     }
//   }, [chatId]);

//   useEffect(() => {
//     if (chat?.background) {
//       setChatBackground(chat.background)
//     }
//   }, [chat])

//   // Handle scroll events to track scroll position
//   const handleScroll = React.useCallback((event: React.UIEvent<HTMLDivElement>) => {
//     const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
//     const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 50;
    
//     // Only update state if the value actually changes
//     setIsScrolledToBottom(prev => {
//       if (prev !== isAtBottom) {
//         // Reset new message count when scrolling to bottom
//         if (isAtBottom) {
//           setNewMessageCount(0);
//         }
//         return isAtBottom;
//       }
//       return prev;
//     });
    
//     // Explicitly return void to match the expected type
//     return undefined;
//   }, [setIsScrolledToBottom, setNewMessageCount]);

//   const handleSendMessage = async (e: React.FormEvent) => {
//     e.preventDefault()
//     console.log("handleSendMessage called with:", newMessage)

//     // Check if we have all the required data to send a message
//     // Make sure to use validChatId instead of chatId
//     if (!newMessage.trim() || !validChatId || !userId) {
//       console.error("Cannot send message: missing data", {
//         newMessage: newMessage.trim() ? "✓" : "✗",
//         validChatId: validChatId ? "✓" : "✗",
//         chatId,
//         actualChatId,
//         isChatLoading,
//         userId: userId ? "✓" : "✗"
//       })

//       // Show a toast message to the user
//       if (!validChatId) {
//         toast({
//           title: "Cannot send message",
//           description: isChatLoading ? "Chat is still loading..." : "Invalid chat ID",
//           variant: "destructive",
//         })
//       }

//       return
//     }

//     try {
//       console.log("Attempting to send message via sendMessage function")

//       if (showMultiMixEditor) {
//         await sendMessage(newMessage, {
//           isMultimix: true,
//           multimixStyle,
//           dataMode: dataMode || "mobile", // Add the data mode
//         })
//       } else {
//         await sendMessage(newMessage, {
//           dataMode: dataMode || "mobile", // Add the data mode
//         })
//       }

//       console.log("Message sent successfully")
//       setNewMessage("")
//       setShowMultiMixEditor(false)
//       setIsScrolledToBottom(true)
//     } catch (error) {
//       console.error("Error in handleSendMessage:", error)
//       toast({
//         title: "Error",
//         description: error instanceof Error ? error.message : "Failed to send message",
//         variant: "destructive",
//       })
//     }
//   }

//   const handleSendNudge = async () => {
//     if (chatId && userId) {
//       try {
//         await sendNudge()
//         toast({
//           title: "Nudge sent!",
//           description: "You sent a nudge to get their attention",
//         })
//       } catch (error) {
//         toast({
//           title: "Error",
//           description: error instanceof Error ? error.message : "Failed to send nudge",
//           variant: "destructive",
//         })
//       }
//     }
//   }

//   const handleImageSelected = async (imageUrl: string) => {
//     if (!chatId || !userId) return

//     try {
//       // Send the image as a message with the image URL
//       await sendMessage(`![Image](${imageUrl})`, {
//         dataMode: dataMode || "mobile",
//       })

//       toast({
//         title: "Image sent",
//         description: "Your image has been sent",
//       })

//       setIsScrolledToBottom(true)
//     } catch (error) {
//       toast({
//         title: "Error",
//         description: error instanceof Error ? error.message : "Failed to send image",
//         variant: "destructive",
//       })
//     }
//   }

//   const handleEmojiSelect = (emoji: string) => {
//     setNewMessage((prev) => prev + emoji)
//     setShowEmojiPicker(false)
//   }

//   // Add function to handle message deletion
//   const handleDeleteMessage = async (messageId: Id<"messages">) => {
//     if (!userId || !chatId) return

//     try {
//       await deleteMessage({ messageId, userId: userId as Id<"users"> })
//       toast({
//         title: "Message deleted",
//         description: "Your message has been deleted",
//       })
//       setSelectedMessageId(null)
//     } catch (error) {
//       console.error("Error deleting message:", error);
//       toast({
//         title: "Error",
//         description: error instanceof Error ? error.message : "Failed to delete message",
//         variant: "destructive",
//       })
//     }
//   }

//   const handleVoiceRecorded = async (audioBlob: Blob) => {
//     if (!chatId || !userId) return

//     try {
//       // In a real app, you would upload the audio blob to storage
//       // and get a URL back. For this example, we'll just use a placeholder.
//       const voiceNoteUrl = "voice-note-url"
//       const voiceNoteDuration = 10 // seconds

//       await sendMessage("Voice Note", {
//         isVoiceNote: true,
//         voiceNoteUrl,
//         voiceNoteDuration,
//         dataMode: dataMode || "mobile", // Add the data mode
//       })

//       setShowVoiceRecorder(false)
//       setIsScrolledToBottom(true)
//     } catch (error) {
//       toast({
//         title: "Error",
//         description: error instanceof Error ? error.message : "Failed to send voice note",
//         variant: "destructive",
//       })
//     }
//   }

//   console.log("Loading states:", { isUserLoading, isChatLoading, isMessagesLoading })

//   if (isUserLoading) {
//     console.log("Showing loading spinner due to isUserLoading")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">Loading user...</p>
//           <LoadingSpinner />
//         </div>
//       </>
//     )
//   }

//   if (isChatLoading) {
//     console.log("Showing loading spinner due to isChatLoading")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">Loading chat...</p>
//           <LoadingSpinner />
//         </div>
//       </>
//     )
//   }

//   if (isMessagesLoading) {
//     console.log("Showing loading spinner due to isMessagesLoading")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">Loading messages...</p>
//           <LoadingSpinner />
//         </div>
//       </>
//     )
//   }

//   if (!userId) {
//     console.log("No userId found")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">Please sign in to continue</p>
//         </div>
//       </>
//     )
//   }

//   if (!chat) {
//     console.log("No chat found")
//     return (
//       <>
//         {/* <ChatDebug /> */}
//         <div className="flex h-full items-center justify-center">
//           <p className="text-muted-foreground">Chat not found</p>
//         </div>
//       </>
//     )
//   }

//   const otherParticipant = chat.type === "direct" ? chat.participants.find((p) => p && p._id !== userId) : null

//   // Create a map to track message IDs we've already seen to avoid duplicates
//   const seenMessageIds = new Map()

//   return (
//     <div
//       className="flex flex-col h-full bg-background dark:bg-gray-950"
//       style={{
//         background: chatBackground !== "none" ? chatBackground : undefined,
//         backgroundSize: "cover",
//         backgroundPosition: "center",
//         backgroundAttachment: "fixed", // 'fixed' makes the background stationary during scroll
//       }}
//     >
//       {/* <ChatDebug /> */}
//       <div className="flex items-center p-2 bg-background border-b">
//         <Button
//           variant="ghost"
//           size="icon"
//           onClick={() => {
//             if (isMobile) {
//               router.push("/chat/mobile")
//             } else {
//               router.push("/chat")
//             }
//           }}
//           className="h-8 w-8"
//         >
//           <ArrowLeft className="h-4 w-4" />
//         </Button>
//         <Avatar className="h-8 w-8">
//           {chat.type === "direct" ? (
//             <>
//               <AvatarImage src={otherParticipant?.avatar} alt={otherParticipant?.name} />
//               <AvatarFallback>{otherParticipant?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//             </>
//           ) : (
//             <>
//               <AvatarImage
//                 src={`/placeholder.svg?height=40&width=40&text=${chat.name?.substring(0, 2)}`}
//                 alt={chat.name}
//               />
//               <AvatarFallback>{chat.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//             </>
//           )}
//         </Avatar>
//         <div className="flex-1 min-w-0">
//           {/* Uncomment this line to show the participant name */}
//           <h2 className="font-bold truncate">{chat.type === "direct" ? otherParticipant?.name : chat.name}</h2>
//           {chat.type === "direct" && otherParticipant ? (
//             <div className="flex items-center gap-2">
//               <div className="text-xs text-muted-foreground">
//                 {otherParticipant.status === "online" ? "Online" :
//                  otherParticipant.status === "away" ? "Away" : "Offline"}
//               </div>
//             </div>
//           ) : (
//             <p className="text-xs text-muted-foreground truncate">{`${chat.participants.length} members`}</p>
//           )}
//         </div>

//         {chat.type === "direct" && (
//           <div className="flex items-center">
//             <Button
//               variant="ghost"
//               size="icon"
//               className="h-8 w-8"
//               onClick={() => setShowVoiceCall(true)}
//             >
//               <Phone className="h-4 w-4" />
//             </Button>
//             <Button
//               variant="ghost"
//               size="icon"
//               className="h-8 w-8"
//               onClick={() => setShowVideoCall(true)}
//             >
//               <Video className="h-4 w-4" />
//             </Button>
//           </div>
//         )}

//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button variant="ghost" size="icon" className="h-8 w-8 text-primary-foreground hover:bg-primary-foreground/10 hover:text-primary-foreground">
//               <MoreVertical className="h-4 w-4" />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             {chat.type === "direct" && (
//               <>
//                 <DropdownMenuItem onClick={handleSendNudge}>
//                   <Zap className="h-4 w-4 mr-2" />
//                   Send Nudge
//                 </DropdownMenuItem>
//                 <DropdownMenuItem onClick={() => setShowGiftDialog(true)}>
//                   <Gift className="h-4 w-4 mr-2" />
//                   Gift Moola
//                 </DropdownMenuItem>
//               </>
//             )}
//             <DropdownMenuItem onClick={() => setShowMultiMixEditor(!showMultiMixEditor)}>
//               <Type className="h-4 w-4 mr-2" />
//               {showMultiMixEditor ? "Cancel MultiMix" : "Create MultiMix"}
//             </DropdownMenuItem>
//             <DropdownMenuItem onClick={() => setShowImageUpload(true)}>
//               <ImageIcon className="h-4 w-4 mr-2" />
//               Send Image
//             </DropdownMenuItem>
//             <DropdownMenuItem onClick={() => setShowBackgroundPicker(true)}>
//               <Palette className="h-4 w-4 mr-2" />
//               Change Background
//             </DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       </div>

//       <div className="flex-1 relative overflow-hidden">
//         <ScrollArea
//           className="h-full bg-background"
//           onScroll={handleScroll} 
//           ref={scrollAreaRef}
//         >
//           <div className="p-3 space-y-3 pb-4">
//             {/* Scroll to bottom button */}
//             {!isScrolledToBottom && newMessageCount > 0 && (
//               <div className="fixed bottom-24 right-4 z-50">
//                 <Button
//                   variant="secondary"
//                   size="sm"
//                   className="rounded-full shadow-lg flex items-center animate-bounce"
//                   onClick={() => scrollToBottom(scrollAreaRef.current, 'smooth')}
//                 >
//                   <ArrowDown className="h-4 w-4 mr-1" />
//                   <span className="text-xs">
//                     {newMessageCount} new message{newMessageCount !== 1 ? 's' : ''}
//                   </span>
//                 </Button>
//               </div>
//             )}

//             {messages.length === 0 ? (
//               <p className="text-center text-muted-foreground py-4">No messages yet. Start the conversation!</p>
//             ) : (
//               messages
//                 .map((message, index) => {
//                   // Create a truly unique key using multiple properties
//                   const messageKey = `msg-${message._id || ""}-${message.senderId || ""}-${message.timestamp}`

//                   // Skip this message if we've already seen this ID to avoid duplicates
//                   const baseId = `${message._id || index}-${message.timestamp}-${message.senderId}`
//                   const contentHash = message.content ? message.content.substring(0, 10).replace(/\s+/g, "") : ""
//                   const uniqueKey = `msg-${baseId}-${message.timestamp}-${message.senderId}-${contentHash}-${index}`

//                   // Skip this message if we've already seen this ID to avoid duplicates
//                   if (seenMessageIds.has(baseId)) {
//                     return null
//                   }

//                   // Mark this ID as seen
//                   seenMessageIds.set(baseId, true)

//                   const isCurrentUser = message.senderId === userId
//                   const time = format(new Date(message.timestamp), "h:mm a")
//                   const isNudge = message.isNudge
//                   const isVoiceNote = message.isVoiceNote
//                   const isMultimix = message.isMultimix
//                   const multimixStyle = message.multimixStyle
//                   const isDeleted = message.isDeleted
//                   const isSystemMessage = message.isSystemMessage

//                   if (isSystemMessage) {
//                     return (
//                       <div key={messageKey} className="flex justify-center my-4">
//                         <div className="bg-gray-800 px-4 py-2 rounded-lg text-sm text-gray-300">{message.content}</div>
//                       </div>
//                     )
//                   }

//                   if (isNudge) {
//                     return (
//                       <div key={messageKey} className="flex justify-center my-4">
//                         <div className="bg-gray-800 px-4 py-2 rounded-lg text-sm animate-bounce">
//                           <span className="font-medium">{message.sender?.name}</span> sent a nudge
//                         </div>
//                       </div>
//                     )
//                   }

//                   if (isVoiceNote && !dataSavingMode) {
//                     return (
//                       <div
//                         key={messageKey}
//                         className={`flex items-start gap-2 ${isCurrentUser ? "flex-row-reverse" : ""}`}
//                       >
//                         {!isCurrentUser && (
//                           <Avatar className="h-8 w-8 mt-1">
//                             <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
//                             <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//                           </Avatar>
//                         )}
//                         <div className="max-w-[75%]">
//                           <div
//                             className={`rounded-2xl px-3 py-2 ${
//                               isCurrentUser ? "bg-primary text-primary-foreground" : "bg-gray-800 text-white"
//                             }`}
//                           >
//                             <div className="flex items-center gap-2">
//                               <Mic className="h-4 w-4" />
//                               <span>Voice Note</span>
//                               <span className="text-xs">{message.voiceNoteDuration}s</span>
//                             </div>
//                             <audio controls className="mt-2 w-full h-8">
//                               <source src={message.voiceNoteUrl} type="audio/webm" />
//                               Your browser does not support the audio element.
//                             </audio>
//                           </div>
//                           <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
//                             <span className="text-muted-foreground">{time}</span>
//                             {isCurrentUser && (
//                               <div className="flex text-primary">
//                                 <Check className="h-3 w-3" />
//                                 <Check className="h-3 w-3 -ml-1" />
//                               </div>
//                             )}
//                           </div>
//                         </div>
//                       </div>
//                     )
//                   }

//                   // Check if the message contains an image (markdown format)
//                   const imageMatch = message.content.match(/!\[Image\]\((.+?)\)/)
//                   const isImage = !!imageMatch

//                   if (isImage && imageMatch) {
//                     const imageUrl = imageMatch[1]
//                     return (
//                       <div
//                         key={uniqueKey}
//                         className={`flex items-start gap-2 group ${isCurrentUser ? "flex-row-reverse" : ""}`}
//                       >
//                         {!isCurrentUser && (
//                           <Avatar className="h-8 w-8 mt-1">
//                             <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
//                             <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//                           </Avatar>
//                         )}
//                         <div className="max-w-[75%]">
//                           <div
//                             className={`rounded-2xl p-1 overflow-hidden ${
//                               isCurrentUser ? "bg-primary" : "bg-gray-800"
//                             }`}
//                           >
//                             <img
//                               src={imageUrl}
//                               alt="Image"
//                               className="max-w-full rounded-xl max-h-[300px] object-contain"
//                               onClick={() => window.open(imageUrl, "_blank")}
//                               style={{ cursor: "pointer" }}
//                             />
//                           </div>
//                           <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
//                             <span className="text-muted-foreground">{time}</span>
//                             {isCurrentUser && (
//                               <div className="flex text-primary">
//                                 <Check className="h-3 w-3" />
//                                 <Check className="h-3 w-3 -ml-1" />
//                               </div>
//                             )}
//                           </div>
//                         </div>
//                       </div>
//                     )
//                   }

//                   if (isMultimix) {
//                     let style = {}
//                     try {
//                       style = message.multimixStyle ? JSON.parse(message.multimixStyle) : {}
//                     } catch (e) {
//                       console.error("Error parsing multimix style:", e)
//                     }

//                     return (
//                       <div
//                         key={messageKey}
//                         className={`flex items-start gap-2 group ${isCurrentUser ? "flex-row-reverse" : ""}`}
//                       >
//                         {!isCurrentUser && (
//                           <Avatar className="h-8 w-8 mt-1">
//                             <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
//                             <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//                           </Avatar>
//                         )}
//                         <div className="max-w-[75%] relative">
//                           <div
//                             className={`rounded-2xl px-3 py-2 ${
//                               isCurrentUser ? "bg-primary text-primary-foreground" : "bg-gray-800 text-white"
//                             }`}
//                           >
//                             <p style={style}>{isDeleted ? "This message has been deleted" : message.content}</p>
//                           </div>
//                           <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
//                             <span className="text-muted-foreground">{time}</span>
//                             {isCurrentUser && (
//                               <div className="flex text-primary">
//                                 <Check className="h-3 w-3" />
//                                 <Check className="h-3 w-3 -ml-1" />
//                               </div>
//                             )}
//                           </div>
//                           {isCurrentUser && !isDeleted && (
//                             <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100">
//                               <DropdownMenu>
//                                 <DropdownMenuTrigger asChild>
//                                   <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full">
//                                     <MoreVertical className="h-3 w-3" />
//                                   </Button>
//                                 </DropdownMenuTrigger>
//                                 <DropdownMenuContent align="end">
//                                   <DropdownMenuItem onClick={() => handleDeleteMessage(message._id as Id<"messages">)}>
//                                     <Trash2 className="h-4 w-4 mr-2" />
//                                     Delete
//                                   </DropdownMenuItem>
//                                   <DropdownMenuItem>
//                                     <Copy className="h-4 w-4 mr-2" />
//                                     Copy
//                                   </DropdownMenuItem>
//                                 </DropdownMenuContent>
//                               </DropdownMenu>
//                             </div>
//                           )}
//                         </div>
//                       </div>
//                     )
//                   }

//                   return (
//                     <div
//                       key={messageKey}
//                       className={`flex items-start gap-2 group ${isCurrentUser ? "flex-row-reverse" : ""}`}
//                     >
//                       {!isCurrentUser && (
//                         <Avatar className="h-8 w-8 mt-1">
//                           <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
//                           <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
//                         </Avatar>
//                       )}
//                       <div className="max-w-[75%] relative">
//                         <div
//                           className={`rounded-2xl px-3 py-2 ${
//                             isCurrentUser ? "bg-primary text-primary-foreground" : "bg-gray-800 text-white"
//                           }`}
//                         >
//                           <p>{isDeleted ? "This message has been deleted" : message.content}</p>
//                         </div>
//                         <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
//                           <span className="text-muted-foreground">{time}</span>
//                           {isCurrentUser && (
//                             <div className="flex text-primary">
//                               <Check className="h-3 w-3" />
//                               <Check className="h-3 w-3 -ml-1" />
//                             </div>
//                           )}
//                         </div>
//                         {isCurrentUser && !isDeleted && (
//                           <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100">
//                             <DropdownMenu>
//                               <DropdownMenuTrigger asChild>
//                                 <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-full">
//                                   <MoreVertical className="h-3 w-3" />
//                                 </Button>
//                               </DropdownMenuTrigger>
//                               <DropdownMenuContent align="end">
//                                 <DropdownMenuItem onClick={() => handleDeleteMessage(message._id as Id<"messages">)}>
//                                   <Trash2 className="h-4 w-4 mr-2" />
//                                   Delete
//                                 </DropdownMenuItem>
//                                 <DropdownMenuItem>
//                                   <Copy className="h-4 w-4 mr-2" />
//                                   Copy
//                                 </DropdownMenuItem>
//                               </DropdownMenuContent>
//                             </DropdownMenu>
//                           </div>
//                         )}
//                       </div>
//                     </div>
//                   )
//                 })
//                 .filter(Boolean) // Filter out any null values (skipped duplicates)
//             )}
//             <div ref={messagesEndRef} className="h-0" />
//           </div>
//         </ScrollArea>
        
//         {/* Scroll to bottom button for mobile */}
//         {!isScrolledToBottom && (
//           <Button
//             variant="secondary"
//             size="icon"
//             className="fixed bottom-24 right-4 rounded-full shadow-lg md:hidden z-50"
//             onClick={() => scrollToBottom(scrollAreaRef.current, 'smooth')}
//           >
//             <ArrowDown className="h-4 w-4" />
//           </Button>
//         )}

//         {/* Add the ScrollToBottomButton component */}
//         <ScrollToBottomButton
//           visible={hasNewMessages && !isScrolledToBottom}
//           onClick={() => {
//             if (scrollAreaRef.current) {
//               scrollToBottom(scrollAreaRef.current)
//               setHasNewMessages(false)
//               setIsScrolledToBottom(true)
//             }
//           }}
//         />
//       </div>

//       {showVoiceRecorder && (
//         <VoiceRecorder onVoiceRecorded={handleVoiceRecorded} onCancel={() => setShowVoiceRecorder(false)} />
//       )}

//       {showMultiMixEditor && (
//         <MultiMixEditor value={newMessage} onChange={setNewMessage} onStyleChange={setMultimixStyle} />
//       )}

//       <ImageUpload
//         open={showImageUpload}
//         onOpenChange={setShowImageUpload}
//         onImageSelected={handleImageSelected}
//       />

//       {chatId && showBackgroundPicker && (
//         <ChatBackgroundPicker
//           chatId={chatId}
//           currentBackground={chatBackground}
//           onBackgroundChange={setChatBackground}
//         />
//       )}

//       <div className="p-2 border-t bg-background dark:bg-gray-900">
//         <form onSubmit={handleSendMessage} className="flex gap-2 items-center">
//           <div className="relative flex-1">
//             <Input
//               value={newMessage}
//               onChange={(e) => setNewMessage(e.target.value)}
//               placeholder={isChatLoading ? "Loading chat..." : !validChatId ? "Cannot send messages" : "Type a message..."}
//               className="pr-10 bg-white rounded-full border-gray-300 text-black"
//               disabled={showVoiceRecorder || isChatLoading || !validChatId}
//             />
//             <Button
//               type="button"
//               variant="ghost"
//               size="icon"
//               className="absolute right-1 top-1/2 -translate-y-1/2"
//               onClick={() => setShowEmojiPicker(!showEmojiPicker)}
//               disabled={showVoiceRecorder}
//             >
//               <span className="text-lg">😊</span>
//             </Button>
//             {showEmojiPicker && (
//               <div className="absolute bottom-full right-0 mb-2 z-10">
//                 <EmojiPicker onEmojiSelect={handleEmojiSelect} />
//               </div>
//             )}
//           </div>
//           {/* Game launcher - hidden on mobile, shown on desktop */}
//           <div className="hidden md:block">
//             <GameDialog chatId={chatId as Id<"chats">}>
//               <Button 
//                 type="button"
//                 variant="ghost"
//                 size="icon"
//                 className="text-muted-foreground"
//               >
//                 <svg
//                   xmlns="http://www.w3.org/2000/svg"
//                   width="24"
//                   height="24"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   stroke="currentColor"
//                   strokeWidth="2"
//                   strokeLinecap="round"
//                   strokeLinejoin="round"
//                   className="h-5 w-5"
//                 >
//                   <line x1="6" y1="11" x2="6" y2="11.01" />
//                   <line x1="8" y1="9" x2="8" y2="9.01" />
//                   <line x1="10" y1="7" x2="10" y2="7.01" />
//                   <line x1="12" y1="5" x2="12" y2="5.01" />
//                   <line x1="14" y1="7" x2="14" y2="7.01" />
//                   <line x1="16" y1="9" x2="16" y2="9.01" />
//                   <line x1="18" y1="11" x2="18" y2="11.01" />
//                   <path d="M4 15a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1z" />
//                 </svg>
//                 <span className="sr-only">Games</span>
//               </Button>
//             </GameDialog>
//           </div>
//           {!showVoiceRecorder ? (
//             <>
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="icon"
//                 onClick={() => setShowImageUpload(true)}
//                 disabled={showMultiMixEditor || showVoiceRecorder}
//                 className="text-muted-foreground"
//               >
//                 <ImageIcon className="h-5 w-5" />
//               </Button>
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="icon"
//                 onClick={() => setShowVoiceRecorder(true)}
//                 disabled={showMultiMixEditor}
//                 className="text-muted-foreground"
//               >
//                 <Mic className="h-5 w-5" />
//               </Button>
//               <Button
//                 type="submit"
//                 size="icon"
//                 disabled={!newMessage.trim() || isChatLoading || !validChatId}
//                 className="rounded-full bg-primary h-10 w-10 text-primary-foreground hover:bg-primary/90"
//                 title={isChatLoading ? "Chat is loading..." : !validChatId ? "Invalid chat ID" : "Send message"}
//               >
//                 <Send className="h-5 w-5" />
//               </Button>
//             </>
//           ) : (
//             <Button type="button" variant="ghost" size="sm" onClick={() => setShowVoiceRecorder(false)}>
//               Cancel
//             </Button>
//           )}
//         </form>
//       </div>
//       {chat.type === "direct" && otherParticipant && (
//         <>
//           <GiftMoolaDialog
//             open={showGiftDialog}
//             onOpenChange={setShowGiftDialog}
//             recipientId={otherParticipant._id}
//             recipientName={otherParticipant.name}
//           />

//           <VideoCall
//             open={showVideoCall}
//             onOpenChange={setShowVideoCall}
//             recipientId={otherParticipant._id}
//             recipientName={otherParticipant.name}
//             recipientAvatar={otherParticipant.avatar}
//           />

//           <VoiceCall
//             open={showVoiceCall}
//             onOpenChange={setShowVoiceCall}
//             recipientId={otherParticipant._id}
//             recipientName={otherParticipant.name}
//             recipientAvatar={otherParticipant.avatar}
//           />
//         </>
//       )}
//     </div>
//   )
// }


