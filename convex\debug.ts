"use client"

import { v } from "convex/values"
import { query, mutation } from "./_generated/server"

export const getChatDetails = query({
  args: { chatId: v.id("chats") },
  handler: async (ctx, args) => {
    try {
      const chat = await ctx.db.get(args.chatId)
      if (!chat) return { error: "Chat not found" }

      const participants = await ctx.db
        .query("chatParticipants")
        .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
        .collect()

      const participantDetails = await Promise.all(
        participants.map(async (p) => {
          const user = await ctx.db.get(p.userId)
          return {
            participantId: p._id,
            userId: p.userId,
            userName: user?.name || "Unknown",
            role: p.role,
            joinedAt: p.joinedAt,
            unreadCount: p.unreadCount,
          }
        }),
      )

      return {
        chat,
        participants: participantDetails,
        totalParticipants: participants.length,
      }
    } catch (error) {
      return { error: String(error) }
    }
  },
})

// Add a function to clear all direct chats for testing
export const clearAllDirectChats = mutation({
  handler: async (ctx) => {
    try {
      // Get all direct chats
      const directChats = await ctx.db
        .query("chats")
        .filter((q) => q.eq(q.field("type"), "direct"))
        .collect()

      // Delete all chat participants first
      for (const chat of directChats) {
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", chat._id))
          .collect()

        for (const participant of participants) {
          await ctx.db.delete(participant._id)
        }

        // Delete the chat
        await ctx.db.delete(chat._id)
      }

      return { success: true, deletedCount: directChats.length }
    } catch (error) {
      return { error: String(error) }
    }
  },
})

