"use client"

import { useEffect, useState } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { Id } from "@/convex/_generated/dataModel"

type Status = "online" | "offline" | "away"

export function useUserStatus(userId: Id<"users">) {
  const { updateUserStatus } = useAuth()
  const [isUpdating, setIsUpdating] = useState(false)
  const updateStatus = useMutation(api.users.updateUserStatus)
  
  // Get real-time status updates for the user
  const userStatus = useQuery(api.users.getUserStatus, { userId })

  // Set up interval to update lastSeen for online users
  useEffect(() => {
    if (!userId) return

    const interval = setInterval(async () => {
      try {
        await updateStatus({
          userId,
          status: "online"
        })
      } catch (error) {
        console.error("Error updating user status:", error)
      }
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [userId, updateStatus])

  // Update status when component mounts/unmounts
  useEffect(() => {
    if (!userId) return

    // Set user as online when component mounts
    const setOnline = async () => {
      try {
        setIsUpdating(true)
        await updateUserStatus("online")
      } catch (error) {
        console.error("Error setting user as online:", error)
      } finally {
        setIsUpdating(false)
      }
    }

    // Set user as offline when component unmounts
    const setOffline = async () => {
      try {
        setIsUpdating(true)
        await updateUserStatus("offline")
      } catch (error) {
        console.error("Error setting user as offline:", error)
      } finally {
        setIsUpdating(false)
      }
    }

    // Set initial online status
    setOnline()

    // Set up beforeunload handler
    const handleBeforeUnload = () => {
      // Use sendBeacon for more reliable offline status updates
      if (navigator.sendBeacon) {
        const data = new FormData()
        data.append('userId', userId)
        data.append('status', 'offline')
        navigator.sendBeacon('/api/update-status', data)
      }
    }

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handleBeforeUnload)
    
    return () => {
      // Clean up
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handleBeforeUnload)
      setOffline()
    }
  }, [userId, updateUserStatus])

  // Get the status from the real-time query
  const status = (userStatus?.status as Status) || "offline"

  return {
    status,
    isUpdating,
    lastSeen: userStatus?.lastSeen,
    updateStatus: (newStatus: Status) => updateUserStatus(newStatus)
  }
}