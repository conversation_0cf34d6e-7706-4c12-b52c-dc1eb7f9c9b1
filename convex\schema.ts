import { defineSchema, defineTable } from "convex/server"
import { v } from "convex/values"
import { chatCategoryValidator, validateChatCategory, CHAT_CATEGORIES } from "./lib/chatUtils"

// Define validators outside the schema
const directChatValidator = v.object({
  type: v.literal("direct"),
  name: v.optional(v.string()),
  description: v.optional(v.string()),
  background: v.optional(v.string()),
  isPublic: v.boolean(),
  creatorId: v.id("users"),
  lastMessageId: v.optional(v.id("messages")),
  createdAt: v.number(),
  updatedAt: v.number(),
  participants: v.array(v.id("users")),
  isPrivate: v.optional(v.boolean()),
  thumbnailUrl: v.optional(v.string()),
});

const groupChatValidator = v.object({
  type: v.union(v.literal("group"), v.literal("chatroom")),
  name: v.string(),
  description: v.optional(v.string()),
  background: v.optional(v.string()),
  isPublic: v.boolean(),
  creatorId: v.id("users"),
  lastMessageId: v.optional(v.id("messages")),
  createdAt: v.number(),
  updatedAt: v.number(),
  category: chatCategoryValidator,
  isAnonymous: v.optional(v.boolean()),
  moolaPerMessage: v.optional(v.number()),
  location: v.optional(v.string()),
  tags: v.optional(v.array(v.string())),
  isPrivate: v.optional(v.boolean()),
  thumbnailUrl: v.optional(v.string()),
  monetizationSettings: v.optional(
    v.object({
      enableTipping: v.boolean(),
      enableSubscription: v.boolean(),
      subscriptionAmount: v.number(),
    }),
  ),
  participants: v.array(v.id("users")),
});

const chatValidator = v.union(directChatValidator, groupChatValidator);

export default defineSchema({
  // --- Existing Tables (Keep As Is) ---
  users: defineTable({
    name: v.string(),
    email: v.optional(v.string()),
    avatar: v.string(),
    status: v.optional(v.union(v.literal("online"), v.literal("offline"), v.literal("away"))),
    lastSeen: v.optional(v.number()),
    lastActive: v.optional(v.number()), 
    statusMessage: v.optional(v.string()),
    mood: v.optional(
      v.union(
        v.literal("happy"),
        v.literal("sad"),
        v.literal("excited"),
        v.literal("bored"),
        v.literal("busy"),
        v.literal("relaxed"),
      ),
    ),
    theme: v.optional(v.string()),
    moola: v.number(),
    clerkId: v.string(),
    language: v.optional(v.string()),
    dataSavingMode: v.optional(v.boolean()),
    
    // User settings
    settings: v.optional(
      v.object({
        hideOfflineContacts: v.optional(v.boolean()),
        theme: v.optional(v.union(v.literal("light"), v.literal("dark"), v.literal("system"))),
        notifications: v.optional(v.boolean()),
        dataSavingMode: v.optional(v.boolean()),
      })
    ),
    
    // Moola economy fields
    lastLoginReward: v.optional(v.number()),
    loginStreak: v.optional(v.number()),
    referredBy: v.optional(v.id("users")),
    referralCount: v.optional(v.number()),
    achievements: v.optional(v.array(v.string())),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_status", ["status"]),

  chats: defineTable(chatValidator)
    .index("by_participants", ["participants"]) // Index for finding direct chats
    .index("by_creatorId", ["creatorId"])
    .index("by_type_and_category", ["type", "category"])
    .index("by_name", ["name"]),

  chatParticipants: defineTable({
    chatId: v.id("chats"),
    userId: v.id("users"),
    unreadCount: v.number(),
    joinedAt: v.float64(),
    role: v.union(v.literal("admin"), v.literal("member")),
    lastReadMessageId: v.optional(v.id("messages")),
  })
    .index("by_chat", ["chatId"])
    .index("by_user", ["userId"])
    .index("by_chat_and_user", ["chatId", "userId"]),

  messages: defineTable({
    chatId: v.id("chats"),
    senderId: v.union(v.id("users"), v.null()), // null for system messages
    content: v.string(),
    timestamp: v.number(),
    isNudge: v.optional(v.boolean()),
    isVoiceNote: v.optional(v.boolean()),
    voiceNoteUrl: v.optional(v.string()), // Consider v.id("_storage") if using Convex storage
    voiceNoteDuration: v.optional(v.number()),
    isMultimix: v.optional(v.boolean()),
    multimixStyle: v.optional(v.string()),
    isDeleted: v.optional(v.boolean()),
    isSystemMessage: v.optional(v.boolean()),
    isRead: v.boolean(),
    replyTo: v.optional(v.id("messages")),
    reactions: v.array(
      v.object({
        userId: v.id("users"),
        emoji: v.string(),
    type: v.optional(v.union(
      v.literal("text"),
      v.literal("like"), 
      v.literal("dislike"))),
      }),
    ),
    moolaCost: v.optional(v.number()),
    dataModeUsed: v.optional(v.union(v.literal("mobile"), v.literal("moola"), v.literal("emergency"))),
    // Added field for listing reference in contact messages
    listingRefId: v.optional(v.id("listings")),
  }).index("by_chat", ["chatId"])
  .index("by_chat_and_timestamp", ["chatId", "timestamp"]),

  moolaTransactions: defineTable({
    userId: v.id("users"),
    amount: v.number(),
    type: v.union(
      v.literal("purchase"),
      v.literal("message_cost"),
      v.literal("bonus"),
      v.literal("refund"),
      v.literal("gift_sent"),
      v.literal("gift_received"),
      v.literal("tradepost_purchase"),
      v.literal("tradepost_fee"),
      v.literal("tradepost_sale"),
      v.literal("data_usage"),
      v.literal("emergency_grant"),
      v.literal("daily_reward"),
      v.literal("referral_reward"),
      v.literal("referral_bonus"),
      v.literal("achievement"),
      v.literal("content_creation")

    ),
    description: v.string(),
    relatedUserId: v.optional(v.id("users")),
    recipientId: v.optional(v.id("users")), // For gifts (alternative to relatedUserId)
    relatedListingId: v.optional(v.id("listings")), // Link transaction to a listing if applicable
    paymentId: v.optional(v.string()), // For purchases
    timestamp: v.number(),
  }).index("by_user", ["userId"]),

    // New tables for tracking data usage
    userDataUsage: defineTable({
      userId: v.id("users"),
      date: v.number(), // Timestamp for the day (midnight)
      kilobytes: v.number(),
      createdAt: v.number(),
      updatedAt: v.number(),
    })
      .index("by_user", ["userId"])
      .index("by_user_and_date", ["userId", "date"]),

    userMessageStats: defineTable({
      userId: v.id("users"),
      date: v.number(), // Timestamp for the day (midnight)
      count: v.number(),
      createdAt: v.number(),
      updatedAt: v.number(),
    })
      .index("by_user", ["userId"])
      .index("by_user_and_date", ["userId", "date"]),

    emergencyMoola: defineTable({
      userId: v.id("users"),
      messagesUsed: v.number(),
      activated: v.number(), // Timestamp when emergency Moola was first activated
      lastUsed: v.number(), // Timestamp when emergency Moola was last used
    }).index("by_user", ["userId"]),


  chatBackgrounds: defineTable({
    userId: v.id("users"),
    chatId: v.id("chats"),
    backgroundUrl: v.string(), // Or v.id("_storage")
    isActive: v.boolean(),
    createdAt: v.number(),
  }).index("by_user_and_chat", ["userId", "chatId"]),

  contacts: defineTable({
    userId: v.id("users"),
    contactId: v.id("users"),
    nickname: v.optional(v.string()),
    isFavorite: v.boolean(),
    isBlocked: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_and_contact", ["userId", "contactId"]),

  emoticons: defineTable({
    name: v.string(),
    code: v.string(),
    imageUrl: v.string(), // Or v.id("_storage")
    category: v.string(),
    isPremium: v.boolean(),
    moolaCost: v.optional(v.number()),
    createdAt: v.number(),
  }).index("by_category", ["category"]),

  userEmoticons: defineTable({
    userId: v.id("users"),
    emoticonId: v.id("emoticons"),
    purchasedAt: v.number(),
  }).index("by_user", ["userId"]),

  nudges: defineTable({
    userId: v.id("users"),
    targetUserId: v.id("users"),
    chatId: v.id("chats"),
    timestamp: v.number(),
  }).index("by_user", ["userId"]),

  localNews: defineTable({
    title: v.string(),
    content: v.string(),
    authorId: v.id("users"),
    isSystem: v.boolean(),
    isNews: v.boolean(),
    description: v.string(),
    url: v.optional(v.string()),
    userId: v.id("users"), // User who submitted the news
    userName: v.string(), // Name of the user who submitted
    userProfileImage: v.optional(v.string()), // Profile image of the user
    
    // `_creationTime` is automatically indexed by Convex for sorting by recency
  }),


  // ===========================================
  // ===      NEW TRADEPOST TABLE ADDED      ===
  // ===========================================
  listings: defineTable({
    sellerId: v.id("users"),
    title: v.string(),
    description: v.string(),
    // Using string for price allows "R 150", "Trade", "Free", etc.
    price: v.string(),
    // Optional: Explicit currency/type if needed for filtering
    // priceType: v.optional(v.union(v.literal("ZAR"), v.literal("Moola"), v.literal("Trade"), v.literal("Free"))),
    category: v.union( // Add more relevant categories
      v.literal("electronics"),
      v.literal("fashion"),
      v.literal("home_garden"),
      v.literal("vehicles"),
      v.literal("property"),
      v.literal("hobbies_toys"),
      v.literal("books_music_games"),
      v.literal("services"),
      v.literal("jobs"),
      v.literal("pets"),
      v.literal("other"),
    ),
    condition: v.optional(v.union( // Item condition
        v.literal("new"),
        v.literal("like_new"),
        v.literal("good"),
        v.literal("fair"),
        v.literal("poor"),
    )),
    location: v.optional(v.string()), // e.g., "Cape Town, WC"
    // Store Convex storage IDs for images
    imageStorageIds: v.optional(v.array(v.id("_storage"))),
    tags: v.optional(v.array(v.string())), // For searchability
    status: v.union( // Status of the listing
        v.literal("active"),    // Available for sale/trade
        v.literal("sold"),      // Item has been sold/traded
        v.literal("inactive"),  // Temporarily hidden by seller
        v.literal("expired"),   // Listing duration expired (if applicable)
        v.literal("removed"),   // Removed by admin/moderator
    ),
    viewCount: v.optional(v.number()), // Optional: track views
    createdAt: v.number(),
    updatedAt: v.number(),
    // Optional: Add expiry date if listings should expire
    // expiresAt: v.optional(v.number()),
  })
  .index("by_sellerId", ["sellerId"])        // Find listings by seller
  .index("by_category", ["category"])      // Filter by category
  .index("by_status", ["status"])          // Find active/sold listings
  .index("by_seller_and_status", ["sellerId", "status"]), // Find seller's active listings

  // Tradepost favorites
  tradepostFavorites: defineTable({
    userId: v.id("users"),
    listingId: v.id("listings"),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  // Note: For text search on title/description/tags, you'd configure
  // a Convex Search Index separately in your convex/ directory.

  // Optional: Table for Watchlist/Favorites
  userWatchlist: defineTable({
    userId: v.id("users"),
    listingId: v.id("listings"),
    addedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_listing", ["listingId"])
  .index("by_user_and_listing", ["userId", "listingId"]),

  // Game tables
  games: defineTable({
    type: v.union(
      v.literal("tictactoe"),
      v.literal("rps"),
      v.literal("dice"),
      v.literal("numberguess")
    ),
    status: v.union(
      v.literal("waiting"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("abandoned")
    ),
    players: v.object({
      x: v.union(v.id("users"), v.null()),
      o: v.union(v.id("users"), v.null()),
      currentPlayer: v.union(v.literal("x"), v.literal("o")),
    }),
    chatId: v.id("chats"),
    // Game-specific data (stored as JSON)
    gameData: v.any(),
    winner: v.optional(v.union(v.id("users"), v.literal("draw"))),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_chat", ["chatId"])
    .index("by_player", ["players.x", "players.o"])
    .index("by_status", ["status"]),

  gameInvites: defineTable({
    gameType: v.union(
      v.literal("tictactoe"),
      v.literal("rps"),
      v.literal("dice"),
      v.literal("numberguess")
    ),
    fromUser: v.id("users"),
    toUser: v.id("users"),
    chatId: v.id("chats"),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("declined")
    ),
    expiresAt: v.number(),
  })
    .index("by_user", ["toUser"])
    .index("by_chat", ["chatId"])
    .index("by_status", ["status"]),

  // Notifications table
  notifications: defineTable({
    userId: v.id("users"),
    type: v.string(), // message, friend_request, system, etc.
    title: v.string(),
    message: v.string(),
    linkUrl: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    sourceId: v.optional(v.string()), // ID of the related entity (message, user, etc.)
    sourceType: v.optional(v.string()), // Type of the related entity
    isRead: v.boolean(),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  // No duplicate table needed
})