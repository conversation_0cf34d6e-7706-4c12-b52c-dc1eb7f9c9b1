import { defineSchema, defineTable } from "convex/server"
import { v } from "convex/values"

export default defineSchema({
  // --- Existing Tables (Keep As Is) ---
  users: defineTable({
    name: v.string(),
    email: v.optional(v.string()),
    avatar: v.string(),
    status: v.union(v.literal("online"), v.literal("offline"), v.literal("away")),
    lastSeen: v.optional(v.number()),
    statusMessage: v.optional(v.string()),
    mood: v.optional(
      v.union(
        v.literal("happy"),
        v.literal("sad"),
        v.literal("excited"),
        v.literal("bored"),
        v.literal("busy"),
        v.literal("relaxed"),
      ),
    ),
    theme: v.optional(v.string()),
    moola: v.number(),
    clerkId: v.string(),
    language: v.optional(v.string()),
    dataSavingMode: v.optional(v.boolean()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_status", ["status"]),

  chats: defineTable({
    type: v.union(v.literal("direct"), v.literal("group"), v.literal("chatroom")),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    background: v.optional(v.string()),
    isPublic: v.boolean(),
    creatorId: v.id("users"),
    lastMessageId: v.optional(v.id("messages")),
    createdAt: v.number(),
    updatedAt: v.number(),
    // For chatrooms
    category: v.optional(
      v.union(
        v.literal("flirt"),
        v.literal("teen"),
        v.literal("grown-up"),
        v.literal("kingdom"),
        v.literal("topical"),
        v.literal("geographical"),
      ),
    ),
    moolaPerMessage: v.optional(v.number()),
    location: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    isPrivate: v.optional(v.boolean()),
    thumbnailUrl: v.optional(v.string()),
    // Monetization settings
    monetizationSettings: v.optional(
      v.object({
        enableTipping: v.boolean(),
        enableSubscription: v.boolean(),
        subscriptionAmount: v.number(),
      }),
    ),
    // For direct chats
    participants: v.array(v.id("users")), // Required array of participant IDs
  })
    .index("by_participants", ["participants"]) // Index for finding direct chats
    .index("by_creator", ["creatorId"])
    .index("by_type", ["type"]),

  chatParticipants: defineTable({
    chatId: v.id("chats"),
    userId: v.id("users"),
    unreadCount: v.number(),
    joinedAt: v.float64(),
    role: v.union(v.literal("admin"), v.literal("member")),
    lastReadMessageId: v.optional(v.id("messages")),
  })
    .index("by_chat", ["chatId"])
    .index("by_user", ["userId"])
    .index("by_chat_and_user", ["chatId", "userId"]),

  messages: defineTable({
    chatId: v.id("chats"),
    senderId: v.union(v.id("users"), v.null()), // null for system messages
    content: v.string(),
    timestamp: v.number(),
    isNudge: v.optional(v.boolean()),
    isVoiceNote: v.optional(v.boolean()),
    voiceNoteUrl: v.optional(v.string()), // Consider v.id("_storage") if using Convex storage
    voiceNoteDuration: v.optional(v.number()),
    isMultimix: v.optional(v.boolean()),
    multimixStyle: v.optional(v.string()),
    isDeleted: v.optional(v.boolean()),
    isSystemMessage: v.optional(v.boolean()),
    isRead: v.boolean(),
    replyTo: v.optional(v.id("messages")),
    reactions: v.array(
      v.object({
        userId: v.id("users"),
        emoji: v.string(),
      }),
    ),
    moolaCost: v.optional(v.number()),
    dataModeUsed: v.optional(v.union(v.literal("mobile"), v.literal("moola"), v.literal("emergency"))),
    // Added field for listing reference in contact messages
    listingRefId: v.optional(v.id("listings")),
  }).index("by_chat", ["chatId"]),

  moolaTransactions: defineTable({
    userId: v.id("users"),
    amount: v.number(),
    type: v.union(
      v.literal("purchase"),
      v.literal("message_cost"),
      v.literal("bonus"),
      v.literal("refund"),
      v.literal("gift_sent"),
      v.literal("gift_received"),
      v.literal("tradepost_purchase"), // Optional: If direct Moola purchase is added later
      v.literal("tradepost_fee"),
      v.literal("data_usage"),
      v.literal("emergency_grant"),
      v.literal("tradepost_sale"),   // Optional: If listing fees are added
    ),
    description: v.string(),
    relatedUserId: v.optional(v.id("users")),
    recipientId: v.optional(v.id("users")), // For gifts (alternative to relatedUserId)
    relatedListingId: v.optional(v.id("listings")), // Link transaction to a listing if applicable
    paymentId: v.optional(v.string()), // For purchases
    timestamp: v.number(),
  }).index("by_user", ["userId"]),

    // New tables for tracking data usage
    userDataUsage: defineTable({
      userId: v.id("users"),
      date: v.number(), // Timestamp for the day (midnight)
      kilobytes: v.number(),
      createdAt: v.number(),
      updatedAt: v.number(),
    })
      .index("by_user", ["userId"])
      .index("by_user_and_date", ["userId", "date"]),

    userMessageStats: defineTable({
      userId: v.id("users"),
      date: v.number(), // Timestamp for the day (midnight)
      count: v.number(),
      createdAt: v.number(),
      updatedAt: v.number(),
    })
      .index("by_user", ["userId"])
      .index("by_user_and_date", ["userId", "date"]),

    emergencyMoola: defineTable({
      userId: v.id("users"),
      messagesUsed: v.number(),
      activated: v.number(), // Timestamp when emergency Moola was first activated
      lastUsed: v.number(), // Timestamp when emergency Moola was last used
    }).index("by_user", ["userId"]),


  chatBackgrounds: defineTable({
    userId: v.id("users"),
    chatId: v.id("chats"),
    backgroundUrl: v.string(), // Or v.id("_storage")
    isActive: v.boolean(),
    createdAt: v.number(),
  }).index("by_user_and_chat", ["userId", "chatId"]),

  contacts: defineTable({
    userId: v.id("users"),
    contactId: v.id("users"),
    nickname: v.optional(v.string()),
    isFavorite: v.boolean(),
    isBlocked: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_user_and_contact", ["userId", "contactId"]),

  emoticons: defineTable({
    name: v.string(),
    code: v.string(),
    imageUrl: v.string(), // Or v.id("_storage")
    category: v.string(),
    isPremium: v.boolean(),
    moolaCost: v.optional(v.number()),
    createdAt: v.number(),
  }).index("by_category", ["category"]),

  userEmoticons: defineTable({
    userId: v.id("users"),
    emoticonId: v.id("emoticons"),
    purchasedAt: v.number(),
  }).index("by_user", ["userId"]),

  nudges: defineTable({
    userId: v.id("users"),
    targetUserId: v.id("users"),
    chatId: v.id("chats"),
    timestamp: v.number(),
  }).index("by_user", ["userId"]),


  // ===========================================
  // ===      NEW TRADEPOST TABLE ADDED      ===
  // ===========================================
  listings: defineTable({
    sellerId: v.id("users"),
    title: v.string(),
    description: v.string(),
    // Using string for price allows "R 150", "Trade", "Free", etc.
    price: v.string(),
    // Optional: Explicit currency/type if needed for filtering
    // priceType: v.optional(v.union(v.literal("ZAR"), v.literal("Moola"), v.literal("Trade"), v.literal("Free"))),
    category: v.union( // Add more relevant categories
      v.literal("electronics"),
      v.literal("fashion"),
      v.literal("home_garden"),
      v.literal("vehicles"),
      v.literal("property"),
      v.literal("hobbies_toys"),
      v.literal("books_music_games"),
      v.literal("services"),
      v.literal("jobs"),
      v.literal("pets"),
      v.literal("other"),
    ),
    condition: v.optional(v.union( // Item condition
        v.literal("new"),
        v.literal("like_new"),
        v.literal("good"),
        v.literal("fair"),
        v.literal("poor"),
    )),
    location: v.optional(v.string()), // e.g., "Cape Town, WC"
    // Store Convex storage IDs for images
    imageStorageIds: v.optional(v.array(v.id("_storage"))),
    tags: v.optional(v.array(v.string())), // For searchability
    status: v.union( // Status of the listing
        v.literal("active"),    // Available for sale/trade
        v.literal("sold"),      // Item has been sold/traded
        v.literal("inactive"),  // Temporarily hidden by seller
        v.literal("expired"),   // Listing duration expired (if applicable)
        v.literal("removed"),   // Removed by admin/moderator
    ),
    viewCount: v.optional(v.number()), // Optional: track views
    createdAt: v.number(),
    updatedAt: v.number(),
    // Optional: Add expiry date if listings should expire
    // expiresAt: v.optional(v.number()),
  })
  .index("by_sellerId", ["sellerId"])        // Find listings by seller
  .index("by_category", ["category"])      // Filter by category
  .index("by_status", ["status"])          // Find active/sold listings
  .index("by_seller_and_status", ["sellerId", "status"]), // Find seller's active listings

  // Tradepost favorites
  tradepostFavorites: defineTable({
    userId: v.id("users"),
    listingId: v.id("listings"),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  // Note: For text search on title/description/tags, you'd configure
  // a Convex Search Index separately in your convex/ directory.

  // Optional: Table for Watchlist/Favorites
  userWatchlist: defineTable({
    userId: v.id("users"),
    listingId: v.id("listings"),
    addedAt: v.number(),
  })
  .index("by_user", ["userId"])
  .index("by_listing", ["listingId"])
  .index("by_user_and_listing", ["userId", "listingId"]),

  // Notifications table
  notifications: defineTable({
    userId: v.id("users"),
    type: v.string(), // message, friend_request, system, etc.
    title: v.string(),
    message: v.string(),
    linkUrl: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    sourceId: v.optional(v.string()), // ID of the related entity (message, user, etc.)
    sourceType: v.optional(v.string()), // Type of the related entity
    isRead: v.boolean(),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  // No duplicate table needed
})