import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { Id } from "./_generated/dataModel"

// Get all marketplace listings
export const getListings = query({
  args: {},
  handler: async (ctx) => {
    const listings = await ctx.db.query("listings").order("desc").collect()

    // Get seller info for each listing
    const listingsWithSellers = await Promise.all(
      listings.map(async (listing) => {
        const seller = await ctx.db.get(listing.sellerId as Id<"users">)

        return {
          ...listing,
          sellerName: seller?.name || "Unknown Seller",
          sellerAvatar: seller?.avatar || "",
        }
      }),
    )

    return listingsWithSellers
  },
})

// Get listings by seller
export const getListingsBySeller = query({
  args: {
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const listings = await ctx.db
      .query("listings")
      .filter((q) => q.eq(q.field("sellerId"), args.sellerId))
      .order("desc")
      .collect()

    return listings
  },
})

// Create a new listing
export const createListing = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    price: v.string(),
    imageStorageIds: v.optional(v.array(v.id("_storage"))),
    category: v.union(
      v.literal("electronics"),
      v.literal("fashion"),
      v.literal("home_garden"),
      v.literal("vehicles"),
      v.literal("property"),
      v.literal("hobbies_toys"),
      v.literal("books_music_games"),
      v.literal("services"),
      v.literal("jobs"),
      v.literal("pets"),
      v.literal("other")
    ),
    location: v.string(),
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const { title, description, price, imageStorageIds, category, location, sellerId } = args

    // Validate that price is not empty
    if (!price.trim()) {
      throw new Error("Price cannot be empty")
    }

    // Create listing
    const listingId = await ctx.db.insert("listings", {
      title,
      description,
      price,
      imageStorageIds: imageStorageIds || [], 
      category,
      location,
      sellerId,
      status: "active",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    })

    return listingId
  },
})

// Update a listing
export const updateListing = mutation({
  args: {
    listingId: v.id("listings"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.string()),
    imageStorageIds: v.optional(v.array(v.id("_storage"))),
    category: v.optional(v.union(
      v.literal("electronics"),
      v.literal("fashion"),
      v.literal("home_garden"),
      v.literal("vehicles"),
      v.literal("property"),
      v.literal("hobbies_toys"),
      v.literal("books_music_games"),
      v.literal("services"),
      v.literal("jobs"),
      v.literal("pets"),
      v.literal("other")
    )),
    location: v.optional(v.string()),
    status: v.optional(v.union(v.literal("active"), v.literal("sold"), v.literal("inactive"))),
  },
  handler: async (ctx, args) => {
    const { listingId, ...updates } = args

    // Validate price if provided
    if (updates.price !== undefined && !updates.price.trim()) {
      throw new Error("Price cannot be empty")
    }

    // Update listing
    await ctx.db.patch(listingId, {
      ...updates,
      updatedAt: Date.now(),
    })

    return listingId
  },
})

// Delete a listing
export const deleteListing = mutation({
  args: {
    listingId: v.id("listings"),
    sellerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const { listingId, sellerId } = args

    // Get the listing
    const listing = await ctx.db.get(listingId)
    if (!listing) {
      throw new Error("Listing not found")
    }

    // Check if the user is the seller
    if (listing.sellerId !== sellerId) {
      throw new Error("You can only delete your own listings")
    }

    // Delete the listing
    await ctx.db.delete(listingId)

    return { success: true }
  },
})

// Add a listing to favorites
export const addToFavorites = mutation({
  args: {
    userId: v.id("users"),
    listingId: v.id("listings"),
  },
  handler: async (ctx, args) => {
    const { userId, listingId } = args

    // Check if already in favorites
    const existing = await ctx.db
      .query("tradepostFavorites")
      .filter((q) => q.and(q.eq(q.field("userId"), userId), q.eq(q.field("listingId"), listingId)))
      .first()

    if (existing) {
      return { success: true, alreadyExists: true }
    }

    // Add to favorites
    await ctx.db.insert("tradepostFavorites", {
      userId,
      listingId,
      createdAt: Date.now(),
    })

    return { success: true, alreadyExists: false }
  },
})

// Remove a listing from favorites
export const removeFromFavorites = mutation({
  args: {
    userId: v.id("users"),
    listingId: v.id("listings"),
  },
  handler: async (ctx, args) => {
    const { userId, listingId } = args

    // Find the favorite entry
    const favorite = await ctx.db
      .query("tradepostFavorites")
      .filter((q) => q.and(q.eq(q.field("userId"), userId), q.eq(q.field("listingId"), listingId)))
      .first()

    if (!favorite) {
      return { success: true, notFound: true }
    }

    // Remove from favorites
    await ctx.db.delete(favorite._id)

    return { success: true, notFound: false }
  },
})

// Get user's favorite listings
export const getFavoriteListings = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const favorites = await ctx.db
      .query("tradepostFavorites")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .collect()

    const listingIds = favorites.map((fav) => fav.listingId)

    if (listingIds.length === 0) {
      return []
    }

    // Get the actual listings
    const listings = await Promise.all(
      listingIds.map(async (id) => {
        const listing = await ctx.db.get(id)
        if (!listing) return null

        const seller = await ctx.db.get(listing.sellerId as Id<"users">)

        return {
          ...listing,
          sellerName: seller?.name || "Unknown Seller",
          sellerAvatar: seller?.avatar || "",
          isFavorite: true,
        }
      }),
    )

    return listings.filter(Boolean)
  },
})

// Add this function to get a single listing by ID
export const getListing = query({
  args: {
    listingId: v.id("listings"),
  },
  handler: async (ctx, args) => {
    const { listingId } = args

    // Get the listing
    const listing = await ctx.db.get(listingId)
    if (!listing) {
      return null
    }

    // Get the seller
    const seller = await ctx.db.get(listing.sellerId as Id<"users">)
    if (!seller) {
      return {
        ...listing,
        sellerName: "Unknown Seller",
        sellerAvatar: "",
      }
    }

    return {
      ...listing,
      sellerName: seller.name,
      sellerAvatar: seller.avatar,
    }
  },
})

