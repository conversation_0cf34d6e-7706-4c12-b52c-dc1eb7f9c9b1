"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { Gift } from "lucide-react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { MoolaIcon } from "@/components/moola-icon"
import { Id } from "@/convex/_generated/dataModel"

interface GiftMoolaDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  recipientId: string
  recipientName: string
}

export function GiftMoolaDialog({ open, onOpenChange, recipientId, recipientName }: GiftMoolaDialogProps) {
  const { toast } = useToast()
  const { user } = useAuth() as any
  const [amount, setAmount] = useState(10)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const giftMoola = useMutation(api.moola.giftMoola)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to gift Moola",
        variant: "destructive",
      })
      return
    }

    if (amount <= 0) {
      toast({
        title: "Error",
        description: "Please enter a valid amount",
        variant: "destructive",
      })
      return
    }

    if (user.moola < amount) {
      toast({
        title: "Error",
        description: "You don't have enough Moola",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      await giftMoola({
        senderId: user._id,
        recipientId: recipientId as Id<"users">,
        amount,
      })

      toast({
        title: "Success",
        description: `You gifted ${amount} Moola to ${recipientName}`,
      })

      onOpenChange(false)
    } catch (error) {
      console.error("Error gifting Moola:", error)
      toast({
        title: "Error",
        description: "Failed to gift Moola",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent aria-describedby="gift-moola-description">
        <DialogHeader>
          <DialogTitle>Gift Moola to {recipientName}</DialogTitle>
          <DialogDescription id="gift-moola-description">Send Moola as a gift</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center gap-4">
            <Gift className="h-10 w-10 text-primary" />
            <div>
              <p className="font-medium">Recipient</p>
              <p className="text-sm text-muted-foreground">{recipientName}</p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">Your Balance</p>
            <div className="flex items-center gap-1">
              <MoolaIcon className="h-4 w-4 text-yellow-500" />
              <p className="font-medium">{user?.moola || 0}</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount to Gift</Label>
            <div className="flex items-center">
              <MoolaIcon className="h-4 w-4 mr-2 text-yellow-500" />
              <Input
                id="amount"
                type="number"
                min={1}
                max={user?.moola || 0}
                value={amount}
                onChange={(e) => setAmount(Number.parseInt(e.target.value) || 0)}
                className="flex-1"
              />
            </div>
            <p className="text-xs text-muted-foreground">You can gift up to {user?.moola || 0} Moola.</p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || amount <= 0 || (user?.moola || 0) < amount}
            >
              {isSubmitting ? "Sending..." : "Send Gift"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}





