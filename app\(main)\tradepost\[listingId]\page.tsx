"use client"

import Link from "next/link"
import Image from "next/image"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { useState, useEffect } from "react"

// UI Components
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

// Icons
import { ArrowLeft, MapPin, CalendarDays, MessageSquare, Loader2 } from "lucide-react"
import type { Id } from "@/convex/_generated/dataModel"

// Helper Function for Formatting Dates
function formatDate(timestamp: number | null) {
  if (!timestamp) return ""
  return new Date(timestamp).toLocaleDateString("en-ZA", {
    day: "numeric",
    month: "short",
    year: "numeric",
  })
}

// The Page Component
export default function ListingDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { userId, user } = useAuth()
  const initiateChatWithSeller = useMutation(api.chats.initiateChatWithSeller)

  const [isContacting, setIsContacting] = useState(false)
  const [contactError, setContactError] = useState<string | null>(null)

  const listingId = params.listingId as string

  // Handle the special case for "create" route
  useEffect(() => {
    if (listingId === "create") {
      router.push("/tradepost/create")
    }
  }, [listingId, router])

  // Fetch data using useQuery - Only if not "create"
  const listingData =
    listingId !== "create"
      ? useQuery(api.tradepost.getListing, {
          listingId: listingId as Id<"tradepostListings">,
        })
      : undefined

  // Handle Loading State
  if (listingData === undefined || !userId) {
    return <ListingDetailSkeleton />
  }

  // Handle Not Found
  if (listingData === null) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium text-muted-foreground">Listing not found</h2>
        <p className="text-sm text-muted-foreground mt-2">The listing you're looking for might have been removed.</p>
        <Link href="/tradepost" className="mt-4 inline-block">
          <Button variant="secondary">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Tradepost
          </Button>
        </Link>
      </div>
    )
  }

  // Now we know listingData is available
  const { sellerName, sellerAvatar, ...listing } = listingData

  // Determine if the current user is the seller
  const isSeller = userId === listing.sellerId

  // Contact Seller Handler
  const handleContactSellerClick = async () => {
    if (!userId) {
      router.push(`/sign-in?redirect_url=/tradepost/${listing._id}`)
      return
    }
    if (isSeller) return

    setIsContacting(true)
    setContactError(null)

    try {
      const result = await initiateChatWithSeller({
        listingId: listing._id,
        sellerId: listing.sellerId,
      })

      // Redirect to the chat page on success
      router.push(`/chat/${result.chatId}?listingRef=${listing._id}`)
    } catch (error) {
      console.error("Failed to initiate chat:", error)
      setContactError(error instanceof Error ? error.message : "Could not contact seller.")
    } finally {
      setIsContacting(false)
    }
  }

  // If this is the create page, render nothing
  if (listingId === "create") {
    return null
  }

  // Render the Page
  return (
    <div className="bg-muted/40 min-h-screen pb-16 md:pb-0">
      <main className="container py-6 md:py-8 px-4">
        {/* Back Button */}
        <div className="mb-4">
          <Link href="/tradepost">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Tradepost
            </Button>
          </Link>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          {/* Left Column (Image) */}
          <div className="md:col-span-2">
            <Card className="overflow-hidden shadow-md">
              <div className="relative aspect-video w-full bg-muted">
                <Image
                  src={listing.imageUrl || "/placeholder.svg?height=400&width=600"}
                  alt={listing.title}
                  fill
                  className="object-cover"
                  priority
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                />
              </div>
            </Card>
          </div>

          {/* Right Column (Details & Actions) */}
          <div className="md:col-span-1 flex flex-col gap-4">
            {/* Title and Price */}
            <Card className="shadow-sm">
              <CardHeader>
                <Badge variant="secondary" className="w-fit mb-2 capitalize">
                  {listing.category.replace("_", " ")}
                </Badge>
                <CardTitle className="text-2xl md:text-3xl">{listing.title}</CardTitle>
                <p className="text-2xl font-bold text-primary mt-1">{listing.price} Moola</p>
              </CardHeader>
              <CardContent className="flex flex-col gap-2 text-sm text-muted-foreground">
                {listing.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 flex-shrink-0" />
                    <span>{listing.location}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <CalendarDays className="h-4 w-4 flex-shrink-0" />
                  <span>Posted: {formatDate(listing.createdAt)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Seller Information */}
            {sellerName && (
              <Card className="shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg">Seller Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={sellerAvatar} alt={sellerName} />
                      <AvatarFallback>{sellerName?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-semibold">{sellerName}</p>
                      <p className="text-xs text-muted-foreground">Member since {formatDate(listing.createdAt)}</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  {/* Contact Seller Button */}
                  <Button onClick={handleContactSellerClick} className="w-full" disabled={isSeller || isContacting}>
                    {isContacting ? (
                      <>
                        {" "}
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Contacting...{" "}
                      </>
                    ) : isSeller ? (
                      "This is your listing"
                    ) : (
                      <>
                        {" "}
                        <MessageSquare className="mr-2 h-4 w-4" /> Contact Seller{" "}
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            )}
            {/* Display contact error */}
            {contactError && <p className="text-sm text-destructive text-center mt-2">{contactError}</p>}
          </div>

          {/* Description */}
          <div className="md:col-span-2">
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle className="text-xl">Description</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-sm dark:prose-invert max-w-none text-foreground">
                <p className="whitespace-pre-wrap">{listing.description}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

// Skeleton Loader Component
function ListingDetailSkeleton() {
  return (
    <div className="container py-6 md:py-8 px-4 animate-pulse">
      <div className="mb-4">
        <Skeleton className="h-8 w-36" /> {/* Back button */}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
        {/* Image Skeleton */}
        <div className="md:col-span-2">
          <Skeleton className="aspect-video w-full rounded-lg" />
        </div>
        {/* Details Skeleton */}
        <div className="md:col-span-1 flex flex-col gap-4">
          {/* Title/Price Card */}
          <Card>
            <CardHeader>
              <Skeleton className="h-5 w-20 mb-2" /> {/* Badge */}
              <Skeleton className="h-8 w-3/4 mb-1" /> {/* Title */}
              <Skeleton className="h-7 w-1/2" /> {/* Price */}
            </CardHeader>
            <CardContent className="flex flex-col gap-2">
              <Skeleton className="h-5 w-2/3" /> {/* Location */}
              <Skeleton className="h-5 w-1/2" /> {/* Date */}
            </CardContent>
          </Card>
          {/* Seller Card */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" /> {/* Seller Title */}
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-1.5">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Skeleton className="h-10 w-full" /> {/* Button */}
            </CardFooter>
          </Card>
        </div>
        {/* Description Skeleton */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-28" /> {/* Description Title */}
            </CardHeader>
            <CardContent className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}


