"use client"

import { useState, useEffect } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import type { Id } from "@/convex/_generated/dataModel"

export function useChat(chatId: string | null) {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [actualChatId, setActualChatId] = useState<string | null>(null)
  const [isChatIdResolved, setIsChatIdResolved] = useState(false)

  // Create direct chat mutation
  const createDirectChat = useMutation(api.chats.createDirectChat)

  // Add debug logging
  useEffect(() => {
    console.log(`useChat hook - chatId: ${chatId}, userId: ${userId}, isAuthLoading: ${isAuthLoading}, actualChatId: ${actualChatId}, isChatIdResolved: ${isChatIdResolved}`)
  }, [chatId, userId, isAuthLoading, actualChatId, isChatIdResolved])

  // If chatId looks like a user ID, we need to create or get a direct chat
  useEffect(() => {
    // Reset state when chatId changes
    if (chatId !== null) {
      setIsChatIdResolved(false)
      setActualChatId(null)
    }

    const fetchOrCreateChat = async () => {
      if (!chatId || !userId || isAuthLoading) return

      try {
        // Check if the chatId is actually a user ID
        // This is a heuristic - in a real app, you might want to validate this more carefully
        if (chatId.includes('users')) {
          console.log(`chatId ${chatId} appears to be a user ID, creating/fetching direct chat`)

          // Create or get a direct chat with this user
          const newChatId = await createDirectChat({
            userId: userId as Id<"users">,
            otherUserId: chatId as Id<"users">,
          })

          console.log(`Created/fetched direct chat with ID: ${newChatId}`)
          setActualChatId(newChatId)
          setIsChatIdResolved(true)
        } else {
          // It's already a chat ID
          setActualChatId(chatId)
          setIsChatIdResolved(true)
        }
      } catch (err) {
        console.error("Error creating/fetching direct chat:", err)
        setError(err instanceof Error ? err : new Error("Failed to create/fetch chat"))
        setIsChatIdResolved(true) // Mark as resolved even on error
      }
    }

    fetchOrCreateChat()
  }, [chatId, userId, isAuthLoading, createDirectChat])

  // Get a specific chat by ID only after we've resolved whether it's a chat ID or user ID
  const chat = useQuery(
    api.chats.getChatById,
    actualChatId && userId && isChatIdResolved
      ? {
          chatId: actualChatId as Id<"chats">,
          userId: userId as Id<"users">,
        }
      : "skip",
  )

  // Add debug logging for the chat result
  useEffect(() => {
    console.log(`useChat hook - chat query result:`, chat)

    if (chat === undefined && chatId && userId) {
      console.log(`Chat is undefined for chatId: ${chatId}`)
    }
  }, [chat, chatId, userId])

  useEffect(() => {
    if (isAuthLoading) {
      setIsLoading(true)
    } else if (!chatId || !userId) {
      setIsLoading(false)
      setError(new Error("Missing chatId or userId"))
    } else if (!isChatIdResolved) {
      // Still resolving the chat ID
      setIsLoading(true)
    } else if (chat !== undefined) {
      setIsLoading(false)
    }
  }, [chat, isAuthLoading, chatId, userId, isChatIdResolved])

  return {
    chat: chat || null,
    chatId: actualChatId, // Return the actual chat ID for use in other hooks
    isLoading: isLoading || isAuthLoading,
    error,
  }
}

