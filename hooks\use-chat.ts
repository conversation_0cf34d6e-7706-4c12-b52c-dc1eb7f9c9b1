"use client"

import { useState, useEffect, useCallback } from "react"
import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import type { Id } from "@/convex/_generated/dataModel"
import { useQuery as useConvexQuery } from "convex/react"

export function useChat(chatId: string | null) {
  const { userId, isLoading: isAuthLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [actualChatId, setActualChatId] = useState<string | null>(chatId)
  const [isChatIdResolved, setIsChatIdResolved] = useState(false)
  const [chatNotFound, setChatNotFound] = useState(false)

  // Create direct chat mutation
  const createDirectChat = useMutation(api.chats.createDirectChat)

  // Add debug logging
  useEffect(() => {
    console.log(`useChat hook - chatId: ${chatId}, userId: ${userId}, isAuthLoading: ${isAuthLoading}, actualChatId: ${actualChatId}, isChatIdResolved: ${isChatIdResolved}`)
  }, [chatId, userId, isAuthLoading, actualChatId, isChatIdResolved])

  // Define chat type for better type safety
  type ChatWithParticipants = {
    _id: Id<"chats">;
    _creationTime: number;
    type: string;
    name?: string;
    description?: string;
    background?: string;
    isPublic: boolean;
    creatorId: Id<"users">;
    lastMessageId?: Id<"messages">;
    createdAt: number;
    updatedAt: number;
    category?: string;
    isAnonymous?: boolean;
    moolaPerMessage?: number;
    location?: string;
    tags?: string[];
    isPrivate?: boolean;
    thumbnailUrl?: string;
    participants: Array<{
      _id: Id<"users">;
      _creationTime: number;
      email?: string;
      lastSeen?: number;
      statusMessage?: string;
      mood?: "happy" | "sad" | "excited" | "bored" | "busy" | "relaxed";
      theme?: string;
      moola?: number;
      clerkId: string;
      name: string;
      avatar: string;
      status: "online" | "offline" | "away";
      language?: string;
      dataSavingMode?: boolean;
      lastLoginReward?: number;
      loginStreak?: number;
      referredBy?: Id<"users">;
      referralCount?: number;
      achievements?: string[];
      createdAt: number;
      updatedAt: number;
    } | null>;
  } | null;

  // Get chat by ID query
  const getChatById = useConvexQuery(api.chats.getChatById, 
    (chatId && !chatId.startsWith('user_') && userId) 
      ? { 
          chatId: chatId as Id<"chats">, 
          userId: userId as Id<"users"> 
        } 
      : 'skip'
  ) as ChatWithParticipants | 'skip'

  // If chatId looks like a user ID, we need to create or get a direct chat
  useEffect(() => {
    // Reset state when chatId changes
    if (chatId !== null) {
      setIsChatIdResolved(false)
      setActualChatId(null)
    }

    const fetchOrCreateChat = async () => {
      if (!chatId || !userId || isAuthLoading) return

      try {
        // Check if the chatId is actually a user ID
        if (chatId.startsWith('user_') || chatId.includes('users')) {
          console.log(`chatId ${chatId} appears to be a user ID, creating/fetching direct chat`)

          // Create or get a direct chat with this user
          const newChatId = await createDirectChat({
            userId: userId as Id<"users">,
            otherUserId: chatId as Id<"users">,
          })

          console.log(`Created/fetched direct chat with ID: ${newChatId}`)
          setActualChatId(newChatId)
          setIsChatIdResolved(true)
        } else if (chatId.startsWith('chats_') || chatId.includes('chats')) {
          // If it's a chat ID, use it directly
          console.log(`Using provided chat ID: ${chatId}`)
          setActualChatId(chatId)
          setIsChatIdResolved(true)
        } else {
          // If we're not sure, check if we have chat data from the query
          console.log(`Checking if chat ID is valid: ${chatId}`)
          if (getChatById && getChatById !== 'skip') {
            console.log(`Found existing chat with ID: ${chatId}`, { chatData: getChatById })
            setActualChatId(chatId)
            setIsChatIdResolved(true)
          } else {
            console.warn(`No chat data found for ID: ${chatId}. This might be a new chat.`)
            // Instead of treating this as an error, we'll treat it as a new chat
            setActualChatId(chatId)
            setIsChatIdResolved(true)
            // Don't set an error since this might be a valid case for new chats
          }
        }
      } catch (err) {
        console.error("Error creating/fetching direct chat:", err)
        setError(err instanceof Error ? err : new Error("Failed to create/fetch chat"))
        setIsChatIdResolved(true) // Mark as resolved even on error
      }
    }

    fetchOrCreateChat()
  }, [chatId, userId, isAuthLoading, createDirectChat])

  // Get a specific chat by ID only after we've resolved whether it's a chat ID or user ID
  const chatQuery = useQuery(
    api.chats.getChatById,
    actualChatId && userId && isChatIdResolved
      ? {
          chatId: actualChatId as Id<"chats">,
          userId: userId as Id<"users">,
        }
      : "skip",
  )

  // Add debug logging for the chat result
  useEffect(() => {
    console.log(`useChat hook - chat query result:`, chatQuery)
    
    // Handle case where chat is not found
    if (chatQuery === null && actualChatId && userId) {
      console.warn(`Chat not found for ID: ${actualChatId}`)
      setChatNotFound(true)
      setError(new Error('Chat not found'))
    } else if (chatQuery) {
      setChatNotFound(false)
      setError(null)
    }

    if (chatQuery === undefined && chatId && userId) {
      console.log(`Chat is undefined for chatId: ${chatId}`)
    }
  }, [chatQuery, chatId, userId])

  useEffect(() => {
    if (isAuthLoading) {
      setIsLoading(true)
    } else if (!chatId || !userId) {
      setIsLoading(false)
      setError(new Error("Missing chatId or userId"))
    } else if (!isChatIdResolved) {
      // Still resolving the chat ID
      setIsLoading(true)
    } else if (chatQuery !== undefined) {
      setIsLoading(false)
    }
  }, [chatQuery, isAuthLoading, chatId, userId, isChatIdResolved])

  // Function to create a new direct chat
  const createNewChat = useCallback(async (otherUserId: string) => {
    if (!userId) return null;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const newChatId = await createDirectChat({
        userId: userId as Id<"users">,
        otherUserId: otherUserId as Id<"users">,
      });
      
      if (newChatId) {
        setActualChatId(newChatId);
        setChatNotFound(false);
        return newChatId;
      }
      return null;
    } catch (err) {
      console.error('Failed to create new chat:', err);
      setError(err instanceof Error ? err : new Error('Failed to create chat'));
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [userId, createDirectChat]);

  return {
    chat: chatQuery,
    isLoading: isLoading || !isChatIdResolved || chatQuery === undefined,
    error,
    chatNotFound,
    isGroup: chatQuery?.type === 'group' || chatQuery?.type === 'chatroom',
    isDirect: chatQuery?.type === 'direct',
    otherParticipant: chatQuery?.type === 'direct' ? chatQuery.participants.find(participant => participant?._id !== userId) : null,
    createNewChat,
    chatId: actualChatId,
  }
}
