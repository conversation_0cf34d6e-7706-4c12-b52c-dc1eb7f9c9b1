'use client';

import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useUser } from '@clerk/nextjs';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface NewsItem {
  _id: string;
  content: string;
  timestamp: number;
  userName: string;
  userAvatar?: string;
}

export function NewsSection() {
  const { user } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [url, setUrl] = useState('');
  
  // Fetch news
  const news = useQuery(api.chatrooms.getLocalNews) || [];
  
  // Mutation for submitting news
  const submitNews = useMutation(api.chatrooms.submitLocalNews);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title || !description) return;
    
    setIsSubmitting(true);
    try {
      await submitNews({ title, description, url });
      setTitle('');
      setDescription('');
      setUrl('');
      toast.success('News submitted successfully!');
    } catch (error) {
      console.error('Error submitting news:', error);
      toast.error('Failed to submit news. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper to parse markdown-like content
  const parseContent = (content: string) => {
    // Simple markdown parsing for bold text and links
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-blue-500 hover:underline">$1</a>');
  };

  return (
    <div className="space-y-6">
      {/* Submit News Form */}
      <Card>
        <CardHeader>
          <CardTitle>Submit Local News</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                placeholder="News Title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div>
              <Textarea
                placeholder="News Description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                required
              />
            </div>
            <div>
              <Input
                type="url"
                placeholder="Optional: Add a URL"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : 'Submit News'}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* News Feed */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Local News</h2>
        {news.length === 0 ? (
          <p className="text-muted-foreground">No news yet. Be the first to share!</p>
        ) : (
          <div className="space-y-6">
            {news.map((item) => (
              <Card key={item._id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-2">
                    {item.userAvatar && (
                      <img 
                        src={item.userAvatar} 
                        alt={item.userName} 
                        className="w-8 h-8 rounded-full"
                      />
                    )}
                    <div>
                      <p className="font-medium">{item.userName}</p>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(item.timestamp), 'MMM d, yyyy h:mm a')}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div 
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: parseContent(item.content) }}
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
