import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import type { Id } from "@/convex/_generated/dataModel";
import { useEffect, useRef, useCallback, useMemo } from "react";
import { useAuth } from "@/hooks/use-auth";

export function useOnlineUsers(userIds?: Id<"users">[]) {
  const { isAuthenticated, userId: currentUserId } = useAuth();
  const isMounted = useRef(true);
  
  // Get the updatePresence mutation
  const updatePresence = useMutation(api.presence.updatePresence);
  
  // Determine which query to use based on parameters
  const { query, args } = useMemo(() => {
    if (!isAuthenticated || !currentUserId) {
      return { query: api.presence.getAllPresence, args: "skip" };
    }
    return userIds 
      ? { query: api.presence.getUsersPresence, args: { userIds } }
      : { query: api.presence.getAllPresence, args: {} };
  }, [isAuthenticated, currentUserId, userIds]);
  
  // Only fetch presence data if authenticated
  const presenceData = useQuery(query, args) || {};

  // Memoize the presence update function
  const updateUserPresence = useCallback(async (status: 'online' | 'away' | 'offline' = 'online') => {
    if (!isAuthenticated || !currentUserId) return;
    
    try {
      console.log(`[Presence] Updating status to ${status} for user ${currentUserId}`);
      await updatePresence({ status });
    } catch (error) {
      // Only log unexpected errors
      if (!(error instanceof Error && error.message.includes('Not authenticated'))) {
        console.error('Failed to update presence:', error);
      }
    }
  }, [updatePresence, isAuthenticated, currentUserId]);

  // Set up a heartbeat to keep the user's presence updated
  useEffect(() => {
    if (!isAuthenticated || !currentUserId) return;
    
    // Initial presence update - force online status
    updateUserPresence('online');
    
    // Set up interval for periodic updates
    const intervalId = setInterval(() => updateUserPresence('online'), 30000); // Every 30 seconds
    
    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [updateUserPresence, isAuthenticated, currentUserId]);

  // Update presence when the window gets focus
  useEffect(() => {
    if (!isAuthenticated || !currentUserId) return;
    
    const handleFocus = () => {
      updateUserPresence();
    };
    
    window.addEventListener('focus', handleFocus);
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [updateUserPresence, isAuthenticated, currentUserId]);

  // Process presence data - simplified version
  const { onlineUsers, awayUsers } = useMemo(() => {
    console.log('[Presence] Processing presence data:', presenceData);
    const onlineUsers = new Set<string>();
    const awayUsers = new Set<string>();
    const now = Date.now();
    
    if (presenceData) {
      // Handle both array and object response formats
      const users = Array.isArray(presenceData) 
        ? presenceData 
        : Object.entries(presenceData).map(([id, data]) => ({ _id: id, ...(data as any) }));
      
      users.forEach(user => {
        if (!user?._id) return;
        
        const lastActive = (user as any).lastActive || 0;
        const timeSinceActive = now - lastActive;
        const isAway = timeSinceActive > 5 * 60 * 1000; // 5 minutes
        
        console.log(`[Presence] User ${user._id} status:`, {
          status: (user as any).status,
          lastActive,
          timeSinceActive,
          isAway
        });
        
        if ((user as any).status === 'online') {
          if (isAway) {
            awayUsers.add(user._id);
            console.log(`[Presence] User ${user._id} marked as away`);
          } else {
            onlineUsers.add(user._id);
            console.log(`[Presence] User ${user._id} marked as online`);
          }
        }
      });
    }
    
    // Current user is always considered online if authenticated
    if (isAuthenticated && currentUserId) {
      onlineUsers.add(currentUserId);
    }
    
    return { onlineUsers, awayUsers };
  }, [presenceData, isAuthenticated, currentUserId]);

  // Get user status (online/away/offline)
  const getUserStatus = useCallback((userId: string): 'online' | 'away' | 'offline' => {
    if (userId === currentUserId) {
      return isAuthenticated ? 'online' : 'offline';
    }
    
    if (onlineUsers.has(userId)) return 'online';
    if (awayUsers.has(userId)) return 'away';
    return 'offline';
  }, [onlineUsers, awayUsers, currentUserId, isAuthenticated]);
  
  // For backward compatibility
  const isUserOnline = useCallback((userId: string): boolean => {
    return getUserStatus(userId) === 'online';
  }, [getUserStatus]);
  
  // Get status text (for display)
  const getStatusText = useCallback((userId: string): string => {
    const status = getUserStatus(userId);
    return status.charAt(0).toUpperCase() + status.slice(1);
  }, [getUserStatus]);

  return {
    isUserOnline,
    getUserStatus,
    getStatusText,
    onlineUsers,
    updatePresence: updateUserPresence
  };
}
