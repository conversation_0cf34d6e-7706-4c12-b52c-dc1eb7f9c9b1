import { v } from "convex/values"
import { query } from "./_generated/server"

// A public query that doesn't require authentication
export const publicQuery = query({
  args: {},
  handler: async (ctx) => {
    return { message: "This is a public query that works without authentication" }
  },
})

// A query that requires authentication
export const authQuery = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    // Get the auth information
    const identity = await ctx.auth.getUserIdentity()

    if (!identity) {
      throw new Error("Not authenticated")
    }

    // The tokenIdentifier should be in the format "https://clerk.your-domain.com|user_id"
    const tokenSubject = identity.tokenIdentifier.split("|")[1]

    // Verify that the clerkId matches the token subject
    if (tokenSubject !== args.clerkId) {
      throw new Error("User ID mismatch")
    }

    return {
      message: "Authentication successful",
      user: {
        subject: tokenSubject,
        clerkId: args.clerkId,
      },
    }
  },
})

