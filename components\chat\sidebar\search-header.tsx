import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, UserPlus } from "lucide-react";
import { SearchHeaderProps } from "./types";

export function SearchHeader({ search, onSearchChange, onManageContacts }: SearchHeaderProps) {
  return (
    <div className="p-4 border-b">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Messages</h2>
        <Button
          variant="ghost"
          size="icon"
          aria-label="Manage Contacts"
          title="Manage Contacts"
          onClick={onManageContacts}
        >
          <UserPlus className="h-5 w-5" />
        </Button>
      </div>
      <div className="relative">
        <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search chats..."
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-8 w-full"
          aria-label="Search chats"
        />
      </div>
    </div>
  );
}
