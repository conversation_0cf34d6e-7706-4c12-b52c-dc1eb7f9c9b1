// src/components/news/submit-news-dialog.tsx
"use client"

import { useState } from "react"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { useConvexUser } from "@/hooks/use-convex-auth" // Assuming you have this hook

interface SubmitNewsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  localNewsChatroomId: Id<"chats"> // Pass the ID of your 'Local News' chatroom
}

export function SubmitNewsDialog({ open, onOpenChange, localNewsChatroomId }: SubmitNewsDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [url, setUrl] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { userId, user } = useConvexUser(); // Get user details
  const createLocalNews = useMutation(api.localNews.createLocalNews);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId || !user) {
      toast({
        title: "Error",
        description: "You must be logged in to submit news.",
        variant: "destructive",
      });
      return;
    }
    if (!title || !description) {
      toast({
        title: "Error",
        description: "Title and Description are required.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await createLocalNews({
        title,
        description,
        url: url || undefined, // Send undefined if empty string
        userId: userId as Id<"users">,
        userName: user.name || "Anonymous", // Use user's name or a fallback
        userProfileImage: user.avatar || undefined,
        localNewsChatroomId,
      });

      toast({
        title: "Success",
        description: "Local news submitted successfully!",
      });

      setTitle("");
      setDescription("");
      setUrl("");
      onOpenChange(false); // Close dialog
    } catch (error) {
      console.error("Error submitting local news:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit local news.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Submit Local News</DialogTitle>
          <DialogDescription>
            Share an important update or event with the community.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Title
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="url" className="text-right">
              Link (Optional)
            </Label>
            <Input
              id="url"
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="col-span-3"
              placeholder="e.g., https://example.com/article"
            />
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit News"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}