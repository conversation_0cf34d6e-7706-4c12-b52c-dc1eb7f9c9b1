// This script creates a basic icon directly in the browser
// It's a fallback in case the other icon generation methods don't work

function createBasicIcon() {
    const canvas = document.createElement("canvas")
    canvas.width = 192
    canvas.height = 192
    const ctx = canvas.getContext("2d")
  
    // Background
    ctx.fillStyle = "#1a1a2e"
    ctx.fillRect(0, 0, 192, 192)
  
    // Text
    ctx.fillStyle = "white"
    ctx.font = "bold 96px Arial"
    ctx.textAlign = "center"
    ctx.textBaseline = "middle"
    ctx.fillText("XC", 96, 96)
  
    // Convert to data URL
    const dataUrl = canvas.toDataURL("image/png")
  
    // Create download link
    const link = document.createElement("a")
    link.download = "icon-192x192.png"
    link.href = dataUrl
    link.click()
  }
  
  // Call the function
  createBasicIcon()
  
  