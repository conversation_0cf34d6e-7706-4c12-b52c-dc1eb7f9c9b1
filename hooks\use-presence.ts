// hooks/use-user-presence.ts
"use client";

import { useEffect, useCallback, useRef } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useConvexUser } from "@/hooks/use-convex-auth"; // Assuming this hook provides userId
import { Id } from "@/convex/_generated/dataModel";

type UserStatus = "online" | "offline" | "away";

export function useUserPresence() {
  const { userId, isLoading: isUserLoading } = useConvexUser();
  const updatePresence = useMutation(api.presence.updatePresence);
  const statusRef = useRef<UserStatus | null>(null); // To keep track of current status without re-renders

  const setStatus = useCallback(
    (newStatus: UserStatus) => {
      if (userId && !isUserLoading && statusRef.current !== newStatus) {
        statusRef.current = newStatus;
        updatePresence({ status: newStatus }).catch(
          (error) => {
            console.error("Failed to update user status:", newStatus, error);
            // Optionally revert statusRef.current or handle error display
          }
        );
      }
    },
    [userId, isUserLoading, updatePresence]
  );

  useEffect(() => {
    if (!userId || isUserLoading) {
      // Don't set status if user is not logged in or still loading
      return;
    }

    // Set initial status to "online" when component mounts
    setStatus("online");

    const handleVisibilityChange = () => {
      if (document.hidden) {
        setStatus("away"); // User is away when tab is hidden
      } else {
        setStatus("online"); // User is back online when tab is visible
      }
    };

    const handleBeforeUnload = () => {
      // Try to set status to "offline" before the tab/window closes
      // This is best-effort as it might not always complete.
      // A more robust solution involves server-side timeouts.
      setStatus("offline");
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup: Set status to "offline" when component unmounts
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("beforeunload", handleBeforeUnload);
      if (userId && !isUserLoading) {
        // Only attempt to set offline if userId is valid and not still loading
        setStatus("offline");
      }
    };
  }, [userId, isUserLoading, setStatus]);

  // You can optionally return the current user's status from this hook
  // if you want other parts of your app to react to the *current user's* status
  // without re-querying. For others' status, you'd use api.presence.getUserStatus.
  // const myStatus = useQuery(api.presence.getUserStatus, userId ? { userId } : 'skip');
  // return myStatus;
}