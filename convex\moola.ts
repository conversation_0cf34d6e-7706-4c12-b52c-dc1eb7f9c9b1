import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { MOOLA_RATES } from "../lib/moola-services"

export const getTransactions = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const transactions = await ctx.db
      .query("moolaTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .collect()

    return transactions
  },
})

export const getMoolaBalance = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) return 0

    return user.moola
  },
})

export const getUserUsageStats = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) return null

    // Get today's date at midnight
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayTimestamp = today.getTime()

    // Get start of week (Sunday)
    const startOfWeek = new Date(today)
    startOfWeek.setDate(today.getDate() - today.getDay())
    const startOfWeekTimestamp = startOfWeek.getTime()

    // Get today's transactions
    const todayTransactions = await ctx.db
      .query("moolaTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.gte(q.field("timestamp"), todayTimestamp))
      .collect()

    // Get this week's transactions
    const weeklyTransactions = await ctx.db
      .query("moolaTransactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.gte(q.field("timestamp"), startOfWeekTimestamp))
      .collect()

    // Calculate stats
    const todayMoolaUsed = todayTransactions.filter((t) => t.amount < 0).reduce((sum, t) => sum + Math.abs(t.amount), 0)

    const weeklyMoolaUsed = weeklyTransactions
      .filter((t) => t.amount < 0)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0)

    // Get today's message count
    const todayMessagesSent = await ctx.db
      .query("userMessageStats")
      .withIndex("by_user_and_date", (q) => q.eq("userId", args.userId).gte("date", todayTimestamp))
      .first()

    // Get today's data usage
    const todayDataUsage = await ctx.db
      .query("userDataUsage")
      .withIndex("by_user_and_date", (q) => q.eq("userId", args.userId).gte("date", todayTimestamp))
      .first()

    return {
      todayMoolaUsed,
      todayMessagesSent: todayMessagesSent?.count || 0,
      todayDataUsed: todayDataUsage?.kilobytes || 0,
      weeklyMoolaUsed,
    }
  },
})

export const getEmergencyMoolaStatus = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) return { available: false, messagesUsed: 0 }

    // Check if user has used emergency Moola before
    const emergencyMoolaRecord = await ctx.db
      .query("emergencyMoola")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first()

    if (!emergencyMoolaRecord) {
      // User has never used emergency Moola
      return { available: true, messagesUsed: 0 }
    }

    // Check if user has used all emergency messages
    if (emergencyMoolaRecord.messagesUsed >= MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) {
      return { available: false, messagesUsed: emergencyMoolaRecord.messagesUsed }
    }

    return {
      available: true,
      messagesUsed: emergencyMoolaRecord.messagesUsed,
    }
  },
})

export const useEmergencyMoola = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) throw new Error("User not found")

    // Check if user has used emergency Moola before
    const emergencyMoolaRecord = await ctx.db
      .query("emergencyMoola")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first()

    if (emergencyMoolaRecord) {
      // User has used emergency Moola before
      if (emergencyMoolaRecord.messagesUsed >= MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) {
        throw new Error("Emergency Moola already used")
      }

      // Update the record
      await ctx.db.patch(emergencyMoolaRecord._id, {
        lastUsed: Date.now(),
      })

      return { success: true }
    }

    // Create a new emergency Moola record
    await ctx.db.insert("emergencyMoola", {
      userId: args.userId,
      messagesUsed: 0,
      activated: Date.now(),
      lastUsed: Date.now(),
    })

    // Record transaction for the emergency Moola grant
    await ctx.db.insert("moolaTransactions", {
      userId: args.userId,
      amount: MOOLA_RATES.EMERGENCY_MOOLA_AMOUNT,
      type: "emergency_grant",
      description: `Emergency Moola grant (${MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES} messages)`,
      timestamp: Date.now(),
    })

    return { success: true }
  },
})

export const purchaseMoola = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    paymentMethod: v.union(v.literal("sms"), v.literal("card"), v.literal("airtime")),
    transactionReference: v.optional(v.string()),
    paymentVerified: v.optional(v.boolean()), // For development only, remove in production
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId)
    if (!user) throw new Error("User not found")

    // IMPORTANT: In a production environment, you should verify the payment
    // through a secure webhook or server-side verification before crediting Moola
    // This is just a placeholder for development purposes

    // For development/testing only - in production, this should be replaced with proper payment verification
    const isPaymentVerified = args.paymentVerified || false

    if (!isPaymentVerified) {
      // In production, this should be handled by a separate webhook endpoint
      // that verifies the payment and then credits the user's account
      console.warn("Payment not verified. In production, this should be handled securely.")

      // For development, we'll allow the purchase to proceed with a warning
      // In production, you should throw an error here or implement proper verification
    }

    // Add Moola to user's balance
    await ctx.db.patch(args.userId, {
      moola: user.moola + args.amount,
      updatedAt: Date.now(),
    })

    // Record transaction
    await ctx.db.insert("moolaTransactions", {
      userId: args.userId,
      amount: args.amount,
      type: "purchase",
      description: `Purchased ${args.amount} Moola via ${args.paymentMethod}`,
      timestamp: Date.now(),
      // Store the transaction reference if provided
      ...(args.transactionReference ? { transactionReference: args.transactionReference } : {}),
    })

    return { success: true, newBalance: user.moola + args.amount }
  },
})

export const recordDataUsage = mutation({
  args: {
    userId: v.id("users"),
    bytes: v.number(),
    description: v.string(),
    isEmergency: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { userId, bytes, description, isEmergency = false } = args

    // Get user
    const user = await ctx.db.get(userId)
    if (!user) throw new Error("User not found")

    // Calculate cost based on data usage
    const kilobytes = bytes / 1024
    const cost = Math.ceil(kilobytes * MOOLA_RATES.DATA_PER_KB * 10) / 10

    if (isEmergency) {
      // Using emergency Moola - update the counter
      const emergencyMoolaRecord = await ctx.db
        .query("emergencyMoola")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first()

      if (!emergencyMoolaRecord) {
        // If emergency record doesn't exist, create it
        await ctx.db.insert("emergencyMoola", {
          userId,
          messagesUsed: 0,
          activated: Date.now(),
          lastUsed: Date.now(),
        })
      } else {
        // Check if user has used all emergency messages
        if (emergencyMoolaRecord.messagesUsed >= MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) {
          throw new Error("Emergency Moola depleted")
        }

        // Update the record
        await ctx.db.patch(emergencyMoolaRecord._id, {
          lastUsed: Date.now(),
        })
      }
    } else {
      // Check if user has enough Moola
      if (user.moola < cost) {
        throw new Error("Insufficient Moola balance for data usage")
      }

      // Deduct Moola
      await ctx.db.patch(userId, {
        moola: user.moola - cost,
        updatedAt: Date.now(),
      })

      // Record transaction
      await ctx.db.insert("moolaTransactions", {
        userId,
        amount: -cost,
        type: "data_usage",
        description: `Data usage: ${description} (${kilobytes.toFixed(1)} KB)`,
        timestamp: Date.now(),
      })
    }

    // Update data usage stats
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayTimestamp = today.getTime()

    const existingUsage = await ctx.db
      .query("userDataUsage")
      .withIndex("by_user_and_date", (q) => q.eq("userId", userId).eq("date", todayTimestamp))
      .first()

    if (existingUsage) {
      await ctx.db.patch(existingUsage._id, {
        kilobytes: existingUsage.kilobytes + kilobytes,
        updatedAt: Date.now(),
      })
    } else {
      await ctx.db.insert("userDataUsage", {
        userId,
        date: todayTimestamp,
        kilobytes,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
    }

    return {
      success: true,
      cost,
      newBalance: isEmergency ? user.moola : user.moola - cost,
    }
  },
})

export const recordMessageSent = mutation({
  args: {
    userId: v.id("users"),
    contentLength: v.number(),
    hasAttachments: v.optional(v.boolean()),
    isEmergency: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { userId, contentLength, hasAttachments = false, isEmergency = false } = args

    // Get user
    const user = await ctx.db.get(userId)
    if (!user) throw new Error("User not found")

    // Calculate cost based on message content
    let cost = MOOLA_RATES.DATA_PER_MESSAGE

    // Add cost for longer messages
    if (contentLength > 500) {
      cost += Math.floor((contentLength - 500) / 500) * 0.1
    }

    // Add cost for attachments
    if (hasAttachments) {
      cost += MOOLA_RATES.IMAGE_UPLOAD
    }

    // Round to 1 decimal place
    cost = Math.ceil(cost * 10) / 10

    if (isEmergency) {
      // Using emergency Moola - update the counter
      const emergencyMoolaRecord = await ctx.db
        .query("emergencyMoola")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .first()

      if (!emergencyMoolaRecord) {
        // If emergency record doesn't exist, create it and grant emergency Moola
        await ctx.db.insert("emergencyMoola", {
          userId,
          messagesUsed: 1, // Start with 1 since we're sending a message now
          activated: Date.now(),
          lastUsed: Date.now(),
        })

        // Record transaction for the emergency Moola grant
        await ctx.db.insert("moolaTransactions", {
          userId,
          amount: MOOLA_RATES.EMERGENCY_MOOLA_AMOUNT,
          type: "emergency_grant",
          description: `Emergency Moola grant (${MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES} messages)`,
          timestamp: Date.now(),
        })
      } else {
        // Check if user has used all emergency messages
        if (emergencyMoolaRecord.messagesUsed >= MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) {
          throw new Error("Emergency Moola depleted")
        }

        // Update the record
        await ctx.db.patch(emergencyMoolaRecord._id, {
          messagesUsed: emergencyMoolaRecord.messagesUsed + 1,
          lastUsed: Date.now(),
        })
      }
    } else {
      // Check if user has enough Moola
      if (user.moola < cost) {
        throw new Error("Insufficient Moola balance for sending message")
      }

      // Deduct Moola
      await ctx.db.patch(userId, {
        moola: user.moola - cost,
        updatedAt: Date.now(),
      })

      // Record transaction
      await ctx.db.insert("moolaTransactions", {
        userId,
        amount: -cost,
        type: "message_cost",
        description: `Message sent (${contentLength} chars${hasAttachments ? " with attachment" : ""})`,
        timestamp: Date.now(),
      })
    }

    // Update message stats
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayTimestamp = today.getTime()

    const existingStats = await ctx.db
      .query("userMessageStats")
      .withIndex("by_user_and_date", (q) => q.eq("userId", userId).eq("date", todayTimestamp))
      .first()

    if (existingStats) {
      await ctx.db.patch(existingStats._id, {
        count: existingStats.count + 1,
        updatedAt: Date.now(),
      })
    } else {
      await ctx.db.insert("userMessageStats", {
        userId,
        date: todayTimestamp,
        count: 1,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
    }

    return {
      success: true,
      cost,
      newBalance: isEmergency ? user.moola : user.moola - cost,
    }
  },
})

export const recordActiveTime = mutation({
  args: {
    userId: v.id("users"),
    minutes: v.number(),
  },
  handler: async (ctx, args) => {
    const { userId, minutes } = args

    // Get user
    const user = await ctx.db.get(userId)
    if (!user) throw new Error("User not found")

    // Calculate cost based on active time
    const cost = Math.ceil(minutes * MOOLA_RATES.DATA_PER_MINUTE_ACTIVE * 10) / 10

    // Check if user has enough Moola
    if (user.moola < cost) {
      throw new Error("Insufficient Moola balance for active time")
    }

    // Deduct Moola
    await ctx.db.patch(userId, {
      moola: user.moola - cost,
      updatedAt: Date.now(),
    })

    // Record transaction
    await ctx.db.insert("moolaTransactions", {
      userId,
      amount: -cost,
      type: "data_usage",
      description: `Active time: ${minutes.toFixed(1)} minutes`,
      timestamp: Date.now(),
    })

    return { success: true, cost, newBalance: user.moola - cost }
  },
})

export const processMarketplaceTransaction = mutation({
  args: {
    sellerId: v.id("users"),
    buyerId: v.id("users"),
    itemName: v.string(),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const { sellerId, buyerId, itemName, amount } = args

    // Get buyer
    const buyer = await ctx.db.get(buyerId)
    if (!buyer) throw new Error("Buyer not found")

    // Get seller
    const seller = await ctx.db.get(sellerId)
    if (!seller) throw new Error("Seller not found")

    // Check if buyer has enough Moola
    if (buyer.moola < amount) {
      throw new Error("Insufficient Moola balance")
    }

    // Calculate marketplace fee
    const fee = Math.ceil(((amount * MOOLA_RATES.TRADEPOST_FEE_PERCENT) / 100) * 10) / 10
    const sellerAmount = amount - fee

    // Deduct from buyer
    await ctx.db.patch(buyerId, {
      moola: buyer.moola - amount,
      updatedAt: Date.now(),
    })

    // Add to seller
    await ctx.db.patch(sellerId, {
      moola: seller.moola + sellerAmount,
      updatedAt: Date.now(),
    })

    const now = Date.now()

    // Record buyer transaction
    await ctx.db.insert("moolaTransactions", {
      userId: buyerId,
      amount: -amount,
      type: "purchase",
      description: `Purchased: ${itemName}`,
      relatedUserId: sellerId,
      timestamp: now,
    })

    // Record seller transaction
    await ctx.db.insert("moolaTransactions", {
      userId: sellerId,
      amount: sellerAmount,
      type: "purchase",
      description: `Sold: ${itemName} (Fee: ${fee} Moola)`,
      relatedUserId: buyerId,
      timestamp: now,
    })

    return {
      success: true,
      buyerNewBalance: buyer.moola - amount,
      sellerNewBalance: seller.moola + sellerAmount,
      fee,
    }
  },
})

export const giftMoola = mutation({
  args: {
    senderId: v.id("users"),
    recipientId: v.id("users"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    const { senderId, recipientId, amount } = args

    if (amount <= 0) {
      throw new Error("Amount must be greater than 0")
    }

    // Get sender
    const sender = await ctx.db.get(senderId)
    if (!sender) {
      throw new Error("Sender not found")
    }

    // Check if sender has enough Moola
    if (sender.moola < amount) {
      throw new Error("Insufficient Moola balance")
    }

    // Get recipient
    const recipient = await ctx.db.get(recipientId)
    if (!recipient) {
      throw new Error("Recipient not found")
    }

    const now = Date.now()

    // Deduct Moola from sender
    await ctx.db.patch(senderId, {
      moola: sender.moola - amount,
      updatedAt: now,
    })

    // Add Moola to recipient
    await ctx.db.patch(recipientId, {
      moola: recipient.moola + amount,
      updatedAt: now,
    })

    // Record transaction for sender
    await ctx.db.insert("moolaTransactions", {
      userId: senderId,
      amount: -amount,
      type: "gift_sent",
      description: `Gifted ${amount} Moola to ${recipient.name}`,
      relatedUserId: recipientId,
      timestamp: now,
    })

    // Record transaction for recipient
    await ctx.db.insert("moolaTransactions", {
      userId: recipientId,
      amount: amount,
      type: "gift_received",
      description: `Received ${amount} Moola gift from ${sender.name}`,
      relatedUserId: senderId,
      timestamp: now,
    })

    return { success: true }
  },
})

