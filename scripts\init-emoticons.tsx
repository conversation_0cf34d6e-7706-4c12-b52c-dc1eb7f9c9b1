"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"

export default function InitEmoticons() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const initializeEmoticons = useMutation(api.emoticons.initializeDefaultEmoticons)

  const handleInitialize = async () => {
    setLoading(true)
    try {
      const result = await initializeEmoticons({})
      setResult(result)
    } catch (error) {
      setResult({ error: error instanceof Error ? error.message : "Unknown error" })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Initialize MXit Emoticons</CardTitle>
          <CardDescription>
            This will populate the database with the default MXit emoticons. Only run this once.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {result && <pre className="bg-muted p-4 rounded-md overflow-auto">{JSON.stringify(result, null, 2)}</pre>}
        </CardContent>
        <CardFooter>
          <Button onClick={handleInitialize} disabled={loading}>
            {loading ? "Initializing..." : "Initialize Emoticons"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

