// "use client"

// import { useState } from "react"
// import { useChats } from "@/hooks/use-chats"
// import { useAuth } from "@/hooks/use-auth"
// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// export function DebugChat() {
//   const [isOpen, setIsOpen] = useState(false)
//   const { userId, isLoading: isAuthLoading } = useAuth()
//   const { chats, isLoading: isChatsLoading } = useChats()

//   if (!isOpen) {
//     return (
//       <Button
//         className="fixed top-20 right-4 z-50 opacity-50"
//         size="sm"
//         variant="outline"
//         onClick={() => setIsOpen(true)}
//       >
//         Debug
//       </Button>
//     )
//   }

//   return (
//     <Card className="fixed top-20 right-4 z-50 w-80 max-h-[80vh] overflow-auto">
//       <CardHeader className="py-2">
//         <CardTitle className="text-sm flex justify-between">
//           <span>Chat Debug</span>
//           <Button size="sm" variant="ghost" onClick={() => setIsOpen(false)}>
//             Close
//           </Button>
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="text-xs space-y-2">
//         <div>
//           <p>
//             <strong>Auth:</strong> {isAuthLoading ? "Loading..." : "Loaded"}
//           </p>
//           <p>
//             <strong>User ID:</strong> {userId || "None"}
//           </p>
//         </div>
//         <div>
//           <p>
//             <strong>Chats:</strong> {isChatsLoading ? "Loading..." : "Loaded"}
//           </p>
//           <p>
//             <strong>Chat Count:</strong> {chats?.length || 0}
//           </p>
//         </div>
//         {chats && chats.length > 0 && (
//           <div>
//             <p>
//               <strong>Chat List:</strong>
//             </p>
//             <ul className="pl-4 list-disc">
//               {chats.map((chat, index) => (
//                 <li key={index}>
//                   {chat.type === "direct"
//                     ? `Direct: ${chat.participants.find((p) => p.id !== userId)?.name || "Unknown"}`
//                     : `Group: ${chat.name || "Unnamed"}`}
//                 </li>
//               ))}
//             </ul>
//           </div>
//         )}
//         <Button size="sm" className="w-full" onClick={() => window.location.reload()}>
//           Reload Page
//         </Button>
//       </CardContent>
//     </Card>
//   )
// }

