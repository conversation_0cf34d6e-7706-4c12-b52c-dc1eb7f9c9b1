"use client"

import { useQuery, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useState, useEffect } from "react"
import { useAuth, useUser } from "@clerk/nextjs"
import { useToast } from "@/components/ui/use-toast"

export function useConvexUser() {
  const [userId, setUserId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()

  const { isLoaded: isClerkLoaded, userId: clerkId, isSignedIn } = useAuth()
  const { user: clerkUser, isLoaded: isUserLoaded } = useUser()

  const storeUser = useMutation(api.auth.storeUser)
  const existingUser = useQuery(api.users.getCurrentUser, isSignedIn ? undefined : "skip")

  useEffect(() => {
    async function createOrGetUser() {
      if (!isClerkLoaded || !isUserLoaded) {
        setIsLoading(true)
        return
      }

      if (!isSignedIn || !clerkId) {
        setIsLoading(false)
        setUserId(null)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        if (existingUser) {
          setUserId(existingUser._id)
        } else if (clerkUser) {
          // Create user in Convex if they don't exist
          const newUserId = await storeUser({
            clerkId,
            name:
              clerkUser.firstName && clerkUser.lastName
                ? `${clerkUser.firstName} ${clerkUser.lastName}`
                : clerkUser.username || "New User",
            email: clerkUser.emailAddresses[0]?.emailAddress,
            avatar: clerkUser.imageUrl || "/placeholder.svg?height=200&width=200",
          })
          setUserId(newUserId)
        }
      } catch (err) {
        console.error("Error in createOrGetUser:", err)
        setError(err instanceof Error ? err : new Error("Failed to authenticate"))
        setUserId(null)

        toast({
          title: "Authentication Error",
          description: "There was a problem connecting to the server. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    createOrGetUser()
  }, [clerkId, clerkUser, existingUser, isClerkLoaded, isUserLoaded, isSignedIn, storeUser, toast])

  return {
    isLoading: isLoading || !isClerkLoaded || !isUserLoaded,
    isAuthenticated: Boolean(userId),
    userId,
    user: existingUser,
    error,
  }
}
