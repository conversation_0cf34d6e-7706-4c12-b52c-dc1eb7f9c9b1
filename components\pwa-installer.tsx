"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, X } from "lucide-react"

// Key for storing the dismissal status in localStorage
const DISMISS_STORAGE_KEY = "pwaInstallDismissed"

export function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  
  // No need for a separate isInstalled state, as we can just hide the prompt.
  // The 'appinstalled' event will handle this.

  useEffect(() => {
    // --- 1. Check if the user has already dismissed the prompt ---
    const hasDismissed = localStorage.getItem(DISMISS_STORAGE_KEY) === "true"
    if (hasDismissed) {
      console.log("Install prompt was previously dismissed by the user.")
      return // Exit early, don't show the prompt
    }

    // --- 2. Check if the app is already running in standalone mode ---
    const isStandalone =
      window.matchMedia("(display-mode: standalone)").matches ||
      (window.navigator as any).standalone ||
      document.referrer.includes("android-app://")

    if (isStandalone) {
      console.log("App is already installed.")
      return // Exit early, don't show the prompt
    }

    // --- 3. Listen for the `beforeinstallprompt` event (for non-iOS devices) ---
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
      console.log("'beforeinstallprompt' event fired. App is installable.")
    }

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt)

    // --- 4. Handle iOS devices, which don't fire the install event ---
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    if (isIOS && !isStandalone) {
      // For iOS, we can show the instruction banner immediately
      // as long as it's not installed and hasn't been dismissed.
      setIsInstallable(true)
    }

    // --- 5. Listen for the `appinstalled` event to hide the prompt after installation ---
    const handleAppInstalled = () => {
      console.log("PWA was installed successfully!")
      setIsInstallable(false) // Hide the prompt
      setDeferredPrompt(null) // Clear the prompt event
    }

    window.addEventListener("appinstalled", handleAppInstalled)

    // Cleanup listeners when the component unmounts
    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt)
      window.removeEventListener("appinstalled", handleAppInstalled)
    }
  }, []) // This effect runs only once on component mount

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice

    if (outcome === "accepted") {
      console.log("User accepted the install prompt.")
    } else {
      console.log("User dismissed the install prompt.")
    }

    // The prompt can only be used once, so we clear it
    setDeferredPrompt(null)
    setIsInstallable(false)
  }

  const handleDismiss = () => {
    // --- Key Change: Save the dismissal state permanently ---
    localStorage.setItem(DISMISS_STORAGE_KEY, "true")
    // Hide the prompt immediately
    setIsInstallable(false)
    console.log("User permanently dismissed the install prompt.")
  }

  // If the component is not installable (or has been dismissed/installed), render nothing
  if (!isInstallable) return null

  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)

  return (
    <div className="fixed bottom-4 right-4 z-[60] animate-in fade-in-50 slide-in-from-bottom-2">
      {isIOS ? (
        // iOS specific instructions with a dismiss button
        <div className="bg-white p-4 rounded-lg shadow-lg border border-gray-200 max-w-xs relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-1 right-1 h-6 w-6"
            onClick={handleDismiss}
            aria-label="Dismiss install instructions"
          >
            <X className="h-4 w-4" />
          </Button>
          <h3 className="font-bold text-lg mb-2">Install App</h3>
          <p className="text-sm text-gray-600 mb-3">
            To install on your iPhone:
          </p>
          <ol className="text-sm text-gray-600 list-decimal pl-5 mb-3 space-y-1">
            <li>Tap the <strong>Share</strong> icon in your browser menu.</li>
            <li>Scroll down and tap <strong>Add to Home Screen</strong>.</li>
          </ol>
        </div>
      ) : (
        // Standard PWA install button for other platforms
        <div className="flex items-center gap-2">
          <Button
            onClick={handleInstallClick}
            className="flex gap-2 bg-primary text-primary-foreground hover:bg-primary/90"
            size="lg"
          >
            <Download className="h-5 w-5" />
            Install App
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDismiss}
            className="bg-white rounded-full shadow"
            aria-label="Dismiss install button"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}