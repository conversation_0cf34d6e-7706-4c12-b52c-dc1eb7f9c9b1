"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useUser } from "@clerk/nextjs"
import { formatMoolaAmount } from "@/lib/moola-services"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Gift } from "lucide-react"

export function DailyReward() {
  const { user } = useUser()
  const { toast } = useToast()
  
  // Get user data
  const userData = useQuery(api.users.getUserByClerkId, {
    clerkId: user?.id || ""
  })
  
  // Claim reward mutation
  const claimReward = useMutation(api.moola.claimDailyReward)
  
  // Handle claim reward
  const handleClaimReward = async () => {
    if (!userData) return
    
    try {
      const result = await claimReward({ userId: userData._id })
      
      if (result.success) {
        toast({
          title: "Reward Claimed!",
          description: `You earned ${formatMoolaAmount(result.reward || 0)}! ${result.message || ''}`,
        })
      } else {
        toast({
          title: "Already Claimed",
          description: result.message,
          variant: "default"
        })
      }
    } catch (error) {
      console.error("Error claiming reward:", error)
      toast({
        title: "Error",
        description: "Failed to claim daily reward. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Calculate next reward
  const nextReward = userData?.lastLoginReward 
    ? new Date(userData.lastLoginReward + 24 * 60 * 60 * 1000)
    : new Date()
    
  const now = new Date()
  const canClaim = !userData?.lastLoginReward || 
    new Date(userData.lastLoginReward).toDateString() !== now.toDateString()
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2">
          <Gift className="h-5 w-5 text-yellow-500" />
          Daily Reward
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!userData ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-muted-foreground">Current Streak</p>
                <p className="text-2xl font-bold">{userData.loginStreak || 0} days</p>
              </div>
              
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Next Reward</p>
                <p className="text-sm font-medium">
                  {canClaim 
                    ? "Available now!" 
                    : `Available ${nextReward.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`}
                </p>
              </div>
            </div>
            
            <Button 
              className="w-full" 
              onClick={handleClaimReward}
              disabled={!canClaim}
            >
              {canClaim ? "Claim Daily Reward" : "Already Claimed"}
            </Button>
            
            <div className="text-xs text-center text-muted-foreground">
              {canClaim 
                ? `Earn up to ${formatMoolaAmount(5)} + bonus for streaks!`
                : "Come back tomorrow for more rewards!"}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
