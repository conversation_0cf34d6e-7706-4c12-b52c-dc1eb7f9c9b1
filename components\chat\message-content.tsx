"use client"

import { useState, useEffect } from "react"
import { useEmoticons } from "@/hooks/use-emoticons"

interface MessageContentProps {
  content: string
  isDeleted?: boolean
}

export function MessageContent({ content, isDeleted = false }: MessageContentProps) {
  const [mounted, setMounted] = useState(false)
  const { parseEmoticons } = useEmoticons()

  useEffect(() => {
    setMounted(true)
  }, [])

  if (isDeleted) {
    return <p className="italic text-muted-foreground">This message has been deleted</p>
  }

  if (!mounted) {
    // Return plain text during SSR to avoid hydration mismatch
    return <p>{content}</p>
  }

  // Parse emoticons in the message
  const parsedContent = parseEmoticons(content)

  return <div dangerouslySetInnerHTML={{ __html: parsedContent }} className="message-content" />
}

