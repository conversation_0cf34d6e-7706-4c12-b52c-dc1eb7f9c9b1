"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { useEmoticons } from "@/hooks/use-emoticons"
import { useToast } from "@/components/ui/use-toast"
import { MoolaIcon } from "@/components/moola-icon"
import { Lock } from "lucide-react"

interface EmoticonPickerProps {
  onEmojiSelect: (emoji: string) => void
}

export function EmoticonPicker({ onEmojiSelect }: EmoticonPickerProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [mounted, setMounted] = useState(false)
  const { toast } = useToast()

  const { allEmoticons, userEmoticons, purchaseEmoticon } = useEmoticons()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Create a set of purchased emoticon IDs for quick lookup
  const purchasedEmoticonIds = new Set(userEmoticons?.map((e) => e?._id) || [])

  // Filter emoticons by category
  const getEmoticonsByCategory = (category: string) => {
    if (!allEmoticons) return []
    return allEmoticons.filter((e) => e.category === category)
  }

  const handlePurchase = async (emoticonId: string, name: string, cost: number) => {
    try {
      const result = await purchaseEmoticon(emoticonId)

      if (result.success) {
        if (result.error) {
          toast({
            title: "Purchase Failed",
            description: result.error,
            variant: "destructive",
          })
        } else {
          toast({
            title: "Purchase Successful",
            description: `You purchased the ${name} emoticon for ${cost} Moola`,
          })
        }
      } else {
        toast({
          title: "Purchase Failed",
          description: result.error || "Failed to purchase emoticon",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }

  // Render an emoticon button
  const renderEmoticonButton = (emoticon: any) => {
    const isPremium = emoticon.isPremium
    const isOwned = !isPremium || purchasedEmoticonIds.has(emoticon._id)

    return (
      <Button
        key={emoticon._id}
        variant="ghost"
        size="sm"
        className="relative h-10 w-10 p-0 rounded-md hover:bg-muted"
        onClick={() => {
          if (isOwned) {
            onEmojiSelect(emoticon.code)
          } else {
            handlePurchase(emoticon._id, emoticon.name, emoticon.moolaCost || 0)
          }
        }}
      >
        {isPremium && !isOwned && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-md">
            <Lock className="h-3 w-3 text-white" />
            <div className="flex items-center ml-1">
              <MoolaIcon className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-white">{emoticon.moolaCost}</span>
            </div>
          </div>
        )}
        <img
          src={emoticon.imageUrl || "/placeholder.svg"}
          alt={emoticon.name}
          className="h-6 w-6"
          style={{ opacity: isOwned ? 1 : 0.7 }}
        />
      </Button>
    )
  }

  if (!mounted) {
    return null // Avoid hydration mismatch
  }

  return (
    <Card className="w-64 p-2 shadow-lg">
      <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic" className="text-xs">
            Basic
          </TabsTrigger>
          <TabsTrigger value="premium" className="text-xs">
            Premium
          </TabsTrigger>
          <TabsTrigger value="special" className="text-xs">
            Special
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-2">
          <div className="grid grid-cols-5 gap-1">{getEmoticonsByCategory("basic").map(renderEmoticonButton)}</div>
        </TabsContent>

        <TabsContent value="premium" className="mt-2">
          <div className="grid grid-cols-5 gap-1">{getEmoticonsByCategory("premium").map(renderEmoticonButton)}</div>
        </TabsContent>

        <TabsContent value="special" className="mt-2">
          <div className="grid grid-cols-5 gap-1">{getEmoticonsByCategory("special").map(renderEmoticonButton)}</div>
        </TabsContent>
      </Tabs>

      <div className="mt-2 pt-2 border-t text-xs text-muted-foreground">
        <p>Classic MXit emoticons</p>
      </div>
    </Card>
  )
}

