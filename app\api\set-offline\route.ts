"use server"

import { NextResponse } from "next/server"
import { ConvexHttpClient } from "convex/browser"
import { api } from "@/convex/_generated/api"

// Initialize the Convex client
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!)

export async function POST(req: Request) {
  try {
    const { userId } = await req.json()

    if (!userId) {
      return NextResponse.json({ success: false, error: "No user ID provided" }, { status: 400 })
    }

    // Set user status to offline
    await convex.mutation(api.users.updateUserStatus, {
      userId,
      status: "offline",
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error setting user offline:", error)
    return NextResponse.json({ success: false, error: String(error) }, { status: 500 })
  }
}

