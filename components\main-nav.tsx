// src/components/main-nav.jsx (or wherever your file is)
'use client'; // Good practice to explicitly declare if needed

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  MessageSquare,
  Users,
  User,
  Moon,
  Sun,
  LogOut,
  Settings,
  UserPlus,
  Activity,
  ShoppingCart // <-- Import the icon
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/hooks/use-auth"
import { useTheme } from "@/components/providers/theme-provider"
import { useClerk } from "@clerk/nextjs"
import { Badge } from "@/components/ui/badge"
import { MoolaIcon } from "@/components/moola-icon"
import { MixitLogo } from "@/components/mixit-logo"
import { DataModeIndicator } from "@/components/data-mode-indicator"
export function MainNav() {
  const pathname = usePathname()
  const router = useRouter()
  const { user } = useAuth() // Assuming this hook correctly provides user data client-side
  const { theme, setTheme } = useTheme()
  const { signOut } = useClerk()

  const routes = [
    {
      href: "/chat",
      label: "Chat",
      icon: MessageSquare,
      active: pathname === "/chat" || pathname?.startsWith("/chat/"),
    },
    {
      href: "/contacts",
      label: "Contacts",
      icon: UserPlus,
      active: pathname === "/contacts",
    },
    {
      href: "/online",
      label: "Online",
      icon: Activity,
      active: pathname === "/online",
    },
    {
      href: "/chatrooms",
      label: "Chatrooms",
      icon: Users,
      active: pathname === "/chatrooms",
    },
    // --- Add Tradepost Route Here ---
    {
      href: "/tradepost",
      label: "Tradepost",
      icon: ShoppingCart, // <-- Use the imported icon
      // Check base path and sub-paths (like /tradepost/new or /tradepost/[id])
      active: pathname === "/tradepost" || pathname?.startsWith("/tradepost/"),
    },
    // --- End of Added Route ---
    // Keep Profile route if desired, or remove if handled by dropdown
    // {
    //   href: "/profile",
    //   label: "Profile",
    //   icon: User,
    //   active: pathname === "/profile",
    // },
  ]

  return (
    <div className="flex h-16 items-center border-b px-4 bg-background">
      <Link href="/chat" className="flex items-center gap-2"> {/* Link logo to chat for logged-in users */}
        <img src="/icon-512.svg" alt="XITchat" className="h-8 w-8" />
        <span className="text-xl font-bold">XIT<span className="text-primary">chat</span></span>
      </Link>
      <div className="ml-auto flex items-center gap-4">
        {/* Data Mode Indicator */}
        <DataModeIndicator />
      </div>

      <div className="ml-auto flex items-center gap-4">
        {/* Moola balance - ensure user object is available client-side */}
        {user && ( // Conditionally render if user exists
            <div className="hidden md:flex items-center gap-2 bg-muted px-3 py-1 rounded-full">
            {/* <span className="text-xs font-medium">Moola:</span> */}
            {/* <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
                <MoolaIcon className="h-3 w-3 mr-1" />
                {user?.moola || 0}
            </Badge> */}
            </div>
        )}

        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center gap-1"> {/* Use nav tag, adjust gap */}
          {routes.map((route) => (
            <Button
              key={route.href}
              variant={route.active ? "secondary" : "ghost"} // Use secondary for active? Or keep default
              size="sm"
              onClick={() => router.push(route.href)}
              aria-current={route.active ? "page" : undefined} // Accessibility
            >
              <route.icon className="h-4 w-4 mr-2" />
              {route.label}
            </Button>
          ))}
        </nav>

        {/* Mobile navigation (still commented out) */}
        {/* <div className="flex md:hidden"> ... </div> */}

        {/* User Dropdown Menu - ensure user object is available client-side */}
        {user && ( // Conditionally render if user exists
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  {/* Ensure user.avatar and user.name are available */}
                  <AvatarImage src={user.avatar || undefined} alt={user.name || 'User'} />
                  <AvatarFallback>{user.name ? user.name.charAt(0).toUpperCase() : 'U'}</AvatarFallback>
                </Avatar>
                {/* Status Indicator (optional) - ensure user.status is available */}
                {user.status && (
                    <span
                        className={`absolute bottom-0 right-0 h-2 w-2 rounded-full ${
                        user.status === "online" ? "bg-green-500" : user.status === "away" ? "bg-yellow-500" : "bg-gray-500"
                        } border border-background`}
                    ></span>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56"> {/* Added width */}
              <div className="flex items-center justify-start gap-2 p-2">
                {/* Ensure user details are available */}
                <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar || undefined} alt={user.name || 'User'} />
                    <AvatarFallback>{user.name ? user.name.charAt(0).toUpperCase() : 'U'}</AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium truncate">{user.name || 'Username'}</p>
                  <p className="text-xs text-muted-foreground truncate">{user.email || '<EMAIL>'}</p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push("/profile")}>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/settings")}>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme(theme === "light" ? "dark" : "light")}>
                {theme === "light" ? (
                  <>
                    <Moon className="mr-2 h-4 w-4" />
                    <span>Dark Mode</span>
                  </>
                ) : (
                  <>
                    <Sun className="mr-2 h-4 w-4" />
                    <span>Light Mode</span>
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600 focus:text-red-600 cursor-pointer" // Added cursor-pointer
                onSelect={(e) => { // Use onSelect for better handling than onClick in DropdownMenuItem
                  e.preventDefault(); // Prevent default closing behavior if needed
                  signOut(() => router.push("/"));
                }}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )
}