const fs = require("fs")
const path = require("path")
const { createCanvas } = require("canvas")

// Define sizes and directory
const sizes = [16, 32, 72, 96, 128, 144, 152, 167, 180, 192, 384, 512]
const iconDir = path.join(__dirname, "..", "public", "icons")

// Ensure the directory exists
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true })
}

// Create icons for each size
for (const size of sizes) {
  const canvas = createCanvas(size, size)
  const ctx = canvas.getContext("2d")

  // Background
  ctx.fillStyle = "#1a1a2e"
  ctx.fillRect(0, 0, size, size)

  // Text
  ctx.fillStyle = "white"
  ctx.font = `bold ${size / 2}px Arial`
  ctx.textAlign = "center"
  ctx.textBaseline = "middle"
  ctx.fillText("XC", size / 2, size / 2)

  // Save as PNG
  const buffer = canvas.toBuffer("image/png")
  fs.writeFileSync(path.join(iconDir, `icon-${size}x${size}.png`), buffer)
  // console.log(`Created icon-${size}x${size}.png`)
}

// Create special icons
const specialIcons = [
  { name: "chat-icon-192x192.png", text: "CH", size: 192 },
  { name: "rooms-icon-192x192.png", text: "RM", size: 192 },
  { name: "offline-image.png", text: "Offline", size: 512, bg: "#f0f0f0", color: "#888888" },
]

for (const icon of specialIcons) {
  const canvas = createCanvas(icon.size, icon.size)
  const ctx = canvas.getContext("2d")

  // Background
  ctx.fillStyle = icon.bg || "#1a1a2e"
  ctx.fillRect(0, 0, icon.size, icon.size)

  // Text
  ctx.fillStyle = icon.color || "white"
  ctx.font = `bold ${icon.size / 4}px Arial`
  ctx.textAlign = "center"
  ctx.textBaseline = "middle"
  ctx.fillText(icon.text, icon.size / 2, icon.size / 2)

  // Save as PNG
  const buffer = canvas.toBuffer("image/png")
  fs.writeFileSync(path.join(iconDir, icon.name), buffer)
  // console.log(`Created ${icon.name}`)
}

// console.log("Icon generation complete.")

