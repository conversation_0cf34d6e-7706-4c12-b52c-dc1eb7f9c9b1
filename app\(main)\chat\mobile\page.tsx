"use client"

import { useRouter } from "next/navigation"
import { MobileChatList } from "@/components/chat/mobile-chat-list"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UserPlus } from "lucide-react"

export default function MobileChatPage() {
  const router = useRouter()

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-hidden">
        <MobileChatList />
      </div>

      <Button
        className="fixed bottom-20 right-4 rounded-full shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
        size="icon"
        onClick={() => router.push("/contacts")}
      >
        <UserPlus className="h-5 w-5" />
      </Button>
    </div>
  )
}

