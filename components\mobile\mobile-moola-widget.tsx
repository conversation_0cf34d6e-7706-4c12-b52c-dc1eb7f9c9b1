"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { MoolaIcon } from "@/components/moola-icon"
import { DataModeIndicator } from "@/components/data-mode-indicator"
import { useDataMode } from "@/hooks/use-data-mode"
import { useAuth } from "@/hooks/use-auth"
import { cn } from "@/lib/utils"
import { 
  Wifi, 
  WifiOff, 
  Zap, 
  TrendingUp, 
  MessageSquare, 
  Clock,
  Database
} from "lucide-react"

interface MobileMoolaWidgetProps {
  className?: string
  compact?: boolean
}

export function MobileMoolaWidget({ className, compact = false }: MobileMoolaWidgetProps) {
  const { user } = useAuth()
  const {
    dataMode,
    toggleDataMode,
    isDataModeAvailable,
    messagesRemaining,
    minutesRemaining,
    kbRemaining,
    usageStats,
    isLowMoolaBalance,
    emergencyMoolaAvailable
  } = useDataMode()

  const [showDetails, setShowDetails] = useState(false)

  const moolaBalance = user?.moola || 0
  const maxMoola = 1000 // For progress bar calculation
  const progressPercentage = Math.min((moolaBalance / maxMoola) * 100, 100)

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-center bg-muted px-2 py-1 rounded-full">
          <MoolaIcon className="h-4 w-4 text-yellow-500 mr-1" />
          <span className="text-sm font-medium tabular-nums">
            {moolaBalance.toFixed(1)}
          </span>
        </div>
        <DataModeIndicator />
      </div>
    )
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <MoolaIcon className="h-6 w-6 text-yellow-500" />
            <span className="font-bold text-lg tabular-nums">
              {moolaBalance.toFixed(1)}
            </span>
            <span className="text-sm text-muted-foreground">Moola</span>
          </div>
          
          <div className="flex items-center gap-2">
            {isLowMoolaBalance && (
              <Badge variant="destructive" className="text-xs">
                Low Balance
              </Badge>
            )}
            <DataModeIndicator />
          </div>
        </div>

        {/* Balance Progress */}
        <div className="mb-4">
          <Progress 
            value={progressPercentage} 
            className="h-2"
            style={{
              background: isLowMoolaBalance ? 'rgb(239 68 68 / 0.2)' : undefined
            }}
          />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>0</span>
            <span>{maxMoola}+</span>
          </div>
        </div>

        {/* Data Mode Toggle */}
        <div className="mb-4">
          <Button
            onClick={toggleDataMode}
            variant={dataMode === "mobile" ? "outline" : "default"}
            className="w-full"
            disabled={!isDataModeAvailable && !emergencyMoolaAvailable && dataMode === "mobile"}
          >
            {dataMode === "mobile" ? (
              <>
                <Wifi className="h-4 w-4 mr-2" />
                Switch to Moola Data
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 mr-2" />
                Switch to Mobile Data
              </>
            )}
          </Button>
        </div>

        {/* Usage Stats */}
        {dataMode !== "mobile" && (
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center p-2 bg-muted rounded-lg">
              <MessageSquare className="h-4 w-4 mx-auto mb-1 text-blue-500" />
              <div className="text-xs font-medium">{messagesRemaining}</div>
              <div className="text-xs text-muted-foreground">Messages</div>
            </div>
            
            <div className="text-center p-2 bg-muted rounded-lg">
              <Clock className="h-4 w-4 mx-auto mb-1 text-green-500" />
              <div className="text-xs font-medium">{minutesRemaining}m</div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            
            <div className="text-center p-2 bg-muted rounded-lg">
              <Database className="h-4 w-4 mx-auto mb-1 text-purple-500" />
              <div className="text-xs font-medium">{kbRemaining}KB</div>
              <div className="text-xs text-muted-foreground">Data</div>
            </div>
          </div>
        )}

        {/* Emergency Moola */}
        {emergencyMoolaAvailable && dataMode === "mobile" && isLowMoolaBalance && (
          <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium text-orange-700">
                Emergency Moola Available
              </span>
            </div>
            <p className="text-xs text-orange-600 mb-2">
              Get 5 free messages when you run out of data
            </p>
            <Button size="sm" variant="outline" className="w-full border-orange-300">
              Activate Emergency Moola
            </Button>
          </div>
        )}

        {/* Today's Usage */}
        {showDetails && (
          <div className="space-y-2 pt-3 border-t">
            <h4 className="text-sm font-medium">Today's Usage</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Moola Used:</span>
                <span className="font-medium">{usageStats.todayMoolaUsed.toFixed(1)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Messages:</span>
                <span className="font-medium">{usageStats.todayMessagesSent}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Data Used:</span>
                <span className="font-medium">{usageStats.todayDataUsed.toFixed(1)}KB</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Weekly:</span>
                <span className="font-medium">{usageStats.weeklyMoolaUsed.toFixed(1)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Toggle Details */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="w-full mt-2 text-xs"
        >
          {showDetails ? "Hide Details" : "Show Usage Details"}
          <TrendingUp className="h-3 w-3 ml-1" />
        </Button>
      </CardContent>
    </Card>
  )
}
