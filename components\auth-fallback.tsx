"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export function AuthFallback() {
  const router = useRouter()

  return (
    <div className="flex flex-col items-center justify-center h-screen p-4">
      <div className="max-w-md text-center space-y-4">
        <h1 className="text-2xl font-bold">Authentication Error</h1>
        <p>There was a problem connecting to the server. This could be due to:</p>
        <ul className="text-left list-disc pl-6 space-y-2">
          <li>Incorrect environment variables</li>
          <li>Authentication configuration issues</li>
          <li>Server connectivity problems</li>
        </ul>
        <div className="pt-4 space-y-2">
          <Button onClick={() => window.location.reload()} className="w-full">
            Retry
          </Button>
          <Button variant="outline" onClick={() => router.push("/")} className="w-full">
            Return to Home
          </Button>
        </div>
      </div>
    </div>
  )
}

