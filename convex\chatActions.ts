// convex/chatActions.ts
import { action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api"; // Import api to call other mutations/queries
import { Id } from "./_generated/dataModel"; // Ensure Id type is imported

export const initiateChatWithSeller = action({
  args: {
    listingId: v.id("listings"),
    sellerId: v.id("users"), // The internal Convex ID of the seller
  },
  handler: async (ctx, args): Promise<{ chatId: Id<"chats"> }> => { // Return chat ID object
    // 1. Verify current user authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      // It's often better to let the calling client handle redirect logic
      // Throwing an error is standard for actions if auth fails
      throw new Error("ACTION_ERROR: Authentication required.");
    }

    // 2. Get current user's internal Convex ID using runQuery
    const currentUser = await ctx.runQuery(api.users.getCurrentUser); // Uses the no-arg version
    if (!currentUser) {
        // This indicates a state mismatch (auth identity exists, but no DB user)
        console.error(`ACTION_ERROR: User not found in DB for Clerk ID: ${identity.subject}`);
        throw new Error("ACTION_ERROR: Current user record not found.");
    }
    const currentUserId = currentUser._id;

    // 3. Prevent contacting self
    if (currentUserId === args.sellerId) {
      console.warn(`ACTION_WARN: User ${currentUserId} attempted to initiate chat with self.`);
      // Throw an error the client can catch and display
      throw new Error("ACTION_ERROR: You cannot start a chat with yourself.");
    }

    // 4. Check if a direct chat already exists between these two users
    const participants = [currentUserId, args.sellerId].sort(); // Sort IDs for consistency
    // Call the findDirectChat query
    const existingChat = await ctx.runQuery(api.chats.findDirectChat, { participants });

    let chatId: Id<"chats">;

    // 5. Handle Existing or Create New Chat
    if (existingChat) {
      // --- Chat already exists ---
      // Correctly access the ID from the returned chat object
      chatId = existingChat._id;
      console.log(`Direct chat ${chatId} already exists between ${participants[0]} and ${participants[1]}.`);

      // Optional: Add a new message indicating renewed interest?
      // Depends on desired UX. Could send a system message or user message.
       try {
           const listing = await ctx.runQuery(api.listings.get, { listingId: args.listingId });
           if (listing) {
                // Example: Send a new message from the current user
                // await ctx.runMutation(api.messages.sendMessage, {
                //     chatId: chatId,
                //     content: `Hi, I'm still interested in your listing: "${listing.title}"`,
                //     listingRefId: args.listingId
                // });
                // OR send a system message
                 await ctx.runMutation(api.messages.sendSystemMessage, {
                    chatId: chatId,
                    content: `Renewed inquiry about listing: "${listing.title}"`,
                    listingRefId: args.listingId
                 });
           }
       } catch(error) {
           console.error(`Failed to send follow-up message/fetch listing in existing chat ${chatId}: ${error}`);
           // Decide if this error should prevent returning the chatId
       }

    } else {
      // --- Create a new direct chat ---
      console.log(`Creating new direct chat between ${participants[0]} and ${participants[1]}.`);
      // Call the createDirectChat mutation, passing the sorted participants array
      chatId = await ctx.runMutation(api.chats.createDirectChat, { participants });

       // Send an initial message referencing the listing
       try {
            const listing = await ctx.runQuery(api.listings.get, { listingId: args.listingId });
            if (listing) {
                await ctx.runMutation(api.messages.sendSystemMessage, {
                    chatId: chatId,
                    content: `Inquiry about listing: "${listing.title}"`,
                    listingRefId: args.listingId
                });
            } else {
                 console.warn(`Could not fetch listing details (${args.listingId}) for initial message in new chat ${chatId}.`);
                 // Send a generic initial message?
                 await ctx.runMutation(api.messages.sendSystemMessage, { chatId: chatId, content: `Chat started regarding a listing.` });
            }
        } catch (error) {
             console.error(`Failed to fetch listing or send initial message for new chat ${chatId}: ${error}`);
             // Chat was created, but initial message failed. Return chatId anyway?
        }
    }

    // 6. Return the chat ID (either existing or newly created)
    return { chatId };
  },
});