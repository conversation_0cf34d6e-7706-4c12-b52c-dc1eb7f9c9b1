import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Video, Phone, ArrowLeft, MoreVertical, Circle } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Chat } from "../types";
import { useRouter } from "next/navigation";
import { useOnlineUsers } from "@/hooks/use-online-users";
import { Id } from "@/convex/_generated/dataModel";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";

interface ChatHeaderProps {
  chat: Chat | null | undefined;
  onVideoCall: () => void;
  onVoiceCall: () => void;
  onBack: () => void;
  isMobile: boolean;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  chat,
  onVideoCall,
  onVoiceCall,
  onBack,
  isMobile,
}) => {
  const router = useRouter();
  
  // Get online status for chat participants
  const participantIds = chat?.participants
    ?.filter((p): p is { _id: Id<"users">; name: string } => p !== null && p._id !== undefined)
    .map(p => p._id) || [];
    
  const { isUserOnline, getStatusText, getUserStatus, onlineUsers } = useOnlineUsers(participantIds);
  
  // Debug log the participant IDs and online status
  useEffect(() => {
    if (chat?.type === 'direct' && participantIds.length > 0) {
      const otherParticipant = getOtherParticipant();
      const participantId = otherParticipant?._id;
      if (participantId) {
        console.log('[ChatHeader] Participant status:', {
          participantId,
          isOnline: isUserOnline(participantId),
          status: getStatusText(participantId),
          onlineUsers: Array.from(onlineUsers)
        });
      }
    }
  }, [chat, participantIds, isUserOnline, getStatusText, onlineUsers]);
  
  // Get the current user's ID from auth context with a safe fallback
  const auth = useAuth?.();
  const currentUserId = auth?.userId || '';

  const getOtherParticipant = () => {
    if (!chat || chat.type !== 'direct' || !chat.participants?.length) return null;
    
    // Find the participant who is not the current user
    const otherParticipant = chat.participants.find(p => p && p._id !== currentUserId);
    
    // If we can't find the other participant (shouldn't happen in a direct chat),
    // return the first participant that's not null
    return otherParticipant || chat.participants.find(p => p) || null;
  };

  const getChatTitle = () => {
    if (!chat) return 'Chat';
    if (chat.type === 'direct') {
      // In a direct chat, show the other user's name
      const otherUser = getOtherParticipant();
      return otherUser?.name || 'Unknown User';
    }
    // For group chats, use the chat name or a default
    return chat.name || 'Group Chat';
  };

  const getChatAvatar = () => {
    if (!chat) return null;
    if (chat.type === 'direct') {
      const otherUser = getOtherParticipant();
      if (!otherUser) return null;
      
      const isOnline = otherUser._id ? isUserOnline(otherUser._id) : false;
      
      return (
        <div className="relative">
          <Avatar className="h-10 w-10">
            <AvatarImage src={otherUser.avatar} alt={otherUser.name} />
            <AvatarFallback className="text-sm">
              {otherUser.name?.substring(0, 2).toUpperCase() || '?'}
            </AvatarFallback>
          </Avatar>
          {chat.type === 'direct' && otherUser._id && (
            <span 
              className={cn(
                "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
                isOnline ? "bg-green-500" : "bg-gray-400"
              )}
              title={isOnline ? "Online" : "Offline"}
            />
          )}
        </div>
      );
    }
    // For group chats
    return (
      <Avatar className="h-10 w-10">
        <AvatarFallback className="text-sm">
          {getChatTitle().substring(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>
    );
  };

  return (
    <div className="flex items-center justify-between p-2 border-b h-14">
      <div className="flex items-center gap-2">
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        )}
        <div className="flex items-center space-x-2">
          {getChatAvatar()}
          <div>
            <h2 className="text-sm font-semibold">{getChatTitle()}</h2>
            {chat?.type === 'direct' && (() => {
              const otherParticipant = getOtherParticipant();
              const participantId = otherParticipant?._id;
              
              if (!participantId) return null;
              
              const isOnline = isUserOnline(participantId);
              const status = isOnline ? 'Online' : 'Offline';
              const statusColor = isOnline ? 'bg-green-500' : 'bg-gray-400';
              
              return (
                <p className="text-xs text-muted-foreground flex items-center">
                  <span 
                    className={cn(
                      "inline-block w-2 h-2 rounded-full mr-1",
                      statusColor
                    )}
                  />
                  {status}
                </p>
              );
            })()}
            {chat?.type === 'group' && (
              <p className="text-xs text-muted-foreground">
                {chat?.participants?.length || 0} members
              </p>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-1">
        <Button variant="ghost" size="icon" onClick={onVoiceCall}>
          <Phone className="h-5 w-5" />
        </Button>
        <Button variant="ghost" size="icon" onClick={onVideoCall}>
          <Video className="h-5 w-5" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/chat/${chat?._id}/info`)}>
              Chat Info
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push(`/chat/${chat?._id}/media`)}>
              View Media
            </DropdownMenuItem>
            <DropdownMenuItem className="text-destructive">
              Clear Chat
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
