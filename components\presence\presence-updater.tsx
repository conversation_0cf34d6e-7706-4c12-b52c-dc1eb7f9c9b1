"use client";

import { useEffect } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

export function PresenceUpdater() {
  const updatePresence = useMutation(api.presence.updatePresence);

  useEffect(() => {
    // Update presence immediately
    updatePresence({ status: "online" });
    
    // Set up interval to update presence every 30 seconds
    const interval = setInterval(() => {
      updatePresence({ status: "online" });
    }, 30000);
    
    // Update presence to away when window loses focus
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updatePresence({ status: "online" });
      } else {
        updatePresence({ status: "away" });
      }
    };
    
    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Cleanup
    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      // Update to offline when component unmounts
      updatePresence({ status: "offline" });
    };
  }, [updatePresence]);

  return null;
}
