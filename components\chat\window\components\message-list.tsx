import { useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { Separator } from "@/components/ui/separator";
import { Message, MessageWithSender } from "../types";
import { Id } from "@/convex/_generated/dataModel";
import { MessageItem } from "./message-item";
import { ScrollToBottomButton } from "./scroll-to-bottom-button";

interface MessageListProps {
  messages: Array<Message | MessageWithSender>;
  userId: string | null;
  onDeleteAction: (messageId: Id<"messages">) => Promise<void>;
  selectedMessageId: Id<"messages"> | null;
  setSelectedMessageId: (id: Id<"messages"> | null) => void;
  chatBackground: string;
  onScrollToBottom: () => void;
  isScrolledToBottom: boolean;
  newMessageCount: number;
  currentUser?: {
    name?: string;
    avatar?: string;
  };
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  userId,
  onDeleteAction,
  selectedMessageId,
  setSelectedMessageId,
  chatBackground,
  onScrollToBottom,
  isScrolledToBottom,
  newMessageCount,
  currentUser,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevMessagesLength = useRef(messages.length);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    if (messages.length === 0) return;
    
    const isNewMessage = messages.length > prevMessagesLength.current;
    prevMessagesLength.current = messages.length;

    if (isScrolledToBottom) {
      scrollToBottom('smooth');
    }
  }, [messages, isScrolledToBottom]);

  const scrollToBottom = (behavior: ScrollBehavior = 'auto') => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior });
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (!scrollAreaRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 50;
    
    if (isAtBottom !== isScrolledToBottom) {
      onScrollToBottom();
    }
  };

  // Group messages by date and ensure unique messages
  const groupedMessages: { [key: string]: Message[] } = {};
  const seenMessageIds = new Set<string>();
  
  // Process messages in reverse to keep the newest ones if there are duplicates
  [...messages].reverse().forEach((message) => {
    // Skip if we've already seen this message ID
    if (seenMessageIds.has(message._id)) return;
    
    // Add to seen set
    seenMessageIds.add(message._id);
    
    // Group by date
    const date = format(new Date(message.timestamp), 'yyyy-MM-dd');
    if (!groupedMessages[date]) {
      groupedMessages[date] = [];
    }
    
    // Add to the beginning of the array to maintain order after reverse
    groupedMessages[date].unshift(message);
  });
  
  // Sort the dates in ascending order
  const sortedGroupedMessages = Object.entries(groupedMessages)
    .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
    .reduce((acc, [date, msgs]) => {
      acc[date] = msgs;
      return acc;
    }, {} as { [key: string]: Message[] });

  return (
    <div className="relative flex-1">
      <ScrollArea
        ref={scrollAreaRef}
        className="h-full w-full"
        onScroll={handleScroll}
      >
        <div className="flex flex-col p-4 space-y-1">
          {Object.entries(sortedGroupedMessages).map(([date, dateMessages]) => (
            <div key={date} className="space-y-1">
              <div className="flex items-center justify-center my-4">
                <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
                  {format(new Date(date), 'MMMM d, yyyy')}
                </div>
              </div>
              {dateMessages.map((message) => {
                // Create a unique key using message ID, timestamp, and a small random string
                // to ensure uniqueness even with rapid messages
                const uniqueKey = `${message._id}-${message.timestamp}-${Math.random().toString(36).substring(2, 8)}`;
                return (
                  <MessageItem
                    key={uniqueKey}
                    message={message}
                    isCurrentUser={message.senderId === userId}
                    onDeleteAction={onDeleteAction}
                    isSelected={selectedMessageId === message._id}
                    onSelect={setSelectedMessageId}
                    currentUser={currentUser}
                  />
                );
              })}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <ScrollToBottomButton
        visible={!isScrolledToBottom && newMessageCount > 0}
        count={newMessageCount}
        onClick={() => {
          scrollToBottom('smooth');
          onScrollToBottom();
        }}
      />
    </div>
  );
};
