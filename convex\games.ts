import { v } from "convex/values"
import { query, mutation } from "./_generated/server"
import { Id } from "./_generated/dataModel"
import * as gameLogic from "./gameLogic"

// Game types and interfaces
type Player = {
  id: Id<"users">
  name: string
  avatar?: string
}

type GameMove = {
  player: Id<"users">
  move: any
  timestamp: number
}

// Create a new game
export const createGame = mutation({
  args: {
    gameType: v.union(
      v.literal("tictactoe"),
      v.literal("rps"),
      v.literal("dice"),
      v.literal("numberguess")
    ),
    chatId: v.id("chats"),
    player1Id: v.id("users"),
    player2Id: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const { gameType, chatId, player1Id, player2Id } = args
    const now = Date.now()
    
    // Initialize game data based on game type
    let gameData = {}
    if (gameType === "tictactoe") {
      gameData = {
        board: Array(3).fill(null).map(() => Array(3).fill(null)),
        moves: [],
      }
    } else if (gameType === "rps") {
      gameData = {
        player1Choice: null,
        player2Choice: null,
        result: null,
      }
    } else if (gameType === "dice") {
      gameData = {
        player1Roll: null,
        player2Roll: null,
        result: null,
      }
    } else if (gameType === "numberguess") {
      const targetNumber = Math.floor(Math.random() * 100) + 1
      gameData = {
        target: targetNumber,
        guesses: [],
        winner: null,
      }
    }

    // Create the game
    const gameId = await ctx.db.insert("games", {
      type: gameType,
      status: player2Id ? "in_progress" : "waiting",
      players: {
        x: player1Id,
        o: player2Id || null,
        currentPlayer: "x" as const,
      },
      chatId,
      gameData,
      createdAt: now,
      updatedAt: now,
    })

    return gameId
  },
})

// Get game by ID
export const getGame = query({
  args: { gameId: v.id("games") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.gameId)
  },
})

// Get active games in a chat
export const getChatGames = query({
  args: { chatId: v.id("chats") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("games")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
      .filter((q) => q.neq(q.field("status"), "completed"))
      .order("desc")
      .collect()
  },
})

// Make a move in a game
export const makeMove = mutation({
  args: {
    gameId: v.id("games"),
    playerId: v.id("users"),
    move: v.any(),
  },
  handler: async (ctx, args) => {
    const { gameId, playerId, move } = args
    const game = await ctx.db.get(gameId)
    
    if (!game) {
      throw new Error("Game not found")
    }
    
    if (game.status !== "in_progress") {
      throw new Error("Game is not in progress")
    }
    
    // Check if it's the player's turn
    const currentPlayer = game.players[game.players.currentPlayer]
    if (currentPlayer !== playerId) {
      throw new Error("It's not your turn")
    }
    
    // Process the move based on game type
    const updatedGame = { ...game }
    updatedGame.updatedAt = Date.now()
    
    switch (game.type) {
      case "tictactoe":
        await gameLogic.processTicTacToeMove(updatedGame, playerId, move)
        break
      case "rps":
        await gameLogic.processRPSMove(updatedGame, playerId, move)
        break
      case "dice":
        await gameLogic.processDiceRoll(updatedGame, playerId)
        break
      case "numberguess":
        await gameLogic.processNumberGuess(updatedGame, playerId, move)
        break
      default:
        throw new Error("Invalid game type")
    }
    
    // Update the game in the database
    await ctx.db.patch(gameId, updatedGame)
    
    return updatedGame
  },
})