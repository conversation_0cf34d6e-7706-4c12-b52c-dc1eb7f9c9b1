import { Id } from "@/convex/_generated/dataModel";

export interface CacheInvalidationOptions {
  aggressive?: boolean;
  cacheNamePrefix?: string;
}

export interface ChatSidebarProps {
  clearCacheOnNavigate?: boolean;
}

export interface ChatItemProps {
  chat: any; // Replace with proper type
  isActive: boolean;
  onChatSelect: (chatId: string) => void;
  onRemoveContact: (contactRelationshipId: string) => Promise<void>;
  currentUserId: string | null;
  contacts: any[]; // Replace with proper type
  isContactsReady: boolean;
}

export interface SearchHeaderProps {
  search: string;
  onSearchChange: (value: string) => void;
  onManageContacts: () => void;
}

export interface EmptyStatesProps {
  hasChats: boolean;
  hasSearchResults: boolean;
  searchQuery: string;
}
