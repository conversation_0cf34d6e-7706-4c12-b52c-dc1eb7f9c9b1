-- Add metadata column to chats table for storing additional chatroom data
ALTER TABLE chats ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Create function to update user status
CREATE OR REPLACE FUNCTION update_user_status()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE users
  SET status = 'online', last_seen = EXTRACT(EPOCH FROM NOW())::bigint
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user login
CREATE TRIGGER on_user_login
AFTER INSERT ON chat_participants
FOR EACH ROW
EXECUTE FUNCTION update_user_status();

-- Create index on metadata for faster searches
CREATE INDEX IF NOT EXISTS idx_chats_metadata ON chats USING GIN (metadata);

