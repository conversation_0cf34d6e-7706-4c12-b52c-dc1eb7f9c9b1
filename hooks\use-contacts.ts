"use client"

import { useState, useEffect } from "react"; // Import useEffect
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/components/ui/use-toast";
import { Id, Doc } from "@/convex/_generated/dataModel"; // Import Doc

// Define or import your Contact type (adjust based on actual data structure)
// This comes from the return type of api.contacts.getUserContacts
type ContactType = Doc<"users"> & {
    nickname?: string;
    isFavorite?: boolean;
    isBlocked?: boolean;
    contactRelationshipId: Id<"contacts">; // Ensure this is returned by getUserContacts
};

// Define or import your SearchResult type (adjust based on actual data structure)
// This comes from the return type of api.contacts.searchUsers
type SearchResultType = Doc<"users"> & {
    isContact: boolean;
};

export function useContacts() {
  const { userId, isLoading: isAuthLoading } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [isContactsLoading, setIsContactsLoading] = useState(true); // Separate loading state
  const [isSearchLoading, setIsSearchLoading] = useState(false); // Separate loading state


  // --- Get user's contacts ---
  // Conditionally run the query based on userId
  const contactsData = useQuery(
      api.contacts.getUserContacts,
      userId ? {} : "skip" // Run with empty args object if userId exists, else skip
  );

  // --- Search for users ---
  const shouldSearch = userId && searchQuery.length >= 2; // Condition to run search
  const searchResultsData = useQuery(
    api.contacts.searchUsers,
    shouldSearch ? { searchQuery } : "skip", // Pass args only if condition met
  );

  // --- Mutations ---
  const addContactMutation = useMutation(api.contacts.addContact);
  const removeContactMutation = useMutation(api.contacts.removeContact);
  const blockContactMutation = useMutation(api.contacts.blockContact);

  // --- Update Loading States ---
  useEffect(() => {
      // Contacts loading is true if auth is happening OR if the query should run but data is still undefined
      setIsContactsLoading(isAuthLoading || (!!userId && contactsData === undefined));
  }, [isAuthLoading, userId, contactsData]);

  useEffect(() => {
      // Search loading is true if the search *should* run but data is undefined
      setIsSearchLoading(shouldSearch && searchResultsData === undefined);
  }, [shouldSearch, searchResultsData]);


  // --- Actions ---
  const addContact = async (contactUserId: string, nickname?: string) => {
    // Use userId from the hook's scope
    if (!userId) {
        toast({ title: "Error", description: "You must be logged in.", variant: "destructive"});
        return;
    };

    try {
      await addContactMutation({
        contactUserId: contactUserId as Id<"users">, // Pass the ID of the user to add
        nickname,
        // isFavorite: false, // Default is likely handled in backend mutation
      });
      toast({
        title: "Contact Added",
        description: "Contact added successfully.",
      });
      setSearchQuery(""); // Clear search after adding
    } catch (error: any) {
      console.error("Error adding contact:", error);
      toast({
        title: "Error Adding Contact",
        description: error?.message || "Failed to add contact.",
        variant: "destructive",
      });
    }
  }

  const removeContact = async (contactRelationshipId: string) => {
    // Expects the ID of the *relationship* document
    if (!userId) {
        toast({ title: "Error", description: "You must be logged in.", variant: "destructive"});
        return;
    };
    if (!contactRelationshipId) {
        console.error("removeContact called without contactRelationshipId");
        toast({ title: "Internal Error", description: "Cannot remove contact: Missing ID.", variant: "destructive"});
        return;
    }

    try {
      await removeContactMutation({ contactRelationshipId: contactRelationshipId as Id<"contacts"> });
      toast({
        title: "Contact Removed",
        description: "Contact removed successfully.",
      });
    } catch (error: any) {
      console.error("Error removing contact:", error);
      toast({
        title: "Error Removing Contact",
        description: error?.message || "Failed to remove contact.",
        variant: "destructive",
      });
    }
  }

  const blockContact = async (contactRelationshipId: string, isBlocked: boolean) => {
      if (!userId) {
        toast({ title: "Error", description: "You must be logged in.", variant: "destructive"});
        return;
    };
     if (!contactRelationshipId) {
        console.error("blockContact called without contactRelationshipId");
        toast({ title: "Internal Error", description: "Cannot block contact: Missing ID.", variant: "destructive"});
        return;
    }

    try {
      await blockContactMutation({ contactRelationshipId: contactRelationshipId as Id<"contacts">, isBlocked });
      toast({
        title: isBlocked ? "Contact Blocked" : "Contact Unblocked",
        description: isBlocked
          ? "Messages from this contact will be hidden."
          : "You can now receive messages from this contact.",
      });
    } catch (error: any) {
      console.error("Error blocking/unblocking contact:", error);
      toast({
        title: "Error Updating Contact",
        description: error?.message || "Failed to update contact block status.",
        variant: "destructive",
      });
    }
  }

  // --- Return Value ---
  // Provide default empty arrays if data is still undefined (or skipped)
  const contactsList = (contactsData ?? []) as ContactType[];
  const searchResultsList = (searchResultsData ?? []) as SearchResultType[];

  return {
    contacts: contactsList,
    searchResults: searchResultsList,
    searchQuery,
    setSearchQuery,
    addContact,
    removeContact,
    blockContact,
    // The overall loading state depends primarily on contacts loading,
    // but you might consider search loading too depending on UI needs.
    isLoading: isContactsLoading,
    // Optionally expose search loading state separately if needed:
    // isSearching: isSearchLoading,
  }
}