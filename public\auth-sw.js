// Special service worker for authentication pages
// This service worker is minimal and doesn't interfere with auth redirects

const CACHE_NAME = "xitchat-auth-v1"

// Install event - cache minimal assets
self.addEventListener("install", (event) => {
  self.skipWaiting() // Activate immediately
})

// Activate event - take control immediately
self.addEventListener("activate", (event) => {
  self.clients.claim() // Take control of all clients immediately
})

// Fetch event - pass through all requests without caching
self.addEventListener("fetch", (event) => {
  // Skip handling all requests - let the browser handle them normally
  // This ensures auth redirects work properly
  return
})
