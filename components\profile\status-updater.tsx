"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Id } from "@/convex/_generated/dataModel"

export function StatusUpdater() {
  const { userId } = useAuth()
  const { toast } = useToast()
  const [statusMessage, setStatusMessage] = useState("")
  const [mood, setMood] = useState<string | null>(null)

  const updateUser = useMutation(api.users.updateUser)
  const userProfile = useQuery(api.users.getUserProfile, userId ? { userId: userId as Id<"users"> } : "skip")

  useEffect(() => {
    if (userProfile) {
      setStatusMessage(userProfile.statusMessage || "")
      setMood(userProfile.mood || null)
    }
  }, [userProfile])

  const moods = [
    { value: "happy", label: "Happy", icon: "😊", description: "Feeling good!" },
    { value: "sad", label: "Sad", icon: "😢", description: "Feeling down..." },
    { value: "excited", label: "Excited", icon: "🤩", description: "Can't wait!" },
    { value: "bored", label: "Bored", icon: "😒", description: "Nothing to do" },
    { value: "busy", label: "Busy", icon: "⏰", description: "No time to chat" },
    { value: "relaxed", label: "Relaxed", icon: "😌", description: "Taking it easy" },
    { value: "tired", label: "Tired", icon: "😴", description: "Need some rest" },
    { value: "angry", label: "Angry", icon: "😠", description: "Don't disturb!" },
    { value: "sick", label: "Sick", icon: "🤒", description: "Not feeling well" },
    { value: "love", label: "In Love", icon: "😍", description: "Feeling romantic" },
  ]

  const handleUpdateStatus = async () => {
    if (!userId) return

    try {
      await updateUser({
        userId: userId as Id<"users">,
        statusMessage: statusMessage.trim(),
        mood: mood as any || undefined,
      })

      toast({
        title: "Status updated",
        description: "Your status has been updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update status",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Update Your Status</CardTitle>
        <CardDescription>Let your contacts know what's on your mind</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="status-message">Status Message</Label>
          <Input
            id="status-message"
            placeholder="What's on your mind?"
            value={statusMessage}
            onChange={(e) => setStatusMessage(e.target.value)}
            maxLength={100}
          />
          <p className="text-xs text-muted-foreground text-right">
            {statusMessage.length}/100
          </p>
        </div>

        <div className="space-y-2">
          <Label>Mood</Label>
          <Tabs defaultValue={mood || "happy"} onValueChange={setMood} className="w-full">
            <TabsList className="grid grid-cols-5 h-auto">
              {moods.slice(0, 5).map((m) => (
                <TabsTrigger key={m.value} value={m.value} className="px-2 py-1">
                  <span className="text-xl">{m.icon}</span>
                </TabsTrigger>
              ))}
            </TabsList>
            <TabsList className="grid grid-cols-5 h-auto mt-1">
              {moods.slice(5, 10).map((m) => (
                <TabsTrigger key={m.value} value={m.value} className="px-2 py-1">
                  <span className="text-xl">{m.icon}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {moods.map((m) => (
              <TabsContent key={m.value} value={m.value} className="mt-2">
                <div className="flex items-center gap-2 p-2 rounded-md bg-muted/50">
                  <span className="text-2xl">{m.icon}</span>
                  <div>
                    <p className="font-medium">{m.label}</p>
                    <p className="text-sm text-muted-foreground">{m.description}</p>
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleUpdateStatus} className="w-full">
          Update Status
        </Button>
      </CardFooter>
    </Card>
  )
}
