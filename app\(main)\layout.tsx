import type { ReactNode } from "react"
import { MobileNav } from "@/components/mobile-nav"
import { MobileHeader } from "@/components/mobile-header"
import { OnlineStatusHeartbeat } from "@/components/online-status-heartbeat"
import { PWAInstaller } from "@/components/pwa-installer"
import { IOSInstallPrompt } from "@/components/ios-install-prompt"
import { MobileStatusIndicator } from "@/components/mobile/mobile-status-indicator"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { headers } from "next/headers"
import { MainNav } from "@/components/main-nav"

export default async function MainLayout({ children }: { children: ReactNode }) {
  const { userId } = await auth()

  if (!userId) {
    redirect("/sign-in")
  }

  // Check if we're on a mobile device and redirect to mobile chat
  const userAgent = (await headers()).get("user-agent") || ""
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)

  // If user is on mobile and trying to access /chat directly, redirect to mobile version
  const pathname = (await headers()).get("next-url") || ""
  if (isMobile && pathname === "/chat") {
    redirect("/chat/mobile")
  }

  return (
    <div className="flex flex-col h-screen ">
      <div className="hidden md:block">
        <MainNav />
      </div>
      <div className="md:hidden">
        <MobileHeader />
      </div>
      <div className="flex-1 overflow-y-auto p-4 md:p-6 pb-16 md:pb-6">{children}</div>
      <MobileNav />
      <OnlineStatusHeartbeat />
      <PWAInstaller />
      <IOSInstallPrompt />
      <MobileStatusIndicator />
    </div>
  )
}

