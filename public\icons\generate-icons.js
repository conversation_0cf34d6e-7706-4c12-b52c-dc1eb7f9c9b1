// This is a script to generate placeholder icons
// In a real app, you would replace these with your actual icons

const fs = require("fs")
const path = require("path")

const sizes = [16, 32, 72, 96, 128, 144, 152, 167, 180, 192, 384, 512]
const iconDir = path.join(__dirname)

// Ensure the directory exists
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true })
}

// Create a simple SVG for each size
sizes.forEach((size) => {
  const svg = `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" fill="#1a1a2e"/>
    <text x="${size / 2}" y="${size / 2 + size / 10}" font-family="Arial" font-size="${size / 2}" fill="white" text-anchor="middle">XC</text>
  </svg>`

  fs.writeFileSync(path.join(iconDir, `icon-${size}x${size}.svg`), svg)
  // console.log(`Created icon-${size}x${size}.svg`)
})

// Create a simple offline image placeholder
const offlineSvg = `<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="#f0f0f0"/>
  <text x="256" y="256" font-family="Arial" font-size="32" fill="#888888" text-anchor="middle">Image Unavailable</text>
</svg>`

fs.writeFileSync(path.join(iconDir, "offline-image.svg"), offlineSvg)
// console.log("Created offline-image.svg")

// console.log("Icon generation complete. Convert these SVGs to PNGs for production use.")

