"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Mic, Square, Send, Trash } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface VoiceRecorderProps {
  onVoiceRecorded: (audioBlob: Blob) => void
  onCancel: () => void
}

export function VoiceRecorder({ onVoiceRecorded, onCancel }: VoiceRecorderProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const { toast } = useToast()

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" })
        setAudioBlob(audioBlob)

        // Stop all tracks in the stream
        stream.getTracks().forEach((track) => track.stop())
      }

      mediaRecorder.start()
      setIsRecording(true)

      // Start timer
      let seconds = 0
      timerRef.current = setInterval(() => {
        seconds++
        setRecordingTime(seconds)

        // Limit recording to 60 seconds
        if (seconds >= 60) {
          stopRecording()
        }
      }, 1000)
    } catch (error) {
      console.error("Error accessing microphone:", error)
      toast({
        title: "Microphone Error",
        description: "Could not access your microphone. Please check permissions.",
        variant: "destructive",
      })
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }

  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }

    setAudioBlob(null)
    setRecordingTime(0)
    onCancel()
  }

  const sendVoiceNote = () => {
    if (audioBlob) {
      onVoiceRecorded(audioBlob)
      setAudioBlob(null)
      setRecordingTime(0)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <div className="flex flex-col items-center space-y-2 p-2 border rounded-lg bg-muted/30">
      {!audioBlob ? (
        <>
          <div className="text-center mb-2">
            {isRecording ? (
              <div className="text-red-500 animate-pulse font-medium">Recording... {formatTime(recordingTime)}</div>
            ) : (
              <div className="text-sm text-muted-foreground">Press the mic button to start recording</div>
            )}
          </div>
          <div className="flex gap-2">
            {isRecording ? (
              <Button variant="destructive" size="icon" onClick={stopRecording}>
                <Square className="h-4 w-4" />
              </Button>
            ) : (
              <Button variant="default" size="icon" onClick={startRecording}>
                <Mic className="h-4 w-4" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={cancelRecording}>
              Cancel
            </Button>
          </div>
        </>
      ) : (
        <>
          <div className="text-center mb-2">
            <div className="text-sm font-medium">Voice note recorded ({formatTime(recordingTime)})</div>
            <audio controls className="mt-2 h-8">
              <source src={URL.createObjectURL(audioBlob)} type="audio/webm" />
              Your browser does not support the audio element.
            </audio>
          </div>
          <div className="flex gap-2">
            <Button variant="default" size="icon" onClick={sendVoiceNote}>
              <Send className="h-4 w-4" />
            </Button>
            <Button variant="destructive" size="icon" onClick={cancelRecording}>
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

