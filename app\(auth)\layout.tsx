"use client"

import { ReactNode, useEffect } from "react"
import { Inter } from "next/font/google"
import { <PERSON><PERSON>rovider } from "@clerk/nextjs"
import { Toaster } from "@/components/ui/toaster"
import { cn } from "@/lib/utils"

const inter = Inter({ subsets: ["latin"] })

export default function AuthLayout({ children }: { children: ReactNode }) {
  // Unregister service workers on auth pages
  useEffect(() => {
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.getRegistrations().then((registrations) => {
        registrations.forEach((registration) => {
          registration.unregister();
          console.log("Unregistered service worker on auth page:", registration.scope);
        });
      });
    }
  }, []);

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Prevent PWA behavior on auth pages */}
        <meta name="apple-mobile-web-app-capable" content="no" />
        <meta name="mobile-web-app-capable" content="no" />
      </head>
      <body className={cn("min-h-screen bg-background antialiased", inter.className)}>
        <ClerkProvider>
          {children}
          <Toaster />
        </ClerkProvider>
      </body>
    </html>
  )
}
