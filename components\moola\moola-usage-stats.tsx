"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MoolaIcon } from "@/components/moola-icon"
import { formatMoolaAmount } from "@/lib/moola-services"
import { MessageSquare, Clock, Database } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useDataMode } from "@/hooks/use-data-mode"
import { Id } from "@/convex/_generated/dataModel"

export function MoolaUsageStats() {
  const { userId } = useAuth()
  const { usageStats } = useDataMode()

  // Get user's Moola balance
  const moolaBalance = useQuery(api.moola.getMoolaBalance, userId ? { userId: userId as Id<"users"> } : "skip") || 0

  // Get recent transactions
  const transactions = useQuery(api.moola.getTransactions, userId ? { userId: userId as Id<"users"> } : "skip") || []

  // Calculate usage by category
  const messageUsage = transactions
    .filter((t) => t.type === "message_cost")
    .reduce((sum, t) => sum + Math.abs(t.amount), 0)

  const dataUsage = transactions.filter((t) => t.type === "data_usage").reduce((sum, t) => sum + Math.abs(t.amount), 0)

  const activeTimeUsage = transactions
    .filter((t) => t.type === "tradepost_fee")
    .reduce((sum, t) => sum + Math.abs(t.amount), 0)

  const totalUsage = messageUsage + dataUsage + activeTimeUsage

  // Calculate percentages
  const messagePercent = totalUsage > 0 ? (messageUsage / totalUsage) * 100 : 0
  const dataPercent = totalUsage > 0 ? (dataUsage / totalUsage) * 100 : 0
  const activeTimePercent = totalUsage > 0 ? (activeTimeUsage / totalUsage) * 100 : 0

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <MoolaIcon className="h-5 w-5 mr-2 text-yellow-500" />
          Moola Usage Stats
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">Current Balance</h3>
            <p className="text-2xl font-bold flex items-center">
              <MoolaIcon className="h-5 w-5 mr-1 text-yellow-500" />
              {formatMoolaAmount(moolaBalance)}
            </p>
          </div>
          <div className="text-right">
            <h3 className="font-medium">Today's Usage</h3>
            <p className="text-2xl font-bold flex items-center justify-end">
              <MoolaIcon className="h-5 w-5 mr-1 text-yellow-500" />
              {formatMoolaAmount(usageStats.todayMoolaUsed)}
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="text-sm font-medium">Usage Breakdown</h4>

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <div className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2 text-blue-500" />
                <span>Messages</span>
              </div>
              <span>{formatMoolaAmount(messageUsage)} Moola</span>
            </div>
            <Progress value={messagePercent} className="h-2" />
          </div>

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <div className="flex items-center">
                <Database className="h-4 w-4 mr-2 text-green-500" />
                <span>Data Transfer</span>
              </div>
              <span>{formatMoolaAmount(dataUsage)} Moola</span>
            </div>
            <Progress value={dataPercent} className="h-2" />
          </div>

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-purple-500" />
                <span>Active Time</span>
              </div>
              <span>{formatMoolaAmount(activeTimeUsage)} Moola</span>
            </div>
            <Progress value={activeTimePercent} className="h-2" />
          </div>
        </div>

        <div className="pt-2 border-t">
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="bg-muted p-2 rounded-md">
              <p className="text-lg font-bold">{usageStats.todayMessagesSent}</p>
              <p className="text-xs">Messages Today</p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <p className="text-lg font-bold">{Math.round(usageStats.todayDataUsed / 1024)}</p>
              <p className="text-xs">MB Today</p>
            </div>
            <div className="bg-muted p-2 rounded-md">
              <p className="text-lg font-bold">{formatMoolaAmount(usageStats.weeklyMoolaUsed)}</p>
              <p className="text-xs">Weekly Usage</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

