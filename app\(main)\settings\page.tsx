"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/hooks/use-auth"
import { useDataMode } from "@/hooks/use-data-mode"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MoolaIcon } from "@/components/moola-icon"
import { SignOutButton } from "@clerk/nextjs"
import { 
  Bell, 
  ChevronRight, 
  HelpCircle, 
  Info, 
  Languages, 
  Lock, 
  LogOut, 
  Moon, 
  <PERSON>lette, 
  Shield, 
  Sun, 
  User, 
  Wif<PERSON> 
} from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"

export default function SettingsPage() {
  const router = useRouter()
  const { user, userId } = useAuth()
  const { theme, setTheme } = useTheme()
  const { dataMode, setDataMode } = useDataMode()
  const { toast } = useToast()
  
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)
  const [soundsEnabled, setSoundsEnabled] = useState(true)
  
  const handleRequestNotifications = async () => {
    try {
      const permission = await Notification.requestPermission()
      setNotificationsEnabled(permission === "granted")
      
      toast({
        title: permission === "granted" ? "Notifications enabled" : "Notifications disabled",
        description: permission === "granted" 
          ? "You will now receive notifications" 
          : "You will not receive notifications",
      })
    } catch (error) {
      console.error("Error requesting notifications:", error)
      toast({
        title: "Error",
        description: "Failed to request notification permissions",
        variant: "destructive",
      })
    }
  }
  
  return (
    <ScrollArea className="h-full">
      <div className="container max-w-2xl mx-auto py-6 px-4 pb-20">
        <h1 className="text-2xl font-bold mb-6">Settings</h1>
        
        {/* User Profile Section */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Profile</CardTitle>
            <CardDescription>Manage your account settings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={user?.avatar} alt={user?.name} />
                <AvatarFallback>{user?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-lg">{user?.name}</p>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
                <div className="flex items-center mt-1">
                  <MoolaIcon className="h-4 w-4 text-yellow-500 mr-1" />
                  <span className="text-sm">{user?.moola || 0} Moola</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Link href="/profile" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                <div className="flex items-center">
                  <User className="h-5 w-5 mr-3 text-primary" />
                  <span>Edit Profile</span>
                </div>
                <ChevronRight className="h-5 w-5 text-muted-foreground" />
              </Link>
              
              <Link href="/moola/buy" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
                <div className="flex items-center">
                  <MoolaIcon className="h-5 w-5 mr-3 text-yellow-500" />
                  <span>Buy Moola</span>
                </div>
                <ChevronRight className="h-5 w-5 text-muted-foreground" />
              </Link>
            </div>
          </CardContent>
        </Card>
        
        {/* Appearance Settings */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Appearance</CardTitle>
            <CardDescription>Customize how XITchat looks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {theme === "dark" ? (
                  <Moon className="h-5 w-5 text-primary" />
                ) : (
                  <Sun className="h-5 w-5 text-primary" />
                )}
                <Label htmlFor="theme">Theme</Label>
              </div>
              <Select
                value={theme}
                onValueChange={(value) => setTheme(value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-primary" />
                <Label htmlFor="chat-background">Chat Background</Label>
              </div>
              <Button variant="outline" size="sm" onClick={() => router.push("/settings/backgrounds")}>
                Customize
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Data Settings */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Data & Connectivity</CardTitle>
            <CardDescription>Manage data usage and connectivity settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Wifi className="h-5 w-5 text-primary" />
                <Label htmlFor="data-mode">Data Mode</Label>
              </div>
              <Select
                value={dataMode || "mobile"}
                onValueChange={(value) => setDataMode(value as "mobile" | "moola")}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Select mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mobile">Mobile Data</SelectItem>
                  <SelectItem value="moola">Use Moola</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Languages className="h-5 w-5 text-primary" />
                <Label htmlFor="language">Language</Label>
              </div>
              <Select defaultValue="en">
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="af">Afrikaans</SelectItem>
                  <SelectItem value="zu">Zulu</SelectItem>
                  <SelectItem value="xh">Xhosa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        {/* Notifications Settings */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Notifications</CardTitle>
            <CardDescription>Manage how you receive notifications</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-primary" />
                <Label htmlFor="notifications">Push Notifications</Label>
              </div>
              <Switch
                id="notifications"
                checked={notificationsEnabled}
                onCheckedChange={() => handleRequestNotifications()}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-primary" />
                <Label htmlFor="sounds">Sounds</Label>
              </div>
              <Switch
                id="sounds"
                checked={soundsEnabled}
                onCheckedChange={setSoundsEnabled}
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Privacy & Security */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Privacy & Security</CardTitle>
            <CardDescription>Manage your privacy and security settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Link href="/settings/privacy" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
              <div className="flex items-center">
                <Lock className="h-5 w-5 mr-3 text-primary" />
                <span>Privacy Settings</span>
              </div>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </Link>
            
            <Link href="/settings/blocked" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
              <div className="flex items-center">
                <Shield className="h-5 w-5 mr-3 text-primary" />
                <span>Blocked Users</span>
              </div>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </Link>
          </CardContent>
        </Card>
        
        {/* Help & Support */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle>Help & Support</CardTitle>
            <CardDescription>Get help and learn more about XITchat</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Link href="/help" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
              <div className="flex items-center">
                <HelpCircle className="h-5 w-5 mr-3 text-primary" />
                <span>Help Center</span>
              </div>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </Link>
            
            <Link href="/about" className="flex items-center justify-between p-2 rounded-md hover:bg-muted">
              <div className="flex items-center">
                <Info className="h-5 w-5 mr-3 text-primary" />
                <span>About XITchat</span>
              </div>
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            </Link>
          </CardContent>
        </Card>
        
        {/* Sign Out */}
        <Card>
          <CardContent className="pt-6">
            <SignOutButton>
              <Button variant="destructive" className="w-full">
                <LogOut className="h-5 w-5 mr-2" />
                Sign Out
              </Button>
            </SignOutButton>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  )
}
