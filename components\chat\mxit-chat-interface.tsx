"use client"

import { useState, useRef, useEffect } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { ArrowLeft, Phone, Video, MoreVertical, Users } from "lucide-react"
import { MxitMessageBubble } from "./mxit-message-bubble"
import { MxitChatInput } from "./mxit-chat-input"
import { MobileTypingIndicator } from "./mobile-typing-indicator"
import { formatDistanceToNow } from "date-fns"
import type { Message, User, Chat } from "@/lib/types"

interface MxitChatInterfaceProps {
  chat: Chat & {
    participants: User[]
    messages: Message[]
  }
  currentUser: User
  onSendMessage: (content: string, options?: any) => void
  onBack?: () => void
  onNudge?: () => void
  onGiftMoola?: () => void
  onMultiMix?: () => void
  onVoiceNote?: () => void
  onImageUpload?: () => void
  onCall?: () => void
  onVideoCall?: () => void
  typingUsers?: User[]
  isLoading?: boolean
}

export function MxitChatInterface({
  chat,
  currentUser,
  onSendMessage,
  onBack,
  onNudge,
  onGiftMoola,
  onMultiMix,
  onVoiceNote,
  onImageUpload,
  onCall,
  onVideoCall,
  typingUsers = [],
  isLoading = false
}: MxitChatInterfaceProps) {
  const [message, setMessage] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [chat.messages])

  // Get chat display info
  const getChatInfo = () => {
    if (chat.type === "chatroom") {
      return {
        name: chat.name || "Chatroom",
        avatar: null,
        status: `${chat.participants?.length || 0} members`,
        isOnline: true
      }
    }

    const otherUser = chat.participants?.find(p => p._id !== currentUser._id)
    if (otherUser) {
      return {
        name: otherUser.name,
        avatar: otherUser.avatar,
        status: otherUser.status === "online" 
          ? "Online" 
          : otherUser.lastSeen 
          ? `Last seen ${formatDistanceToNow(otherUser.lastSeen, { addSuffix: true })}`
          : "Offline",
        isOnline: otherUser.status === "online"
      }
    }

    return {
      name: "Unknown",
      avatar: null,
      status: "Offline",
      isOnline: false
    }
  }

  const chatInfo = getChatInfo()

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message)
      setMessage("")
    }
  }

  const handleVoiceNote = () => {
    setIsRecording(!isRecording)
    onVoiceNote?.()
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat Header */}
      <div className="flex items-center gap-3 p-4 border-b border-blue-200 mxit-header">
        {/* Back button */}
        {onBack && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-white hover:bg-blue-700"
            onClick={onBack}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        )}

        {/* Chat info */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {chat.type === "chatroom" ? (
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
          ) : (
            <Avatar className="h-10 w-10 mxit-avatar">
              <AvatarImage src={chatInfo.avatar} alt={chatInfo.name} />
              <AvatarFallback className="bg-blue-100 text-blue-700 font-bold">
                {chatInfo.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          )}

          <div className="flex-1 min-w-0">
            <h2 className="font-medium text-white truncate">{chatInfo.name}</h2>
            <p className={cn(
              "text-xs truncate",
              chatInfo.isOnline ? "text-green-200" : "text-blue-200"
            )}>
              {chatInfo.status}
            </p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-1">
          {chat.type !== "chatroom" && onCall && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white hover:bg-blue-700"
              onClick={onCall}
            >
              <Phone className="h-4 w-4" />
            </Button>
          )}

          {chat.type !== "chatroom" && onVideoCall && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white hover:bg-blue-700"
              onClick={onVideoCall}
            >
              <Video className="h-4 w-4" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-white hover:bg-blue-700"
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea 
        ref={scrollAreaRef}
        className="flex-1 p-0 mxit-scrollbar"
        style={{ background: "linear-gradient(135deg, #e0f2fe 0%, #bae6fd 50%, #7dd3fc 100%)" }}
      >
        <div className="min-h-full flex flex-col justify-end">
          {/* Messages */}
          <div className="space-y-1 py-4">
            {chat.messages?.map((msg, index) => {
              const sender = chat.participants?.find(p => p._id === msg.senderId) || currentUser
              const isOwn = msg.senderId === currentUser._id
              const showAvatar = !isOwn && (
                index === 0 || 
                chat.messages[index - 1]?.senderId !== msg.senderId
              )

              return (
                <MxitMessageBubble
                  key={msg._id}
                  message={msg}
                  sender={sender}
                  isOwn={isOwn}
                  showAvatar={showAvatar}
                  onReply={(message) => {
                    // Handle reply
                    console.log("Reply to:", message)
                  }}
                  onReact={(messageId, emoji) => {
                    // Handle reaction
                    console.log("React:", messageId, emoji)
                  }}
                  onDelete={(messageId) => {
                    // Handle delete
                    console.log("Delete:", messageId)
                  }}
                />
              )
            })}

            {/* Typing indicator */}
            {typingUsers.length > 0 && (
              <MobileTypingIndicator users={typingUsers} />
            )}

            {/* Empty state */}
            {(!chat.messages || chat.messages.length === 0) && !isLoading && (
              <div className="flex flex-col items-center justify-center py-12 text-blue-600">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  {chat.type === "chatroom" ? (
                    <Users className="h-8 w-8" />
                  ) : (
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={chatInfo.avatar} alt={chatInfo.name} />
                      <AvatarFallback>
                        {chatInfo.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
                <h3 className="text-lg font-medium mb-2">
                  Start chatting with {chatInfo.name}
                </h3>
                <p className="text-sm text-center text-blue-500">
                  Send a message to begin the conversation
                </p>
              </div>
            )}

            {/* Loading state */}
            {isLoading && (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Chat Input */}
      <MxitChatInput
        value={message}
        onChange={setMessage}
        onSend={handleSend}
        onNudge={onNudge}
        onGiftMoola={onGiftMoola}
        onMultiMix={onMultiMix}
        onVoiceNote={handleVoiceNote}
        onImageUpload={onImageUpload}
        moolaBalance={currentUser.moola}
        isRecording={isRecording}
        placeholder={`Message ${chatInfo.name}...`}
      />
    </div>
  )
}
