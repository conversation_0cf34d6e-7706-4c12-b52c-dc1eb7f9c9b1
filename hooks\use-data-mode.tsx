"use client";

import type React from "react";
import {
  useState,
  useEffect,
  createContext,
  useContext,
  useRef,
  useCallback, // Added useCallback for functions passed in context
} from "react";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel"; // Import Id if userId is of type Id<"users">
import { useToast } from "@/components/ui/use-toast";
import {
  MOOLA_RATES,
  calculateMessageCost,
  estimateMessagesWithMoola,
  estimateActiveTimeWithMoola,
  estimateDataWithMoola,
} from "@/lib/moola-services"; // Ensure this path is correct
import { setupMoolaGlobals, initMoolaGlobals } from "@/lib/moola-global";

type DataMode = "mobile" | "moola" | "emergency";

// --- Corrected Interface ---
interface DataModeContextType {
  dataMode: DataMode;
  toggleDataMode: () => void;
  isDataModeAvailable: boolean;
  recordDataUsage: (bytes: number, description: string) => Promise<void>;
  recordMessageSent: (
    contentLength: number,
    hasAttachments?: boolean, // Optional
  ) => Promise<void>;
  recordActiveTime: (minutes: number) => Promise<void>;
  estimatedCost: (bytes: number) => number;
  estimatedMessageCost: (
    contentLength: number,
    hasAttachments?: boolean, // Optional
  ) => number;
  messagesRemaining: number;
  minutesRemaining: number;
  kbRemaining: number;
  usageStats: {
    todayMoolaUsed: number;
    todayMessagesSent: number;
    todayDataUsed: number; // in KB
    weeklyMoolaUsed: number;
  };
  emergencyMoolaAvailable: boolean;
  useEmergencyMoola: () => Promise<void>; // Corrected return type
  isLowMoolaBalance: boolean;
}

// --- Context Creation ---
const DataModeContext = createContext<DataModeContextType | undefined>(
  undefined,
);

// --- Provider Component ---
export function DataModeProvider({ children }: { children: React.ReactNode }) {
  const { userId } = useAuth(); // Ensure useAuth returns Id<"users"> | null | undefined
  const { toast } = useToast();
  const [dataMode, setDataMode] = useState<DataMode>("mobile");
  const [emergencyMoolaAvailable, setEmergencyMoolaAvailable] = useState(false);
  const [emergencyMessagesUsed, setEmergencyMessagesUsed] = useState(0);
  const [usageStats, setUsageStats] = useState({
    todayMoolaUsed: 0,
    todayMessagesSent: 0,
    todayDataUsed: 0, // in KB
    weeklyMoolaUsed: 0,
  });

  // Track active time
  const activeTimeRef = useRef<number>(0);
  const lastActiveRef = useRef<number>(Date.now());

  // --- Convex Queries ---
  // Ensure userId is compatible with expected type (e.g., Id<"users">)
  const validUserId = userId as Id<"users"> | undefined; // Cast or validate if necessary

  const moolaBalance =
    useQuery(
      api.moola.getMoolaBalance,
      validUserId ? { userId: validUserId } : "skip",
    ) ?? 0; // Use ?? 0 for nullish coalescing

  const emergencyMoolaStatus = useQuery(
    api.moola.getEmergencyMoolaStatus,
    validUserId ? { userId: validUserId } : "skip",
  );

  const userUsageStats = useQuery(
    api.moola.getUserUsageStats,
    validUserId ? { userId: validUserId } : "skip",
  );

  // --- Convex Mutations ---
  const recordDataUsageMutation = useMutation(api.moola.recordDataUsage);
  const recordMessageSentMutation = useMutation(api.moola.recordMessageSent);
  const recordActiveTimeMutation = useMutation(api.moola.recordActiveTime);
  const useEmergencyMoolaMutation = useMutation(api.moola.useEmergencyMoola);

  // --- Derived State ---
  const isDataModeAvailable = moolaBalance >= MOOLA_RATES.MIN_BALANCE_FOR_DATA;
  const isLowMoolaBalance = moolaBalance < 10;

  const messagesRemaining = estimateMessagesWithMoola(moolaBalance);
  const minutesRemaining = estimateActiveTimeWithMoola(moolaBalance);
  const kbRemaining = estimateDataWithMoola(moolaBalance);

  // --- Effects ---
  useEffect(() => {
    if (userUsageStats) {
      setUsageStats({
        todayMoolaUsed: userUsageStats.todayMoolaUsed ?? 0,
        todayMessagesSent: userUsageStats.todayMessagesSent ?? 0,
        todayDataUsed: userUsageStats.todayDataUsed ?? 0, // Already in KB
        weeklyMoolaUsed: userUsageStats.weeklyMoolaUsed ?? 0,
      });
    }
  }, [userUsageStats]);

  useEffect(() => {
    if (emergencyMoolaStatus) {
      setEmergencyMoolaAvailable(emergencyMoolaStatus.available ?? false);
      setEmergencyMessagesUsed(emergencyMoolaStatus.messagesUsed ?? 0);
    }
  }, [emergencyMoolaStatus]);

  // Load saved preference on initial mount and when availability changes
  // Consider if only running once on mount is better: useEffect(() => {...}, []);
  useEffect(() => {
    const savedMode = localStorage.getItem("xitchat-data-mode");
    if (savedMode === "moola" && isDataModeAvailable) {
      setDataMode("moola");
    } else if (savedMode === "emergency") {
       // Decide if you should auto-re-enable emergency mode on load
       // For now, it defaults back to mobile if saved was emergency
       // Or check status: if (savedMode === "emergency" && emergencyMoolaAvailable && emergencyMoolaStatus?.messagesUsed < MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) { setDataMode("emergency"); }
    }
    // Default is 'mobile' (initial state)
  }, [isDataModeAvailable]); // Re-evaluates if moola balance changes enough

  // Initialize global Moola functions for components that don't have direct access to this hook
  useEffect(() => {
    // Initialize the global functions
    initMoolaGlobals();

    // Set up the global functions with actual implementations
    setupMoolaGlobals(
      // recordMessageSent function
      async (byteCount: number, hasAttachment: boolean) => {
        if (!validUserId) {
          throw new Error("User not authenticated");
        }

        // Use the current data mode
        const currentMode = dataMode;

        if (currentMode === "mobile") {
          // No Moola cost for mobile data
          return;
        }

        // Record the message sent using the appropriate data mode
        await recordMessageSentMutation({
          userId: validUserId,
          byteCount,
          hasAttachment,
          isEmergency: currentMode === "emergency",
        });
      },

      // getMoolaBalance function
      async () => {
        return moolaBalance;
      },

      // getDataMode function
      () => {
        return dataMode;
      }
    );
  }, [validUserId, dataMode, recordMessageSentMutation, moolaBalance]);

  // --- Functions (Wrapped with useCallback) ---

  // Memoize functions passed down through context to prevent unnecessary re-renders
  // Especially important if consumer components use these functions in their own useEffect dependencies

  const recordActiveTime = useCallback(
    async (minutes: number): Promise<void> => {
      if (dataMode !== "moola" || !validUserId) return;

      try {
        await recordActiveTimeMutation({
          userId: validUserId,
          minutes,
        });
      } catch (error) {
        console.error("Error recording active time:", error);
        // Optionally show a toast, but might be too noisy
      }
    },
    [dataMode, validUserId, recordActiveTimeMutation],
  );

  // Track active time effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined = undefined;

    if (dataMode === "moola") {
      // Reset last active time when switching TO moola mode
      lastActiveRef.current = Date.now();
      activeTimeRef.current = 0;

      intervalId = setInterval(() => {
        const now = Date.now();
        // Ensure timeElapsed is non-negative
        const timeElapsed = Math.max(0, now - lastActiveRef.current) / 1000 / 60; // in minutes
        activeTimeRef.current += timeElapsed;
        lastActiveRef.current = now;

        // Record active time every 5 minutes (accumulated)
        if (activeTimeRef.current >= 5) {
          recordActiveTime(activeTimeRef.current).then(() => {
             // Reset accumulator ONLY after successful recording (or handle error)
             activeTimeRef.current = 0;
          }).catch(err => {
             // Decide how to handle recording errors, maybe retry or log
             console.error("Failed to record active time in interval:", err);
          });
        }
      }, 60000); // Check every minute
    }

    // Cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      // Optional: Record any remaining time when switching away or unmounting
      // if (dataMode === "moola" && activeTimeRef.current > 0) {
      //   recordActiveTime(activeTimeRef.current);
      //   activeTimeRef.current = 0; // Reset after recording
      // }
    };
  }, [dataMode, recordActiveTime]); // Add recordActiveTime dependency because it's used inside

  const switchToMobile = useCallback(() => {
    setDataMode("mobile");
    localStorage.setItem("xitchat-data-mode", "mobile");
  }, []);


  const toggleDataMode = useCallback(() => {
    const currentMode = dataMode; // Read state at time of call

    if (currentMode === "mobile") {
      if (!isDataModeAvailable && !emergencyMoolaAvailable) {
        toast({
          title: "Insufficient Moola",
          description: `Need ${MOOLA_RATES.MIN_BALANCE_FOR_DATA} Moola.`,
          variant: "destructive",
        });
        return;
      }

      if (!isDataModeAvailable && emergencyMoolaAvailable) {
        // Attempt to switch to emergency mode
        setDataMode("emergency");
        localStorage.setItem("xitchat-data-mode", "emergency");
        toast({
          title: "Emergency Moola Mode",
          description: `${
            MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES - emergencyMessagesUsed
          } messages left.`,
        });
        return;
      }

      // Switch to regular moola mode
      setDataMode("moola");
      localStorage.setItem("xitchat-data-mode", "moola");
      toast({
        title: "Moola Data Mode",
        description: `~${messagesRemaining} messages left.`,
      });

    } else { // Switching from 'moola' or 'emergency' to 'mobile'
      switchToMobile();
      toast({
        title: "Mobile Data Mode",
        description: "Using mobile data plan.",
      });
    }
  }, [dataMode, isDataModeAvailable, emergencyMoolaAvailable, emergencyMessagesUsed, messagesRemaining, switchToMobile, toast]);


  const recordDataUsage = useCallback(
    async (bytes: number, description: string): Promise<void> => {
        // Read dataMode at the time function is called
        const currentMode = dataMode;
        if ((currentMode !== "moola" && currentMode !== "emergency") || !validUserId) return;

        try {
          await recordDataUsageMutation({
            userId: validUserId,
            bytes,
            description,
            isEmergency: currentMode === "emergency",
          });
        } catch (error) {
          console.error("Error recording data usage:", error);
          // Fallback to mobile data on error (like insufficient funds)
          switchToMobile();
          toast({
            title: "Switched to Mobile Data",
            description: "Moola usage failed. Using mobile data.",
            variant: "destructive",
          });
        }
    },
    [dataMode, validUserId, recordDataUsageMutation, switchToMobile, toast] // Add dataMode here
  );


  const recordMessageSent = useCallback(
    async (contentLength: number, hasAttachments = false): Promise<void> => {
      const currentMode = dataMode; // Read state at time of call
      if ((currentMode !== "moola" && currentMode !== "emergency") || !validUserId) return;

      const isEmergency = currentMode === "emergency";

      try {
        await recordMessageSentMutation({
          userId: validUserId,
          contentLength,
          hasAttachments,
          isEmergency,
        });

        if (isEmergency) {
          const newEmergencyMessagesUsed = emergencyMessagesUsed + 1;
          setEmergencyMessagesUsed(newEmergencyMessagesUsed); // Update local counter

          // Check if emergency messages are depleted
          if (newEmergencyMessagesUsed >= MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES) {
            switchToMobile(); // Switch to mobile
            toast({
              title: "Emergency Moola Depleted",
              description: "Used all emergency messages. Switched to mobile.",
              variant: "destructive",
            });
            // Optionally disable emergency mode on backend or mark as used up here
             setEmergencyMoolaAvailable(false); // Assume it's no longer available
          }
        }
      } catch (error) {
        console.error("Error recording message sent:", error);
        // Fallback to mobile data on error
        switchToMobile();
        toast({
          title: "Switched to Mobile Data",
          description: "Moola usage failed. Using mobile data.",
          variant: "destructive",
        });
      }
    },
    [dataMode, validUserId, emergencyMessagesUsed, recordMessageSentMutation, switchToMobile, toast] // Add dataMode
  );


  const useEmergencyMoola = useCallback(async (): Promise<void> => {
    if (!validUserId) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to use Emergency Moola.",
        variant: "destructive",
      });
      return;
    }

    // Allow using emergency Moola even if emergencyMoolaAvailable is false
    // The backend will handle the validation and creation of emergency Moola

    try {
      // Call the mutation to activate emergency Moola
      const result = await useEmergencyMoolaMutation({ userId: validUserId });

      // Update state after successful mutation
      setDataMode("emergency");
      localStorage.setItem("xitchat-data-mode", "emergency");
      // Reset the counter since we've just activated it
      setEmergencyMessagesUsed(0);
      // Mark as available
      setEmergencyMoolaAvailable(true);

      toast({
        title: "Emergency Moola Activated",
        description: `You have ${MOOLA_RATES.EMERGENCY_MOOLA_MESSAGES} emergency messages.`,
      });
    } catch (error: any) {
      console.error("Error using emergency Moola:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to activate emergency Moola.",
        variant: "destructive",
      });
    }
  }, [validUserId, useEmergencyMoolaMutation, toast]); // Removed emergencyMoolaAvailable dependency


  // Estimate cost for a given data usage (No need for useCallback unless MOOLA_RATES changes)
  const estimatedCost = (bytes: number): number => {
    const kilobytes = bytes / 1024;
    // Ensure calculation is correct based on your rate definition
    return Math.ceil(kilobytes * MOOLA_RATES.DATA_PER_KB * 10) / 10;
  };

  // Estimate cost for a message (No need for useCallback unless dependencies change)
  const estimatedMessageCost = (
    contentLength: number,
    hasAttachments = false,
  ): number => {
    // Ensure calculateMessageCost is pure function or memoize if needed
    return calculateMessageCost(contentLength, hasAttachments);
  };


  // --- Context Value Object ---
  // Use the memoized functions in the context value
  const contextValue: DataModeContextType = {
    dataMode,
    toggleDataMode, // Already memoized
    isDataModeAvailable,
    recordDataUsage, // Memoized
    recordMessageSent, // Memoized
    recordActiveTime, // Memoized
    estimatedCost, // Not memoized (likely fine)
    estimatedMessageCost, // Not memoized (likely fine)
    messagesRemaining,
    minutesRemaining,
    kbRemaining,
    usageStats,
    emergencyMoolaAvailable,
    useEmergencyMoola, // Memoized
    isLowMoolaBalance,
  };

  // --- Return Provider ---
  // Ensure this JSX is clean and has no hidden characters or typos
  return (
    <DataModeContext.Provider value={contextValue}>
      {children}
    </DataModeContext.Provider>
  );
}

// --- Custom Hook for Consumption ---
export function useDataMode(): DataModeContextType { // Return the specific type
  const context = useContext(DataModeContext);
  if (context === undefined) { // Check for undefined specifically
    throw new Error("useDataMode must be used within a DataModeProvider");
  }
  return context;
}

// --- Default Export ---
export default DataModeProvider;