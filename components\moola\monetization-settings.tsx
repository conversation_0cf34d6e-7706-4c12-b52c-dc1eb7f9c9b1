"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { useToast } from "@/components/ui/use-toast"
import { MoolaIcon } from "@/components/moola-icon"
import { useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useAuth } from "@/hooks/use-auth"
import { Id } from "@/convex/_generated/dataModel"


export function MonetizationSettings({ chatId }: { chatId?: string }) {
  const { userId } = useAuth()
  const { toast } = useToast()
  const [moolaPerMessage, setMoolaPerMessage] = useState(2)
  const [enableTipping, setEnableTipping] = useState(true)
  const [enableSubscription, setEnableSubscription] = useState(false)
  const [subscriptionAmount, setSubscriptionAmount] = useState(100)

  const updateChatSettings = useMutation(api.chats.updateChatSettings)

  const handleSaveSettings = async () => {
    if (!userId || !chatId) return

    try {
      await updateChatSettings({
        chatId: chatId as Id<"chats">,
        userId: userId as Id<"users">,
        settings: {
          moolaPerMessage,
          enableTipping,
          enableSubscription,
          subscriptionAmount,
          enableMonetization: true,
        },
      })

      toast({
        title: "Settings saved",
        description: "Your monetization settings have been updated",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MoolaIcon className="h-5 w-5 text-yellow-500" />
          Monetization Settings
        </CardTitle>
        <CardDescription>Configure how you earn Moola from your content</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="moola-per-message">Moola per Message</Label>
            <span className="font-medium">{moolaPerMessage} Moola</span>
          </div>
          <Slider
            id="moola-per-message"
            min={1}
            max={10}
            step={1}
            value={[moolaPerMessage]}
            onValueChange={(value) => setMoolaPerMessage(value[0])}
          />
          <p className="text-xs text-muted-foreground">Amount of Moola charged per message in your chatrooms</p>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enable-tipping">Enable Tipping</Label>
              <p className="text-xs text-muted-foreground">Allow users to send you Moola as tips</p>
            </div>
            <Switch id="enable-tipping" checked={enableTipping} onCheckedChange={setEnableTipping} />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enable-subscription">Enable Subscription</Label>
              <p className="text-xs text-muted-foreground">Charge a monthly subscription for premium content</p>
            </div>
            <Switch id="enable-subscription" checked={enableSubscription} onCheckedChange={setEnableSubscription} />
          </div>

          {enableSubscription && (
            <div className="pt-2">
              <Label htmlFor="subscription-amount">Monthly Subscription (Moola)</Label>
              <Input
                id="subscription-amount"
                type="number"
                min={50}
                value={subscriptionAmount}
                onChange={(e) => setSubscriptionAmount(Number(e.target.value))}
                className="mt-1"
              />
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label>Withdrawal Options</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" className="justify-start">
              <span className="mr-2">💳</span> Bank Card
            </Button>
            <Button variant="outline" className="justify-start">
              <span className="mr-2">📱</span> Mobile Money
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveSettings} className="w-full">
          Save Settings
        </Button>
      </CardFooter>
    </Card>
  )
}

