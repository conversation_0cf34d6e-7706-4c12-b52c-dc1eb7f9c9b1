"use server"

import { NextResponse } from "next/server"
import { ConvexHttpClient } from "convex/browser"
import { api } from "@/convex/_generated/api"

// Initialize the Convex client
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!)

export async function GET() {
  try {
    // Initialize chat rooms
    const result = await convex.mutation(api.init.initializeChatRooms, {})

    return NextResponse.json({ success: true, result })
  } catch (error) {
    console.error("Initialization error:", error)
    return NextResponse.json({ success: false, error: String(error) }, { status: 500 })
  }
}

