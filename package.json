{"name": "XITchat", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth0/auth0-react": "latest", "@clerk/clerk-react": "latest", "@clerk/clerk-sdk-node": "latest", "@clerk/nextjs": "latest", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "latest", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "convex": "^1.21.0", "date-fns": "^2.30.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.14", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.1.0", "next-themes": "^0.4.6", "querystring": "^0.2.1", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sharp": "^0.33.5", "sonner": "^1.7.4", "svix": "latest", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "url": "^0.11.4", "vaul": "^0.9.9", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}, "pnpm": {"ignoredBuiltDependencies": ["@clerk/shared", "esbuild", "sharp"]}}