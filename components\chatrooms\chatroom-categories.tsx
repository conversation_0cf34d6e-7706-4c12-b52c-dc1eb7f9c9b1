"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/components/ui/use-toast"

interface ChatroomCategoriesProps {
  selectedCategory: string | null
  onSelectCategory: (category: string | null) => void
}

export interface ChatroomCategory {
  id: string | null
  name: string
  ageRestriction: number
  icon: string
  description?: string
  isAnonymous?: boolean
}

export const categories: ChatroomCategory[] = [
  { 
    id: null, 
    name: "All", 
    ageRestriction: 0, 
    icon: "🌐",
    description: "All chatrooms in one place"
  },
  { 
    id: "kasi-vibes", 
    name: "Kasi Vibes", 
    ageRestriction: 16, 
    icon: "🏘️",
    description: "Local township flavor and culture"
  },
  { 
    id: "campus", 
    name: "Campus Life", 
    ageRestriction: 16, 
    icon: "🎓",
    description: "University and college chatrooms"
  },
  { 
    id: "sports", 
    name: "Sports & Teams", 
    ageRestriction: 13, 
    icon: "⚽",
    description: "Football, rugby, cricket and more"
  },
  { 
    id: "music", 
    name: "Music & Arts", 
    ageRestriction: 13, 
    icon: "🎵",
    description: "Amapiano, Hip Hop, Gqom and local artists"
  },
  { 
    id: "flirt", 
    name: "Flirt & Mingle", 
    ageRestriction: 18, 
    icon: "💘",
    description: "Make new connections",
    isAnonymous: true
  },
  { 
    id: "local-news", 
    name: "Local News", 
    ageRestriction: 0, 
    icon: "📰",
    description: "What's happening around you"
  },
  { 
    id: "anonymous", 
    name: "Anonymous", 
    ageRestriction: 18, 
    icon: "🎭",
    description: "Chat without revealing your identity",
    isAnonymous: true
  }
]

// Define a map for specific colors for each category when selected
const categoryColors: { [key: string]: string } = {
  "all": "bg-blue-500", // "All" category
  "kasi-vibes": "bg-purple-600",
  "campus": "bg-pink-500",
  "sports": "bg-cyan-500",
  "music": "bg-green-500",
  "flirt": "bg-orange-500",
  "local-news": "bg-indigo-600",
  "anonymous": "bg-red-600",
}

export function ChatroomCategories({ selectedCategory, onSelectCategory }: ChatroomCategoriesProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [showAgeDialog, setShowAgeDialog] = useState(false)
  const [pendingCategory, setPendingCategory] = useState<string | null>(null)

  const handleCategorySelect = (categoryId: string | null) => {
    const category = categories.find((c) => c.id === categoryId)

    if (!category) {
      onSelectCategory(categoryId)
      return
    }

    // You might want to add actual user age verification here
    // For now, it proceeds if no age restriction or if user is assumed to meet it.
    if (category.ageRestriction > 0 /* && (!user || user.age < category.ageRestriction) */) {
      setPendingCategory(categoryId)
      setShowAgeDialog(true)
    } else {
      onSelectCategory(categoryId)
    }
  }

  const confirmAgeRestriction = () => {
    onSelectCategory(pendingCategory)
    setShowAgeDialog(false)

    toast({
      title: "Age Verified",
      description: "You now have access to this category",
    })
  }

  return (
    <>
      <div className="bg-white p-4 shadow-sm rounded-xl">
        <div className="flex flex-wrap gap-3 justify-start">
          {categories.map((category) => {
            const isSelected = selectedCategory === category.id;
            const categoryIdForColor = category.id || "all"; // Use "all" key for the "All" category
            const selectedColorClass = categoryColors[categoryIdForColor] || "bg-blue-500"; // Fallback color
            
            return (
              <Button
                key={category.id || "all"}
                variant="ghost" // Use ghost variant to strip default shadcn button styles
                size="sm"
                onClick={() => handleCategorySelect(category.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium 
                  transition-all duration-200 whitespace-nowrap
                  ${
                    isSelected
                      ? `${selectedColorClass} text-white shadow-md`
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }
                `}
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
                {category.ageRestriction > 0 && (
                  <span className={`ml-1 text-xs ${isSelected ? "text-white/80" : "text-gray-500"}`}>
                    {category.ageRestriction}+
                  </span>
                )}
              </Button>
            )
          })}
        </div>
      </div>

      <Dialog open={showAgeDialog} onOpenChange={setShowAgeDialog}>
        <DialogContent className="rounded-lg p-5">
          <DialogHeader>
            <DialogTitle>Age Verification Required</DialogTitle>
            <DialogDescription>
              This category requires you to be {categories.find((c) => c.id === pendingCategory)?.ageRestriction}+ years old.
            </DialogDescription>
          </DialogHeader>
          <p className="py-4">By continuing, you confirm that you meet the age requirement for this category.</p>
          <DialogFooter className="flex justify-end gap-3">
            <Button variant="outline" onClick={() => setShowAgeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmAgeRestriction}>I Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}