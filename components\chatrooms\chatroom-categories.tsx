"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/components/ui/use-toast"

interface ChatroomCategoriesProps {
  selectedCategory: string | null
  onSelectCategory: (category: string | null) => void
}

const categories = [
  { id: null, name: "All", ageRestriction: 0 },
  { id: "flirt", name: "Flirt", ageRestriction: 18 },
  { id: "teen", name: "Teen", ageRestriction: 13 },
  { id: "grown-up", name: "Grown-up", ageRestriction: 21 },
  { id: "kingdom", name: "Kingdom", ageRestriction: 0 },
  { id: "topical", name: "Topical", ageRestriction: 0 },
  { id: "geographical", name: "Geographical", ageRestriction: 0 },
]

export function ChatroomCategories({ selectedCategory, onSelectCategory }: ChatroomCategoriesProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [showAgeDialog, setShowAgeDialog] = useState(false)
  const [pendingCategory, setPendingCategory] = useState<string | null>(null)

  const handleCategorySelect = (categoryId: string | null) => {
    const category = categories.find((c) => c.id === categoryId)

    if (!category) {
      onSelectCategory(categoryId)
      return
    }

    // Check age restriction
    if (category.ageRestriction > 0) {
      // In a real app, you would check the user's age from their profile
      // For this demo, we'll just show a dialog
      setPendingCategory(categoryId)
      setShowAgeDialog(true)
    } else {
      onSelectCategory(categoryId)
    }
  }

  const confirmAgeRestriction = () => {
    onSelectCategory(pendingCategory)
    setShowAgeDialog(false)

    toast({
      title: "Age Verified",
      description: "You now have access to this category",
    })
  }

  // Only show the first 4 categories on mobile
  const visibleCategories = categories.slice(0, 4)

  return (
    <>
      <div className="bg-gray-950 p-2">
        <ScrollArea className="w-full" type="horizontal">
          <div className="flex gap-2 pb-1">
            {visibleCategories.map((category) => (
              <Button
                key={category.id || "all"}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategorySelect(category.id)}
                className={`${
                  selectedCategory === category.id
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-gray-900 text-gray-300 hover:bg-gray-800"
                } rounded-md`}
              >
                {category.name}
                {category.ageRestriction > 0 && (
                  <span className="ml-1 text-xs opacity-70">{category.ageRestriction}+</span>
                )}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      <Dialog open={showAgeDialog} onOpenChange={setShowAgeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Age Verification Required</DialogTitle>
            <DialogDescription>
              This category requires you to be {categories.find((c) => c.id === pendingCategory)?.ageRestriction}+ years
              old.
            </DialogDescription>
          </DialogHeader>
          <p className="py-4">By continuing, you confirm that you meet the age requirement for this category.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAgeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmAgeRestriction}>I Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

