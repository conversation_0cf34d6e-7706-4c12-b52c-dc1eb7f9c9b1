"use client"

import { v } from "convex/values"
import { mutation, query } from "./_generated/server"

// Get all chatrooms
export const getAllChatrooms = query({
  handler: async (ctx) => {
    const chatrooms = await ctx.db
      .query("chats")
      .filter((q) => q.eq(q.field("type"), "chatroom"))
      .collect()

    // Get participant count for each chatroom
    const chatroomsWithStats = await Promise.all(
      chatrooms.map(async (room) => {
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", room._id))
          .collect()

        const creator = await ctx.db.get(room.creatorId)

        return {
          ...room,
          memberCount: participants.length,
          creator: creator ? { name: creator.name, avatar: creator.avatar } : null,
        }
      }),
    )

    return chatroomsWithStats
  },
})

// Get chatrooms by user
export const getUserChatrooms = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Get all chatrooms where user is a participant
    const participations = await ctx.db
      .query("chatParticipants")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect()

    const chatIds = participations.map((p) => p.chatId)

    // Filter for only chatroom type
    const chatrooms = await Promise.all(
      chatIds.map(async (chatId) => {
        const chat = await ctx.db.get(chatId)
        if (!chat || chat.type !== "chatroom") return null
        return chat._id
      }),
    )

    return chatrooms.filter(Boolean) as string[]
  },
})

// Get chatrooms by category
export const getChatroomsByCategory = query({
  args: {
    category: v.union(
      v.literal("flirt"),
      v.literal("teen"),
      v.literal("grown-up"),
      v.literal("kingdom"),
      v.literal("topical"),
      v.literal("geographical"),
    ),
  },
  handler: async (ctx, args) => {
    const chatrooms = await ctx.db
      .query("chats")
      .filter((q) => q.and(q.eq(q.field("type"), "chatroom"), q.eq(q.field("category"), args.category)))
      .collect()

    // Get participant count for each chatroom
    const chatroomsWithStats = await Promise.all(
      chatrooms.map(async (room) => {
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", room._id))
          .collect()

        const creator = await ctx.db.get(room.creatorId)

        return {
          ...room,
          memberCount: participants.length,
          creator: creator ? { name: creator.name, avatar: creator.avatar } : null,
        }
      }),
    )

    return chatroomsWithStats
  },
})

// Create a new chatroom
export const createChatroom = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    isPrivate: v.boolean(),
    isPublic: v.boolean(),
    location: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    creatorId: v.id("users"),
    category: v.optional(
      v.union(
        v.literal("flirt"),
        v.literal("teen"),
        v.literal("grown-up"),
        v.literal("kingdom"),
        v.literal("topical"),
        v.literal("geographical"),
      ),
    ),
    moolaPerMessage: v.optional(v.number()),
    thumbnailUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    const user = await ctx.db.query("users").withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject)).unique();
    if (!user) throw new Error("User not found");
    const now = Date.now()

    // Create the chatroom
    const chatroomId = await ctx.db.insert("chats", {
      type: "chatroom" as const,
      name: args.name,
      description: args.description,
      isPublic: !args.isPrivate,
      isPrivate: args.isPrivate,
      creatorId: args.creatorId,
      createdAt: now,
      updatedAt: now,
      location: args.location,
      tags: args.tags,
      category: args.category || "topical",
      moolaPerMessage: args.moolaPerMessage || 2,
      thumbnailUrl: args.thumbnailUrl,
      participants: [], // Add creator as first participant
      background: "", // Add empty background field
    })

    // Add creator as admin participant
    await ctx.db.insert("chatParticipants", {
      chatId: chatroomId,
      userId: args.creatorId,
      unreadCount: 0,
      joinedAt: now,
      role: "admin",
    })

    // Create a welcome message
    await ctx.db.insert("messages", {
      chatId: chatroomId,
      senderId: args.creatorId,
      content: `Welcome to ${args.name}! This is the beginning of the chatroom.`,
      timestamp: now,
      isRead: false,
      isNudge: false,
      isMultimix: false,
      reactions: [],
    })

    return chatroomId
  },
})

// Join a chatroom
export const joinChatroom = mutation({
  args: {
    chatroomId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const now = Date.now()

    // Check if user is already a participant
    const existingParticipant = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) => q.eq("chatId", args.chatroomId).eq("userId", args.userId))
      .first()

    if (existingParticipant) {
      return args.chatroomId
    }

    // Get the chatroom
    const chatroom = await ctx.db.get(args.chatroomId)
    if (!chatroom) {
      throw new Error("Chatroom not found")
    }

    // Check if chatroom is private
    if (chatroom.isPrivate) {
      // For now, allow joining private rooms
      // In a real app, you'd implement invitation logic here
    }

    // Add user as participant
    await ctx.db.insert("chatParticipants", {
      chatId: args.chatroomId,
      userId: args.userId,
      unreadCount: 0,
      joinedAt: now,
      role: "member",
    })

    // Create a system message that user joined
    const user = await ctx.db.get(args.userId)
    if (user) {
      await ctx.db.insert("messages", {
        chatId: args.chatroomId,
        senderId: args.userId,
        content: `${user.name} joined the chatroom.`,
        timestamp: now,
        isRead: false,
        isNudge: false,
        isMultimix: false,
        reactions: [],
      })
    }

    return args.chatroomId
  },
})

// Get online users in chatrooms
export const getOnlineUsersInChatrooms = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    try {
      // Get all public chatrooms
      const chatrooms = await ctx.db
        .query("chats")
        .filter((q) => q.and(q.eq(q.field("type"), "chatroom"), q.eq(q.field("isPublic"), true)))
        .collect()

      // For each chatroom, get the online users
      const chatroomsWithOnlineUsers = await Promise.all(
        chatrooms.map(async (chatroom) => {
          // Get all participants in this chatroom
          const participants = await ctx.db
            .query("chatParticipants")
            .withIndex("by_chat", (q) => q.eq("chatId", chatroom._id))
            .collect()

          // Get user details for each participant
          const users = await Promise.all(
            participants.map(async (participant) => {
              const user = await ctx.db.get(participant.userId)
              return user
            }),
          )

          // Filter to only online users and exclude the current user
          const onlineUsers = users.filter((user) => user && user.status === "online" && user._id !== args.userId)

          return {
            id: chatroom._id,
            name: chatroom.name || "Unnamed Chatroom",
            description: chatroom.description,
            onlineUsers,
            totalOnline: onlineUsers.length,
          }
        }),
      )

      // Only return chatrooms that have online users
      return chatroomsWithOnlineUsers.filter((chatroom) => chatroom.onlineUsers.length > 0)
    } catch (error) {
      console.error("Error getting online users in chatrooms:", error)
      return []
    }
  },
})
