"use client"

import { v } from "convex/values"
import { mutation, query, internalMutation } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { chatCategoryValidator } from "./lib/chatUtils" // Ensure chatCategoryValidator includes "local-news"

// Get all chatrooms with optional category filter
export const getAllChatrooms = query({
  args: {
    category: v.optional(chatCategoryValidator)
  },
  handler: async (ctx, args) => {
    let queryBuilder = ctx.db.query("chats")
      .filter((q) => q.eq(q.field("type"), "chatroom"));

    // Apply category filter if provided
    if (args.category) {
      queryBuilder = queryBuilder.filter((q) => q.eq(q.field("category"), args.category));
    }

    const chatrooms = await queryBuilder.collect();

    // Get participant count for each chatroom
    const chatroomsWithStats = await Promise.all(
      chatrooms.map(async (room) => {
        const participants = await ctx.db
          .query("chatParticipants")
          .withIndex("by_chat", (q) => q.eq("chatId", room._id))
          .collect();

        const creator = await ctx.db.get(room.creatorId);

        return {
          ...room,
          memberCount: participants.length,
          creator: creator ? { name: creator.name, avatar: creator.avatar } : null,
        };
      }),
    );

    return chatroomsWithStats;
  },
});

// Get chatrooms by user (returns an array of chatroom IDs)
export const getUserChatrooms = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const participations = await ctx.db
      .query("chatParticipants")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const chatIds = await Promise.all(
      participations.map(async (p) => {
        const chat = await ctx.db.get(p.chatId);
        // Only return ID if it's an actual chatroom type
        return chat && chat.type === "chatroom" ? chat._id : null;
      })
    );

    return chatIds.filter(Boolean) as Id<"chats">[]; // Explicitly type as Id<"chats">[]
  },
});

// Create a new chatroom
export const createChatroom = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    isPrivate: v.boolean(),
    location: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    creatorId: v.id("users"),
    category: chatCategoryValidator, // Using the validator directly
    moolaPerMessage: v.optional(v.number()),
    thumbnailUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    const user = await ctx.db.query("users").withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject)).unique();
    if (!user) throw new Error("User not found");
    const now = Date.now();

    // Create the chatroom
    const chatroomId = await ctx.db.insert("chats", {
      type: "chatroom", // 'as const' not needed here due to type inference from v.union
      name: args.name,
      description: args.description,
      isPublic: !args.isPrivate,
      isPrivate: args.isPrivate,
      creatorId: args.creatorId,
      createdAt: now,
      updatedAt: now,
      location: args.location,
      tags: args.tags || [],
      category: args.category,
      moolaPerMessage: args.moolaPerMessage || 2,
      thumbnailUrl: args.thumbnailUrl,
      participants: [], // Will be updated by chatParticipants table logic
      background: "",
    });

    // Add creator as admin participant
    await ctx.db.insert("chatParticipants", {
      chatId: chatroomId,
      userId: args.creatorId,
      unreadCount: 0,
      joinedAt: now,
      role: "admin",
    });

    // Create a welcome message
    await ctx.db.insert("messages", {
      chatId: chatroomId,
      senderId: args.creatorId,
      content: `Welcome to ${args.name}! This is the beginning of the chatroom.`,
      timestamp: now,
      // Default message fields as per your schema
      isDeleted: false,
      isNudge: false,
      isMultimix: false,
      isRead: false,
      reactions: [],
    });

    return chatroomId;
  },
});

// Submit local news
export const submitLocalNews = mutation({
  args: {
    localNewsChatroomId: v.id("chats"), // Expect the ID from the frontend
    title: v.string(),
    description: v.string(),
    url: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify the provided chatroom ID exists and is the actual local news chatroom
    const newsChatroom = await ctx.db.get(args.localNewsChatroomId);
    if (!newsChatroom || newsChatroom.name !== "📰 Local News") {
        throw new Error("Invalid or non-local news chatroom ID provided.");
    }

    // Get user info
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Create a message in the chatroom
    const messageContent = `**${args.title}**\n\n${args.description}${args.url ? `\n\n[Read more](${args.url})` : ''}`;

    // Create a message in the chatroom using the existing message creation logic
    const messageId = await ctx.db.insert("messages", {
      chatId: args.localNewsChatroomId,
      senderId: user._id,
      content: messageContent,
      timestamp: Date.now(),
      isDeleted: false,
      isNudge: false,
      isMultimix: false,
      isRead: false,
      reactions: [],
    });

    return { success: true, messageId };
  },
});

// Type for news item with user info
interface NewsItemWithSender {
  _id: Id<"messages">;
  _creationTime: number;
  chatId: Id<"chats">;
  senderId: Id<"users">;
  content: string;
  timestamp: number;
  userName: string;
  userAvatar?: string;
  url?: string; // Add url if it's extracted from content or stored
  title?: string; // Add title if it's extracted from content or stored
}

// Get recent local news (from the dedicated chatroom)
export const getRecentLocalNews = query({
  args: {
    localNewsChatroomId: v.optional(v.id("chats")) // Optional, but preferred if frontend has it
  },
  handler: async (ctx, args): Promise<NewsItemWithSender[]> => {
    let newsChatroom;

    // Try to find by ID if provided, otherwise by name
    if (args.localNewsChatroomId) {
      const chat = await ctx.db.get(args.localNewsChatroomId);
      if (chat && chat.name === "📰 Local News") {
        newsChatroom = chat;
      }
    }

    if (!newsChatroom) {
      // Fallback to finding by name if ID not provided or invalid
      newsChatroom = await ctx.db
        .query("chats")
        .withIndex("by_name", (q) => q.eq("name", "📰 Local News"))
        .first();
    }

    if (!newsChatroom) return [];

    // Get all news messages, sorted by timestamp descending (most recent first)
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat_and_timestamp", (q) => q.eq("chatId", newsChatroom!._id))
      .order("desc") // Order by timestamp descending
      .take(10); // Take recent 10 messages

    const newsWithUsers = await Promise.all(
      messages.map(async (message): Promise<NewsItemWithSender | null> => {
        if (!message.senderId) return null; // Messages without sender shouldn't be news items
        const user = await ctx.db.get(message.senderId);
        if (!user) return null;

        // Basic parsing of title and URL from markdown content
        const titleMatch = message.content.match(/^\*\*([^*]+)\*\*/);
        const urlMatch = message.content.match(/\[Read more\]\(([^)]+)\)/);

        return {
          _id: message._id,
          _creationTime: message._creationTime,
          chatId: message.chatId,
          senderId: message.senderId,
          content: message.content,
          timestamp: message.timestamp,
          userName: user.name || 'Unknown',
          userAvatar: user.avatar,
          title: titleMatch ? titleMatch[1] : undefined,
          url: urlMatch ? urlMatch[1] : undefined,
        };
      })
    );

    return newsWithUsers.filter((item): item is NewsItemWithSender => item !== null);
  },
});


// Function to ensure local news chatroom exists
// In your chatrooms.ts file, modify the ensureLocalNewsChatroom mutation
export const ensureLocalNewsChatroom = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get the current user's ID
    const userId = identity.subject as Id<"users">;

    // Rest of your code, using userId instead of "system"
    const existing = await ctx.db
      .query("chats")
      .withIndex("by_name", (q) => q.eq("name", "📰 Local News"))
      .first();

    if (existing) {
      return existing._id;
    }

    // Create the chatroom with the current user as the creator
    const chatroomId = await ctx.db.insert("chats", {
      name: "📰 Local News",
      description: "Stay updated with the latest local news and announcements",
      isPublic: true,
      creatorId: userId,  // Use the authenticated user's ID
      participants: [userId],
      type: "chatroom",
      category: "local-news",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return chatroomId;
  },
});
// Join a chatroom
export const joinChatroom = mutation({
  args: {
    chatroomId: v.id("chats"),
    userId: v.id("users"),
    userName: v.optional(v.string()), // Consider removing if user.name is always used
    userProfileImage: v.optional(v.string()), // Consider removing if user.avatar is always used
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    const chatroom = await ctx.db.get(args.chatroomId);
    if (!chatroom || chatroom.type !== "chatroom") {
      throw new Error("Chatroom not found or invalid chat type.");
    }

    // Check if user is already a participant
    const existingParticipant = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) =>
        q.eq("chatId", args.chatroomId).eq("userId", args.userId)
      )
      .first();

    if (existingParticipant) {
      return args.chatroomId; // Already joined
    }

    // Check if chatroom is private (implement invitation logic if needed)
    if (chatroom.isPrivate) {
      // For now, allow joining private rooms, but in a real app,
      // you'd implement invitation logic or restrict direct joins.
      console.warn(`Attempted to join private chatroom ${chatroom._id} directly.`);
    }

    // Add user as participant
    await ctx.db.insert("chatParticipants", {
      chatId: args.chatroomId,
      userId: args.userId,
      unreadCount: 0,
      joinedAt: now,
      role: "member",
    });

    // Create a system message that user joined
    const user = await ctx.db.get(args.userId);
    if (user) {
      await ctx.db.insert("messages", {
        chatId: args.chatroomId,
        senderId: user._id,
        content: `${user.name} joined the chatroom.`,
        timestamp: now,
        isDeleted: false,
        isNudge: false,
        isMultimix: false,
        isRead: false,
        reactions: [],
      });
    }

    return args.chatroomId;
  },
});

// Leave a chatroom
export const leaveChatroom = mutation({
  args: {
    chatroomId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Find the participant record
    const participant = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat_and_user", (q) =>
        q.eq("chatId", args.chatroomId).eq("userId", args.userId)
      )
      .first();

    if (!participant) {
      // User is not a participant, nothing to do
      return true;
    }

    // Remove the participant record
    await ctx.db.delete(participant._id);

    // Create a system message that user left
    const user = await ctx.db.get(args.userId);
    if (user) {
      await ctx.db.insert("messages", {
        chatId: args.chatroomId,
        senderId: user._id,
        content: `${user.name} left the chatroom.`,
        timestamp: now,
        isDeleted: false,
        isNudge: false,
        isMultimix: false,
        isRead: false,
        reactions: [],
      });
    }

    return true;
  },
});

// Delete a chatroom
export const deleteChatroom = mutation({
  args: {
    chatroomId: v.id("chats"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Get the chatroom to verify ownership
    const chatroom = await ctx.db.get(args.chatroomId);

    if (!chatroom || chatroom.type !== "chatroom") {
      throw new Error("Chatroom not found or is not a chatroom type.");
    }

    // Verify the user is the creator of the chatroom
    if (chatroom.creatorId !== args.userId) {
      throw new Error("Only the chatroom creator can delete the chatroom");
    }

    // Delete all messages in the chatroom
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatroomId))
      .collect();

    await Promise.all(messages.map((msg) => ctx.db.delete(msg._id)));

    // Delete all participant records
    const participants = await ctx.db
      .query("chatParticipants")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatroomId))
      .collect();

    await Promise.all(participants.map((p) => ctx.db.delete(p._id)));

    // Finally, delete the chatroom itself
    await ctx.db.delete(args.chatroomId);

    return true;
  },
});

// Get online users in chatrooms
export const getOnlineUsersInChatrooms = query({
  args: { userId: v.id("users") }, // Current user ID for exclusion
  handler: async (ctx, args) => {
    try {
      // Get all public chatrooms
      const chatrooms = await ctx.db
        .query("chats")
        .filter((q) => q.and(q.eq(q.field("type"), "chatroom"), q.eq(q.field("isPublic"), true)))
        .collect();

      // For each chatroom, get the online users
      const chatroomsWithOnlineUsers = await Promise.all(
        chatrooms.map(async (chatroom) => {
          // Get all participants in this chatroom
          const participants = await ctx.db
            .query("chatParticipants")
            .withIndex("by_chat", (q) => q.eq("chatId", chatroom._id))
            .collect();

          // Get user details for each participant
          const users = await Promise.all(
            participants.map(async (participant) => {
              const user = await ctx.db.get(participant.userId);
              return user;
            }),
          );

          // Filter to only online users and exclude the current user
          const onlineUsers = users.filter((user) => user && user.status === "online" && user._id !== args.userId);

          return {
            id: chatroom._id,
            name: chatroom.name || "Unnamed Chatroom",
            description: chatroom.description,
            onlineUsers: onlineUsers.map(u => ({
              _id: u!._id, // Add ! because filter ensures it's not null
              name: u!.name,
              avatar: u!.avatar,
            })), // Only send necessary user info
            totalOnline: onlineUsers.length,
          };
        }),
      );

      // Only return chatrooms that have online users
      return chatroomsWithOnlineUsers.filter((chatroom) => chatroom.totalOnline > 0);
    } catch (error) {
      console.error("Error getting online users in chatrooms:", error);
      return [];
    }
  },
});