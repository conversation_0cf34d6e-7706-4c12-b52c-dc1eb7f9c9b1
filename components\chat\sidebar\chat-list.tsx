import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { ChatItem } from "./chat-item";
import { EmptyStates } from "./empty-states";
import { useOnlineUsers } from "@/hooks/use-online-users";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import type { Id } from "@/convex/_generated/dataModel";

interface ChatListProps {
  chats: any[]; // Replace with proper type
  currentChatId: string | string[] | undefined;
  currentUserId: string | null;
  contacts: any[]; // Replace with proper type
  isContactsReady: boolean;
  onChatSelect: (chatId: string) => void;
  onRemoveContact: (contactRelationshipId: string) => Promise<void>;
  searchQuery: string;
}

export function ChatList({
  chats,
  currentChatId,
  currentUserId,
  contacts,
  isContactsReady,
  onChatSelect,
  onRemoveContact,
  searchQuery,
}: ChatListProps) {
  // Get all user IDs for presence checking
  const userIds = chats
    .filter(chat => chat?.type === "direct")
    .flatMap(chat => {
      const otherUser = chat.participants?.find((p: any) => p?._id !== currentUserId);
      return otherUser?._id ? [otherUser._id] : [];
    });
  
  // Get presence for all users in the chat list
  const { isUserOnline } = useOnlineUsers(userIds as Id<"users">[]);
  
  // Get current user settings
  const currentUser = useQuery(
    api.users.getUser, 
    currentUserId ? { userId: currentUserId as Id<"users"> } : "skip"
  );

  const filteredChats = chats.filter((chat) => {
    if (!chat) return false;
    
    // For direct chats, filter out deleted users and check online status
    if (chat.type === "direct") {
      const otherUser = chat.participants?.find((p: any) => p?._id !== currentUserId);
      
      // Skip if user is deleted or not found
      if (!otherUser || otherUser.isDeleted) return false;
      
      // Get user settings with defaults
      const userSettings = currentUser?.settings || {};
      const hideOfflineContacts = userSettings.hideOfflineContacts ?? false;
      
      // Skip if user is not online and hideOfflineContacts is true
      if (hideOfflineContacts && !isUserOnline(otherUser._id)) {
        return false;
      }
      
      // Filter by search query
      const nameToSearch = otherUser?.name || '';
      return typeof nameToSearch === 'string' 
        ? nameToSearch.toLowerCase().includes(searchQuery.toLowerCase())
        : false;
    }
    
    // For group chats, just filter by name
    const nameToSearch = chat.name || '';
    return typeof nameToSearch === 'string'
      ? nameToSearch.toLowerCase().includes(searchQuery.toLowerCase())
      : false;
  });

  const sortedChats = [...filteredChats].sort((a, b) => {
    const timeA = a?.lastMessage?.timestamp ?? a?.updatedAt ?? 0;
    const timeB = b?.lastMessage?.timestamp ?? b?.updatedAt ?? 0;
    if (timeB !== timeA) {
      return timeB - timeA;
    }
    const creationA = a?.createdAt ?? 0;
    const creationB = b?.createdAt ?? 0;
    return creationB - creationA;
  });

  return (
    <ScrollArea className="flex-1">
      <div className="space-y-1 p-2">
        <EmptyStates 
          hasChats={sortedChats.length > 0}
          hasSearchResults={filteredChats.length > 0}
          searchQuery={searchQuery}
        />

        {sortedChats.map((chat, index) => (
          <ChatItem
            key={`chat-${chat.id}-${index}`}
            chat={chat}
            isActive={chat.id === currentChatId}
            currentUserId={currentUserId}
            contacts={contacts}
            isContactsReady={isContactsReady}
            onChatSelect={onChatSelect}
            onRemoveContact={onRemoveContact}
          />
        ))}
      </div>
    </ScrollArea>
  );
}
