import { clerkMiddleware } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// This example protects all routes including api/trpc routes
// Please edit this to allow other routes to be public as needed.
export default clerkMiddleware(async (auth, req) => {
  const { userId } = await auth();
  const url = new URL(req.url);

  // Check if it's a mobile device
  const userAgent = req.headers.get("user-agent") || "";
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  // If user is authenticated and on mobile and trying to access the landing page
  if (userId && isMobile && url.pathname === '/') {
    // Redirect to mobile chat interface
    url.pathname = '/chat/mobile';
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
});

// Stop Middleware running on static files and public folders
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next
     * - static (static files)
     * - favicon.ico
     * - public files
     */
    "/((?!static|.*\\..*|_next|favicon.ico).*)",
    "/",
  ],
}
