"use client"

import { useState } from "react"
import { formatDistanceToNow } from "date-fns"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Reply, Heart, ThumbsUp, Smile, MoreHorizontal } from "lucide-react"
import { MoolaIcon } from "@/components/moola-icon"
import type { Message, User } from "@/lib/types"

interface MxitMessageBubbleProps {
  message: Message
  sender: User
  isOwn: boolean
  showAvatar?: boolean
  onReply?: (message: Message) => void
  onReact?: (messageId: string, emoji: string) => void
  onDelete?: (messageId: string) => void
}

export function MxitMessageBubble({
  message,
  sender,
  isOwn,
  showAvatar = true,
  onReply,
  onReact,
  onDelete
}: MxitMessageBubbleProps) {
  const [showActions, setShowActions] = useState(false)
  const [showReactions, setShowReactions] = useState(false)

  const quickReactions = ["❤️", "😂", "👍", "😮", "😢", "🔥"]

  const handleBubbleClick = () => {
    if (showActions) {
      setShowActions(false)
    }
  }

  const handleLongPress = () => {
    setShowActions(true)
    // Add haptic feedback if available
    if (navigator.vibrate) {
      navigator.vibrate(50)
    }
  }

  const handleReaction = (emoji: string) => {
    onReact?.(message._id, emoji)
    setShowReactions(false)
    setShowActions(false)
  }

  // Format time in Mxit style (just time, not relative)
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-ZA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  return (
    <div className={cn(
      "flex gap-2 mb-2 px-3 mxit-fade-in",
      isOwn ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar - only show for other users */}
      {!isOwn && showAvatar && (
        <Avatar className="h-8 w-8 mt-1 flex-shrink-0 mxit-avatar">
          <AvatarImage src={sender.avatar} alt={sender.name} />
          <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
            {sender.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}

      {/* Message Container */}
      <div className={cn(
        "flex flex-col max-w-[85%]",
        isOwn ? "items-end" : "items-start"
      )}>
        {/* Sender name for group chats */}
        {!isOwn && showAvatar && (
          <span className="text-xs text-blue-600 mb-1 px-2 font-medium">
            {sender.name}
          </span>
        )}

        {/* Message Bubble */}
        <div
          className={cn(
            "relative mxit-bubble cursor-pointer",
            isOwn 
              ? "mxit-bubble-sent" 
              : "mxit-bubble-received",
            message.isNudge && "mxit-bounce border-2 border-orange-400",
            message.isMultimix && "bg-gradient-to-r from-purple-500 to-pink-500 text-white",
            showActions && "ring-2 ring-blue-300"
          )}
          onClick={handleBubbleClick}
          onTouchStart={() => {
            // Start long press timer
            const timer = setTimeout(handleLongPress, 500)
            const cleanup = () => clearTimeout(timer)
            
            const handleTouchEnd = () => {
              cleanup()
              document.removeEventListener('touchend', handleTouchEnd)
              document.removeEventListener('touchcancel', handleTouchEnd)
            }
            
            document.addEventListener('touchend', handleTouchEnd)
            document.addEventListener('touchcancel', handleTouchEnd)
          }}
        >
          {/* Special message types */}
          {message.isNudge && (
            <div className="flex items-center gap-2 text-orange-600 font-bold text-sm">
              <span className="text-lg">👋</span>
              NUDGE!
            </div>
          )}

          {message.isMultimix && (
            <div className="text-xs text-yellow-200 font-bold mb-1 flex items-center gap-1">
              ✨ MultiMix
            </div>
          )}

          {/* Message content */}
          {!message.isNudge && (
            <div className={cn(
              "text-sm leading-relaxed",
              message.isMultimix && "font-bold text-shadow"
            )}>
              {message.content}
            </div>
          )}

          {/* Voice note indicator */}
          {message.isVoiceNote && (
            <div className="flex items-center gap-2 mt-1">
              <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
              <span className="text-xs opacity-75">
                🎵 {message.voiceNoteDuration || "0:00"}
              </span>
            </div>
          )}

          {/* Moola cost indicator */}
          {message.moolaCost && message.moolaCost > 0 && (
            <div className="flex items-center gap-1 mt-1 opacity-75">
              <MoolaIcon className="h-3 w-3" />
              <span className="text-xs">{message.moolaCost}</span>
            </div>
          )}

          {/* Message reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {message.reactions.map((reaction, index) => (
                <span
                  key={index}
                  className="text-sm bg-white bg-opacity-20 rounded-full px-2 py-1"
                >
                  {reaction.emoji}
                </span>
              ))}
            </div>
          )}

          {/* Message time and status */}
          <div className={cn(
            "flex items-center justify-between mt-1 text-xs opacity-75",
            isOwn ? "flex-row-reverse" : "flex-row"
          )}>
            <span>{formatTime(message.timestamp)}</span>
            
            {/* Read status for own messages */}
            {isOwn && (
              <span className={cn(
                "ml-2",
                message.isRead ? "text-blue-200" : "text-gray-300"
              )}>
                {message.isRead ? "✓✓" : "✓"}
              </span>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        {showActions && (
          <div className="flex items-center gap-1 mt-2 p-2 bg-white border border-blue-200 rounded-lg shadow-lg mxit-fade-in">
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => {
                setShowReactions(true)
              }}
            >
              <Smile className="h-4 w-4 text-blue-600" />
            </Button>
            
            {onReply && (
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={() => {
                  onReply(message)
                  setShowActions(false)
                }}
              >
                <Reply className="h-4 w-4 text-blue-600" />
              </Button>
            )}

            {isOwn && onDelete && (
              <Button
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                onClick={() => {
                  onDelete(message._id)
                  setShowActions(false)
                }}
              >
                🗑️
              </Button>
            )}

            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => setShowActions(false)}
            >
              ✕
            </Button>
          </div>
        )}

        {/* Quick Reactions */}
        {showReactions && (
          <div className="flex items-center gap-1 mt-2 p-2 bg-white border border-blue-200 rounded-lg shadow-lg mxit-fade-in">
            {quickReactions.map((emoji) => (
              <Button
                key={emoji}
                size="sm"
                variant="ghost"
                className="text-lg p-1 h-8 w-8 hover:bg-blue-50"
                onClick={() => handleReaction(emoji)}
              >
                {emoji}
              </Button>
            ))}
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => setShowReactions(false)}
            >
              ✕
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
