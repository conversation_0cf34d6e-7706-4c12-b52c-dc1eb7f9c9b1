import { Id } from "@/convex/_generated/dataModel";

export type GameType = "tictactoe" | "rps" | "dice" | "numberguess";
export type GameStatus = "waiting" | "in_progress" | "completed" | "abandoned";

export interface BaseGameState {
  _id?: Id<"games">;
  _creationTime?: number;
  type: GameType;
  status: GameStatus;
  players: {
    x: Id<"users"> | null;
    o: Id<"users"> | null;
    currentPlayer: "x" | "o";
  };
  chatId: Id<"chats">;
  createdAt: number;
  updatedAt: number;
  winner?: Id<"users"> | "draw";
}

export interface TicTacToeState extends BaseGameState {
  type: "tictactoe";
  board: ("x" | "o" | null)[];
}

export interface RPSState extends BaseGameState {
  type: "rps";
  choices: {
    x?: "rock" | "paper" | "scissors";
    o?: "rock" | "paper" | "scissors";
  };
}

export interface DiceState extends BaseGameState {
  type: "dice";
  rolls: {
    x?: number;
    o?: number;
  };
}

export interface NumberGuessState extends BaseGameState {
  type: "numberguess";
  targetNumber: number;
  guesses: {
    playerId: Id<"users">;
    guess: number;
    result: "higher" | "lower" | "correct";
    timestamp: number;
  }[];
  maxNumber: number;
}

export type GameState = TicTacToeState | RPSState | DiceState | NumberGuessState;

export interface GameInvite {
  _id: Id<"gameInvites">;
  _creationTime: number;
  gameType: GameType;
  fromUser: Id<"users">;
  toUser: Id<"users">;
  chatId: Id<"chats">;
  status: "pending" | "accepted" | "declined";
  expiresAt: number;
}
