import { v } from "convex/values"
import { mutation, query } from "./_generated/server"
import { Doc, Id } from "./_generated/dataModel"

// Create a notification
export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    type: v.string(),
    title: v.string(),
    message: v.string(),
    linkUrl: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    sourceId: v.optional(v.string()),
    sourceType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const notificationId = await ctx.db.insert("notifications", {
        userId: args.userId,
        type: args.type,
        title: args.title,
        message: args.message,
        linkUrl: args.linkUrl,
        imageUrl: args.imageUrl,
        sourceId: args.sourceId,
        sourceType: args.sourceType,
        isRead: false,
        createdAt: Date.now(),
      })
      
      return notificationId
    } catch (error) {
      console.error("Error creating notification:", error)
      throw error
    }
  },
})

// Get notifications for a user
export const getNotifications = query({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
    cursor: v.optional(v.id("notifications")),
  },
  handler: async (ctx, args) => {
    try {
      const { userId, limit = 20, cursor } = args
      
      const result = await ctx.db
        .query("notifications")
        .withIndex("by_user", (q) => q.eq("userId", userId))
        .order("desc")
        .paginate({
          numItems: limit,
          cursor: cursor || null,
        })
      
      return {
        notifications: result.page,
        nextCursor: result.continueCursor,
        hasMore: result.continueCursor !== null,
      }
    } catch (error) {
      console.error("Error getting notifications:", error)
      return { notifications: [], nextCursor: null }
    }
  },
})

// Mark a notification as read
export const markNotificationAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const notification = await ctx.db.get(args.notificationId)
      
      if (!notification) {
        throw new Error("Notification not found")
      }
      
      if (notification.userId !== args.userId) {
        throw new Error("Unauthorized to mark this notification as read")
      }
      
      await ctx.db.patch(args.notificationId, {
        isRead: true,
      })
      
      return true
    } catch (error) {
      console.error("Error marking notification as read:", error)
      throw error
    }
  },
})

// Mark all notifications as read
export const markAllNotificationsAsRead = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const notifications = await ctx.db
        .query("notifications")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .filter((q) => q.eq(q.field("isRead"), false))
        .collect()
      
      for (const notification of notifications) {
        await ctx.db.patch(notification._id, {
          isRead: true,
        })
      }
      
      return true
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      throw error
    }
  },
})

// Get unread notification count
export const getUnreadNotificationCount = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const notifications = await ctx.db
        .query("notifications")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .filter((q) => q.eq(q.field("isRead"), false))
        .collect()
      
      return notifications.length
    } catch (error) {
      console.error("Error getting unread notification count:", error)
      return 0
    }
  },
})

// Delete a notification
export const deleteNotification = mutation({
  args: {
    notificationId: v.id("notifications"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      const notification = await ctx.db.get(args.notificationId)
      
      if (!notification) {
        throw new Error("Notification not found")
      }
      
      if (notification.userId !== args.userId) {
        throw new Error("Unauthorized to delete this notification")
      }
      
      await ctx.db.delete(args.notificationId)
      
      return true
    } catch (error) {
      console.error("Error deleting notification:", error)
      throw error
    }
  },
})
