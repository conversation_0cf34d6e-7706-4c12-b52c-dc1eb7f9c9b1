"use client";

import { useCallback, useEffect, useState } from "react";
import { format, formatDistanceToNow } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { MoreVertical, Trash2, Copy, Check, Clock, Circle, CircleDashed, CircleOff } from "lucide-react";
import { cn } from "@/lib/utils";
import { Id } from "@/convex/_generated/dataModel";
import { Message, MessageWithSender } from "../types";
import { useOnlineUsers } from "@/hooks/use-online-users";

// Status indicator component
const StatusIndicator = ({ status, lastActive, className = "" }: { 
  status: 'online' | 'away' | 'offline', 
  lastActive?: number,
  className?: string 
}) => {
  const [timeAgo, setTimeAgo] = useState<string>('');
  
  // Update time ago text
  useEffect(() => {
    if (lastActive) {
      setTimeAgo(formatDistanceToNow(lastActive, { addSuffix: true }));
      
      // Update time ago every minute
      const interval = setInterval(() => {
        setTimeAgo(formatDistanceToNow(lastActive, { addSuffix: true }));
      }, 60000);
      
      return () => clearInterval(interval);
    }
  }, [lastActive]);
  
  const statusConfig = {
    online: {
      icon: <Circle className="h-2.5 w-2.5 fill-current text-green-500" />,
      bg: "bg-green-500",
      tooltip: "Online",
    },
    away: {
      icon: <Clock className="h-2.5 w-2.5 text-yellow-500" />,
      bg: "bg-yellow-500",
      tooltip: `Away${timeAgo ? `, active ${timeAgo}` : ''}`,
    },
    offline: {
      icon: <CircleOff className="h-2.5 w-2.5 text-gray-400" />,
      bg: "bg-gray-400",
      tooltip: `Offline${timeAgo ? `, last seen ${timeAgo}` : ''}`,
    },
  }[status];
  
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className={cn(
          "absolute -bottom-0.5 -right-0.5 flex items-center justify-center",
          "rounded-full border-2 border-background bg-background",
          className
        )}>
          {statusConfig.icon}
        </div>
      </TooltipTrigger>
      <TooltipContent side="right" className="text-xs">
        {statusConfig.tooltip}
      </TooltipContent>
    </Tooltip>
  );
};

interface MessageItemProps {
  message: Message | MessageWithSender;
  isCurrentUser: boolean;
  onDeleteAction: (messageId: Id<"messages">) => Promise<void>;
  isSelected: boolean;
  onSelect?: (id: Id<"messages"> | null) => void;
  currentUser?: {
    name?: string;
    avatar?: string;
    status?: 'online' | 'offline' | 'away';
    lastActive?: number;
  };
}

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isCurrentUser,
  onDeleteAction,
  isSelected,
  onSelect,
  currentUser,
}) => {
  const handleCopy = () => {
    navigator.clipboard.writeText(message.content);
    // You might want to show a toast notification here
  };

  // Get online status using the enhanced useOnlineUsers hook
  const { isUserOnline, updatePresence } = useOnlineUsers();

  // Update presence when the component mounts and when the message changes
  useEffect(() => {
    // Only update presence for the current user's messages
    if (isCurrentUser) {
      updatePresence().catch(console.error);
    }
  }, [isCurrentUser, updatePresence, message._id]);

  // Get sender info with real-time online status
  const getSenderInfo = useCallback(() => {
    // Handle MessageWithSender type
    if ('senderName' in message) {
      const isOnline = message.senderId ? isUserOnline(message.senderId) : false;
      return {
        name: message.senderName || 'Unknown',
        avatar: message.senderAvatar,
        status: isOnline ? 'online' : 'offline',
        id: message.senderId,
        lastActive: message.timestamp
      };
    }
    
    // Handle Message type with embedded sender
    if ('sender' in message && message.sender && typeof message.sender === 'object') {
      const sender = message.sender as Record<string, unknown>;
      const senderId = sender._id as Id<"users"> | undefined;
      const isOnline = senderId ? isUserOnline(senderId) : false;
      
      return {
        name: (sender.name as string) || 'Unknown',
        avatar: sender.avatar as string | undefined,
        status: isOnline ? 'online' : 'offline',
        id: senderId,
        lastActive: (sender.lastActive as number) || message.timestamp
      };
    }
    
    // Fallback for unknown message types
    return {
      name: 'Unknown',
      avatar: undefined,
      status: 'offline' as const,
      id: undefined,
      lastActive: Date.now()
    };
  }, [message, isUserOnline]);

  // Define the reaction type
  type Reaction = {
    emoji: string;
    userId: Id<"users">;
    userName?: string;
    type?: string;
  };

  // Get reactions in the correct format
  const getReactions = () => {
    // Handle case where reactions don't exist or are not an array
    if (!message || !('reactions' in message) || !Array.isArray(message.reactions)) {
      return [];
    }
    
    // Convert reactions to the correct format
    return message.reactions
      .filter((reaction): reaction is Reaction => {
        return typeof reaction === 'object' && 
               reaction !== null && 
               'emoji' in reaction && 
               'userId' in reaction;
      })
      .map((reaction) => ({
        emoji: reaction.emoji,
        userId: reaction.userId,
        userName: 'userName' in reaction && reaction.userName 
          ? String(reaction.userName) 
          : 'User',
      }));
  };

  const { 
    name: senderName, 
    avatar: senderAvatar, 
    status: senderStatus, 
    lastActive: senderLastActive 
  } = getSenderInfo();
  
  // Ensure status is always one of the valid values
  const validStatus = (['online', 'offline', 'away'] as const).includes(senderStatus as any) 
    ? senderStatus as 'online' | 'offline' | 'away' 
    : 'offline';
  const reactions = getReactions();

  return (
    <div 
      className={cn(
        "group relative flex items-start gap-3 p-2 rounded-lg transition-colors",
        isSelected ? "bg-accent/50" : "hover:bg-muted/50",
        isCurrentUser ? "justify-end" : "justify-start"
      )}
      onMouseEnter={() => onSelect?.(message._id)}
      onMouseLeave={() => onSelect?.(null)}
    >
      {!isCurrentUser && (
        <div className="flex-shrink-0 relative">
          <TooltipProvider>
            <div className="relative">
              <div className="relative">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={senderAvatar} alt={senderName} />
                  <AvatarFallback className="text-sm">
                    {senderName?.substring(0, 2).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                {isCurrentUser && (
                  <StatusIndicator 
                    status={validStatus} 
                    lastActive={senderLastActive}
                    className="h-3 w-3 absolute -bottom-1 -right-1 border-2 border-background"
                  />
                )}
              </div>
            </div>
          </TooltipProvider>
        </div>
      )}

      <div className={cn("flex flex-col max-w-[80%]", isCurrentUser ? "items-end" : "items-start")}>
        {!isCurrentUser && (
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium">{senderName}</span>
            <span className="text-xs text-muted-foreground">
              {format(new Date(message.timestamp), 'HH:mm')}
            </span>
          </div>
        )}

        <div className="flex items-end gap-2">
          <div
            className={cn(
              "rounded-lg px-3 py-2 text-sm relative group/message",
              isCurrentUser
                ? "bg-primary text-primary-foreground"
                : "bg-muted"
            )}
          >
            {message.content}
            
            {/* Reactions */}
            {reactions.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-1">
                {reactions.map((reaction: { emoji: string; userId: Id<"users">; userName: string }, index: number) => (
                  <div 
                    key={`${reaction.emoji}-${reaction.userId}-${message._id}-${index}`}
                    className="text-xs bg-background/50 rounded-full px-1.5 py-0.5 border"
                    title={`${reaction.userName} reacted with ${reaction.emoji}`}
                  >
                    {reaction.emoji}
                  </div>
                ))}
              </div>
            )}
            
            <div className={cn(
              "absolute -right-8 top-1/2 -translate-y-1/2 opacity-0 group-hover/message:opacity-100 transition-opacity",
              isCurrentUser ? "right-auto -left-8" : ""
            )}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreVertical className="h-3.5 w-3.5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align={isCurrentUser ? "start" : "end"}>
                  <DropdownMenuItem onClick={handleCopy}>
                    <Copy className="mr-2 h-4 w-4" />
                    <span>Copy</span>
                  </DropdownMenuItem>
                  {isCurrentUser && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDeleteAction(message._id)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      <span>Delete</span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {isCurrentUser && (
            <div className="flex-shrink-0 relative">
              <TooltipProvider>
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={currentUser?.avatar} alt={currentUser?.name} />
                    <AvatarFallback className="text-xs">
                      {currentUser?.name?.substring(0, 2).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <StatusIndicator 
                    status={currentUser?.status || 'offline'} 
                    lastActive={currentUser?.lastActive}
                    className="h-3 w-3"
                  />
                </div>
              </TooltipProvider>
            </div>
          )}
        </div>

        {isCurrentUser && (
          <div className="flex items-center gap-1 mt-1">
            <span className="text-xs text-muted-foreground">
              {format(new Date(message.timestamp), 'HH:mm')}
            </span>
            <Check className="h-3 w-3 text-muted-foreground" />
          </div>
        )}
      </div>
    </div>
  );
};
