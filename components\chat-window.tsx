"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useParams, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowLeft, Send, Video, Phone, MoreVertical, Check, MessageSquare } from "lucide-react"
import { useAuth } from "@/hooks/use-auth" // Using your actual auth hook
import { useChat } from "@/hooks/use-chats"
import { useMessages } from "@/hooks/use-messages"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"

export function ChatWindow() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const chatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId
  const { userId, isLoading: isUserLoading } = useAuth() // Using your actual auth hook

  const [newMessage, setNewMessage] = useState("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true)

  const { chat, isLoading: isChatLoading } = useChat(chatId || null)
  const { messages, sendMessage, isLoading: isMessagesLoading } = useMessages(chatId || null)

  useEffect(() => {
    if (scrollAreaRef.current && isScrolledToBottom) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages, isScrolledToBottom])

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollHeight, scrollTop, clientHeight } = event.currentTarget
    setIsScrolledToBottom(scrollHeight - scrollTop === clientHeight)
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (newMessage.trim() && chatId && userId) {
      try {
        await sendMessage(newMessage)
        setNewMessage("")
        setIsScrolledToBottom(true)
      } catch (error) {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to send message",
          variant: "destructive",
        })
      }
    }
  }

  if (isUserLoading || isChatLoading || isMessagesLoading) {
    return <LoadingSpinner />
  }

  if (!userId) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">Please sign in to continue</p>
      </div>
    )
  }

  if (!chat) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-medium">Select a conversation</h3>
          <p className="text-muted-foreground">Choose a contact from the list to start chatting.</p>
        </div>
      </div>
    )
  }

  const otherParticipant = chat.type === "direct" ? chat.participants.find((p: any) => p.id !== userId) : null

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center gap-4 p-4 border-b">
        <Button variant="ghost" size="icon" className="md:hidden" onClick={() => router.push("/chat/mobile")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Avatar className="h-10 w-10">
          {chat.type === "direct" ? (
            <>
              <AvatarImage src={otherParticipant?.avatar} alt={otherParticipant?.name} />
              <AvatarFallback>{otherParticipant?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
            </>
          ) : (
            <>
              <AvatarImage
                src={`/placeholder.svg?height=40&width=40&text=${chat.name?.substring(0, 2)}`}
                alt={chat.name}
              />
              <AvatarFallback>{chat.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
            </>
          )}
        </Avatar>
        <div className="flex-1">
          <h2 className="font-semibold">{chat.type === "direct" ? otherParticipant?.name : chat.name}</h2>
          <p className="text-sm text-muted-foreground">
            {chat.type === "direct"
              ? otherParticipant?.status === "online"
                ? "Online"
                : "Offline"
              : `${chat.participants.length} members`}
          </p>
        </div>
        {chat.type === "direct" && (
          <>
            <Button variant="ghost" size="icon">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Video className="h-4 w-4" />
            </Button>
          </>
        )}
        <Button variant="ghost" size="icon">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4" onScroll={handleScroll} ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.length === 0 ? (
            <p className="text-center text-muted-foreground">No messages yet. Start the conversation!</p>
          ) : (
            messages.map((message) => {
              // Ensure each message has a unique key
              const messageKey = message._id || `msg-${message.timestamp}-${Math.random()}`
              const isCurrentUser = message.senderId === userId
              const time = format(new Date(message.timestamp), "h:mm a")

              return (
                <div key={messageKey} className={`flex items-end gap-2 ${isCurrentUser ? "flex-row-reverse" : ""}`}>
                  {!isCurrentUser && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
                      <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  )}
                  <div className="max-w-[70%]">
                    <div
                      className={`rounded-2xl px-3 py-2 ${
                        isCurrentUser ? "bg-primary text-primary-foreground" : "bg-muted"
                      }`}
                    >
                      <p>{message.content}</p>
                    </div>
                    <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
                      <span className="text-muted-foreground">{time}</span>
                      {isCurrentUser && (
                        <div className="flex text-primary">
                          <Check className="h-3 w-3" />
                          <Check className="h-3 w-3 -ml-1" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </ScrollArea>

      <div className="p-4 border-t">
        <form onSubmit={handleSendMessage} className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={!newMessage.trim()} className="rounded-full bg-primary">
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  )
}

