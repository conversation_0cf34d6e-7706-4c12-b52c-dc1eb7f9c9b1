"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useUserPresence } from "@/hooks/use-presence"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowLeft, Send, Video, Phone, MoreVertical, Check, MessageSquare } from "lucide-react"
import { ChatInput } from "@/components/chat/chat-input"
import { useAuth } from "@/hooks/use-auth" // Using your actual auth hook
import { useChat } from "@/hooks/use-chats"
import { useMessages } from "@/hooks/use-messages"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"
import { Id } from "@/convex/_generated/dataModel"

function formatLastSeen(lastSeen: Date | null): string {
  if (!lastSeen) return 'Never';
  
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays === 1) return 'Yesterday';
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return lastSeen.toLocaleDateString();
}

function UserStatus({ userId }: { userId: string }) {
  const { status, lastSeen } = useUserPresence(userId);
  
  return (
    <div className="flex flex-col">
      <span className="flex items-center gap-1 text-xs text-muted-foreground">
        <span 
          className={`h-2 w-2 rounded-full ${status === 'online' ? 'bg-green-500' : 'bg-gray-400'}`} 
          title={status === 'online' ? 'Online' : 'Offline'}
        />
        {status === 'online' ? 'Online' : 'Offline'}
      </span>
      {status !== 'online' && lastSeen && (
        <span className="text-xs text-muted-foreground/70">
          Last seen {formatLastSeen(lastSeen)}
        </span>
      )}
    </div>
  );
}

export function ChatWindow() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  // Ensure chatId is properly typed as Id<"chats">
  const chatId = (Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId) as Id <"chats"> | undefined 
  const { userId, isLoading: isUserLoading } = useAuth() // Using your actual auth hook

  const [newMessage, setNewMessage] = useState("")
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const isAutoScrollEnabled = useRef(true)

  const { chat, isLoading: isChatLoading } = useChat(chatId || null)
  const { messages, sendMessage, isLoading: isMessagesLoading } = useMessages(chatId || null, () => {
    // This callback runs when a new message is received
    if (isAutoScrollEnabled.current && scrollAreaRef.current) {
      scrollToBottom()
    }
  })

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'smooth'
      })
    }
  }

  // Auto-scroll when messages change
  useEffect(() => {
    if (isAutoScrollEnabled.current) {
      scrollToBottom()
    }
  }, [messages])

  // Scroll to bottom when chat changes
  useEffect(() => {
    isAutoScrollEnabled.current = true
    scrollToBottom()
  }, [chatId])

  const handleScroll = () => {
    if (!scrollAreaRef.current) return
    
    const { scrollTop, scrollHeight, clientHeight } = scrollAreaRef.current
    const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 10
    isAutoScrollEnabled.current = isAtBottom
  }



  const handleSendMessage = async (message: string) => {
    if (message.trim() && chatId && userId) {
      try {
        await sendMessage(message)
        scrollToBottom()
      } catch (error) {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to send message",
          variant: "destructive",
        })
      }
    }
  }

  if (isUserLoading || isChatLoading || isMessagesLoading) {
    return <LoadingSpinner />
  }

  if (!userId) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">Please sign in to continue</p>
      </div>
    )
  }

  if (!chat) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-medium">Select a conversation</h3>
          <p className="text-muted-foreground">Choose a contact from the list to start chatting.</p>
        </div>
      </div>
    )
  }

  const otherParticipant = chat.type === "direct" ? chat.participants.find((p: any) => p.id !== userId) : null

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center gap-4 p-4 border-b">
        <Button variant="ghost" size="icon" className="md:hidden" onClick={() => router.push("/chat/mobile")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Avatar className="h-10 w-10">
          {chat.type === "direct" ? (
            <>
              <AvatarImage src={otherParticipant?.avatar} alt={otherParticipant?.name} />
              <AvatarFallback>{otherParticipant?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
            </>
          ) : (
            <>
              <AvatarImage
                src={`/placeholder.svg?height=40&width=40&text=${chat.name?.substring(0, 2)}`}
                alt={chat.name}
              />
              <AvatarFallback>{chat.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
            </>
          )}
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h2 className="font-semibold">{chat.type === "direct" ? otherParticipant?.name : chat.name}</h2>
            {chat.type === "direct" && otherParticipant && (
              <UserStatus userId={otherParticipant._id} />
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            {chat.type === "direct"
              ? otherParticipant?.status === "online"
                ? "Online"
                : "Offline"
              : `${chat.participants.length} members`}
          </p>
        </div>
        {chat.type === "direct" && (
          <>
            <Button variant="ghost" size="icon">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <Video className="h-4 w-4" />
            </Button>
          </>
        )}
        <Button variant="ghost" size="icon">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea 
        className="flex-1 p-4" 
        onScroll={handleScroll}
        ref={scrollAreaRef}
      >
        <div className="space-y-4">
          <div className="min-h-[1px]" ref={messagesEndRef} />
          {messages.length === 0 ? (
            <p className="text-center text-muted-foreground">No messages yet. Start the conversation!</p>
          ) : (
            messages.map((message) => {
              // Ensure each message has a unique key
              const messageKey = message._id || `msg-${message.timestamp}-${Math.random()}`
              const isCurrentUser = message.senderId === userId
              const time = format(new Date(message.timestamp), "h:mm a")

              return (
                <div key={messageKey} className={`flex items-end gap-2 ${isCurrentUser ? "flex-row-reverse" : ""}`}>
                  {!isCurrentUser && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={message.sender?.avatar} alt={message.sender?.name} />
                      <AvatarFallback>{message.sender?.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  )}
                  <div className="max-w-[70%]">
                    <div
                      className={`rounded-2xl px-3 py-2 ${
                        isCurrentUser ? "bg-primary text-primary-foreground" : "bg-muted"
                      }`}
                    >
                      <p>{message.content}</p>
                    </div>
                    <div className={`mt-1 flex items-center gap-1 text-xs ${isCurrentUser ? "justify-end" : ""}`}>
                      <span className="text-muted-foreground">{time}</span>
                      {isCurrentUser && (
                        <div className="flex text-primary">
                          <Check className="h-3 w-3" />
                          <Check className="h-3 w-3 -ml-1" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })
          )}
          <div className="min-h-[1px]" ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Only render ChatInput if we have a valid chatId */}
      {chatId && (
        <div className="p-4 border-t">
          <ChatInput 
            chatId={chatId}
            onSendMessage={handleSendMessage}
            onStartRecording={chat.type === 'direct' ? () => {} : undefined}
          />
        </div>
      )}
    </div>
  )
}

