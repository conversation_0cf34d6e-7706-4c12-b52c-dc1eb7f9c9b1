/* Mxit Classic Theme - Authentic look and feel */

:root {
  /* Mxit Classic Colors */
  --mxit-blue: #1e3a8a;
  --mxit-light-blue: #3b82f6;
  --mxit-dark-blue: #1e40af;
  --mxit-orange: #f97316;
  --mxit-yellow: #fbbf24;
  --mxit-green: #10b981;
  --mxit-red: #ef4444;
  --mxit-purple: #8b5cf6;
  
  /* Chat bubble colors */
  --mxit-sent-bubble: #1e40af;
  --mxit-received-bubble: #f1f5f9;
  --mxit-system-bubble: #fef3c7;
  
  /* Background gradients */
  --mxit-bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 50%, #7dd3fc 100%);
  --mxit-header-gradient: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
  
  /* Shadows */
  --mxit-shadow: 0 2px 8px rgba(30, 64, 175, 0.15);
  --mxit-bubble-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Global Mxit styling */
.mxit-theme {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: var(--mxit-bg-gradient);
  min-height: 100vh;
}

/* Mxit-style chat bubbles */
.mxit-bubble {
  border-radius: 18px;
  padding: 8px 12px;
  margin: 2px 0;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: var(--mxit-bubble-shadow);
  position: relative;
}

.mxit-bubble-sent {
  background: var(--mxit-sent-bubble);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.mxit-bubble-received {
  background: var(--mxit-received-bubble);
  color: #1f2937;
  margin-right: auto;
  border-bottom-left-radius: 4px;
  border: 1px solid #e2e8f0;
}

.mxit-bubble-system {
  background: var(--mxit-system-bubble);
  color: #92400e;
  margin: 0 auto;
  text-align: center;
  font-size: 0.875rem;
  border-radius: 12px;
}

/* Mxit-style header */
.mxit-header {
  background: var(--mxit-header-gradient);
  color: white;
  box-shadow: var(--mxit-shadow);
  border-bottom: 2px solid var(--mxit-dark-blue);
}

/* Mxit-style navigation */
.mxit-nav {
  background: white;
  border-top: 2px solid var(--mxit-light-blue);
  box-shadow: 0 -2px 8px rgba(30, 64, 175, 0.1);
}

.mxit-nav-item {
  color: var(--mxit-blue);
  transition: all 0.2s ease;
}

.mxit-nav-item:hover {
  color: var(--mxit-light-blue);
  transform: translateY(-1px);
}

.mxit-nav-item.active {
  color: var(--mxit-light-blue);
  background: rgba(59, 130, 246, 0.1);
}

/* Mxit-style buttons */
.mxit-button {
  background: var(--mxit-light-blue);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: var(--mxit-shadow);
}

.mxit-button:hover {
  background: var(--mxit-dark-blue);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.2);
}

.mxit-button-secondary {
  background: white;
  color: var(--mxit-blue);
  border: 2px solid var(--mxit-light-blue);
}

.mxit-button-secondary:hover {
  background: var(--mxit-light-blue);
  color: white;
}

/* Mxit-style input */
.mxit-input {
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  padding: 8px 16px;
  transition: all 0.2s ease;
  background: white;
}

.mxit-input:focus {
  border-color: var(--mxit-light-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Mxit-style cards */
.mxit-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--mxit-shadow);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.mxit-card-header {
  background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 16px;
}

/* Mxit-style avatars */
.mxit-avatar {
  border: 3px solid white;
  box-shadow: var(--mxit-shadow);
}

/* Mxit-style badges */
.mxit-badge {
  background: var(--mxit-orange);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

.mxit-badge-green {
  background: var(--mxit-green);
}

.mxit-badge-red {
  background: var(--mxit-red);
}

/* Mxit-style status indicators */
.mxit-status-online {
  color: var(--mxit-green);
}

.mxit-status-away {
  color: var(--mxit-yellow);
}

.mxit-status-offline {
  color: #6b7280;
}

/* Mxit-style animations */
.mxit-bounce {
  animation: mxitBounce 0.6s ease-in-out;
}

@keyframes mxitBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.mxit-fade-in {
  animation: mxitFadeIn 0.3s ease-in-out;
}

@keyframes mxitFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mxit-style scrollbar */
.mxit-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.mxit-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.mxit-scrollbar::-webkit-scrollbar-thumb {
  background: var(--mxit-light-blue);
  border-radius: 3px;
}

.mxit-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--mxit-dark-blue);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mxit-bubble {
    max-width: 90%;
    font-size: 0.9rem;
  }
  
  .mxit-header {
    padding: 8px 16px;
  }
  
  .mxit-nav {
    padding: 8px 0;
  }
}
