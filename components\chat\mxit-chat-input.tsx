"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { 
  Send, 
  Smile, 
  Mic, 
  Image, 
  Gift,
  Zap,
  Palette
} from "lucide-react"
import { SAEmojiPicker } from "./sa-emoji-picker"
import { MoolaIcon } from "@/components/moola-icon"

interface MxitChatInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  onNudge?: () => void
  onGiftMoola?: () => void
  onMultiMix?: () => void
  onVoiceNote?: () => void
  onImageUpload?: () => void
  disabled?: boolean
  placeholder?: string
  moolaBalance?: number
  isRecording?: boolean
}

export function MxitChatInput({
  value,
  onChange,
  onSend,
  onNudge,
  onGiftMoola,
  onMultiMix,
  onVoiceNote,
  onImageUpload,
  disabled = false,
  placeholder = "Type a message...",
  moolaBalance = 0,
  isRecording = false
}: MxitChatInputProps) {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showActions, setShowActions] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleSend = () => {
    if (value.trim() && !disabled) {
      onSend()
      setShowEmojiPicker(false)
      setShowActions(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    const newValue = value + emoji
    onChange(newValue)
    inputRef.current?.focus()
  }

  const canSend = value.trim().length > 0 && !disabled

  return (
    <div className="relative">
      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute bottom-full left-0 right-0 mb-2 z-50">
          <SAEmojiPicker 
            onEmojiSelect={handleEmojiSelect}
            className="mx-auto"
          />
        </div>
      )}

      {/* Action Menu */}
      {showActions && (
        <div className="absolute bottom-full left-0 right-0 mb-2 z-40">
          <div className="bg-white border border-blue-200 rounded-lg shadow-lg p-3 mx-3 mxit-fade-in">
            <div className="grid grid-cols-4 gap-2">
              {/* Nudge */}
              {onNudge && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  onClick={() => {
                    onNudge()
                    setShowActions(false)
                  }}
                >
                  <Zap className="h-5 w-5 text-orange-500" />
                  <span className="text-xs">Nudge</span>
                </Button>
              )}

              {/* Gift Moola */}
              {onGiftMoola && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  onClick={() => {
                    onGiftMoola()
                    setShowActions(false)
                  }}
                >
                  <Gift className="h-5 w-5 text-yellow-500" />
                  <span className="text-xs">Gift</span>
                </Button>
              )}

              {/* MultiMix */}
              {onMultiMix && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  onClick={() => {
                    onMultiMix()
                    setShowActions(false)
                  }}
                >
                  <Palette className="h-5 w-5 text-purple-500" />
                  <span className="text-xs">MultiMix</span>
                </Button>
              )}

              {/* Image Upload */}
              {onImageUpload && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-auto py-2"
                  onClick={() => {
                    onImageUpload()
                    setShowActions(false)
                  }}
                >
                  <Image className="h-5 w-5 text-blue-500" />
                  <span className="text-xs">Image</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Input Area */}
      <div className="flex items-center gap-2 p-3 bg-white border-t border-blue-200">
        {/* Action Button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 text-blue-600 hover:bg-blue-50"
          onClick={() => setShowActions(!showActions)}
        >
          <span className="text-lg">+</span>
        </Button>

        {/* Text Input */}
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "mxit-input pr-10",
              disabled && "opacity-50"
            )}
          />
          
          {/* Emoji Button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 text-blue-600 hover:bg-blue-50"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        {/* Voice Note Button */}
        {onVoiceNote && (
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-10 w-10 hover:bg-blue-50",
              isRecording ? "text-red-500 animate-pulse" : "text-blue-600"
            )}
            onClick={onVoiceNote}
          >
            <Mic className="h-5 w-5" />
          </Button>
        )}

        {/* Send Button */}
        <Button
          onClick={handleSend}
          disabled={!canSend}
          className={cn(
            "h-10 w-10 p-0 mxit-button",
            canSend 
              ? "bg-blue-600 hover:bg-blue-700" 
              : "bg-gray-300 cursor-not-allowed"
          )}
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Moola Balance Indicator */}
      {moolaBalance !== undefined && (
        <div className="absolute top-0 right-3 transform -translate-y-full">
          <div className="bg-yellow-100 border border-yellow-300 rounded-t-lg px-2 py-1 flex items-center gap-1">
            <MoolaIcon className="h-3 w-3 text-yellow-600" />
            <span className="text-xs font-medium text-yellow-700">
              {moolaBalance.toFixed(1)}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
