import { ReactNode } from "react"
export type UserStatus = "online" | "offline" | "away"
export type UserMood = "happy" | "sad" | "excited" | "bored" | "busy" | "relaxed"
export type ChatType = "direct" | "group" | "chatroom"
export type ChatroomCategory = "flirt" | "teen" | "grown-up" | "kingdom" | "topical" | "geographical"
export type MoolaTransactionType =
  | "purchase"
  | "data_usage"
  | "tradepost_purchase"
  | "tradepost_sale"
  | "bonus"
  | "refund"
  | "gift_sent"
  | "gift_received"
  | "data_usage"
  | "mobile"
  | "moola"
export type ParticipantRole = "admin" | "member"

export interface User {
  id: string
  name: string
  email?: string
  avatar: string
  status: UserStatus
  lastSeen?: number
  statusMessage?: string
  mood?: UserMood
  theme?: string
  moola: number
  clerkId: string
  createdAt: number
  updatedAt: number
}

export interface Chat {
  id: string
  type: ChatType
  name?: string
  description?: string
  background?: string
  isPublic: boolean
  creatorId: string
  lastMessageId?: string
  createdAt: number
  updatedAt: number
  category?: ChatroomCategory
  moolaPerMessage?: number
  location?: string
  tags?: string[]
  isPrivate?: boolean
  thumbnailUrl?: string
}

export interface ChatWithDetails extends Chat {
  participants: User[]
  lastMessage?: Message & { sender: User }
  unreadCount: number
  memberCount?: number
  creator?: User
}

export interface ChatParticipant {
  id: string
  chatId: string
  userId: string
  unreadCount: number
  joinedAt: number
  role: ParticipantRole
  lastReadMessageId?: string
}

export interface Message {
  _id: string
  tempId: any
  isVoiceNote: any
  voiceNoteDuration: ReactNode
  voiceNoteUrl: string | undefined
  multimixStyle: any
  id: string
  chatId: string
  senderId: string
  content: string
  timestamp: number
  isRead: boolean
  isNudge: boolean
  isMultimix: boolean
  replyTo?: string
  reactions: MessageReaction[]
  moolaCost?: number
  sender?: User
}

export interface MessageReaction {
  userId: string
  emoji: string
}

export interface MoolaTransaction {
  id: string
  userId: string
  amount: number
  type: MoolaTransactionType
  description: string
  timestamp: number
}

export interface MultimixStyle {
  fontSize?: string; // Make fontSize optional
  // Add other potential style properties here
  [key: string]: any; // Allow other properties (be careful with this)
}