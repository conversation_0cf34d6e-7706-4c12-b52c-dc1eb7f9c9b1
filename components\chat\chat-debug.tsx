"use client"

import { useParams } from "next/navigation"
import { useAuth } from "@/hooks/use-auth"
import { useChat } from "@/hooks/use-chat"
import { useMessages } from "@/hooks/use-messages"

export function ChatDebug() {
  const params = useParams()
  const chatId = Array.isArray(params?.chatId) ? params.chatId[0] : params?.chatId
  const { userId, isLoading: isUserLoading } = useAuth()
  const { chat, isLoading: isChatLoading } = useChat(chatId || null)
  const { messages, isLoading: isMessagesLoading } = useMessages(chatId || null)

  return (
    <div className="fixed top-0 left-0 z-50 bg-gray-900 text-white p-2 text-xs opacity-80">
      <h3 className="font-bold">Debug Info:</h3>
      
      
      <div>chatId: {chatId || "none"}
      </div>
      <div>userId: {userId || "none"}
      </div>
      <div>isUserLoading: {isUserLoading.toString()}
      </div>
      <div>isChatLoading: {isChatLoading.toString()}
      </div>
      <div>isMessagesLoading: {isMessagesLoading?.toString() || "null"}
      </div>
      <div>chat: {chat ? "exists" : "null"}
      </div>
      <div>messages: {messages?.length || 0}
      </div>
    </div>
  )
}

